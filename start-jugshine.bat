@echo off
title Jugshine Simple Launcher
color 0A

echo.
echo ========================================
echo    JUGSHINE SIMPLE LAUNCHER
echo ========================================
echo.

:: Set error handling
setlocal enabledelayedexpansion

:: Check current directory
echo [INFO] Current directory: %CD%
echo.

:: Check if we're in the right directory
if not exist "server" (
    echo [ERROR] 'server' directory not found!
    echo Make sure you're running this from the Jugshine root directory.
    echo.
    pause
    exit /b 1
)

if not exist "webclient" (
    echo [ERROR] 'webclient' directory not found!
    echo Make sure you're running this from the Jugshine root directory.
    echo.
    pause
    exit /b 1
)

echo [INFO] Directory structure looks good!
echo.

:: Simple Node.js check
echo [INFO] Checking Node.js...
where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found in PATH
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: Get Node.js version
for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
echo [INFO] Node.js version: %NODE_VERSION%

:: Simple npm check
echo [INFO] Checking npm...
where npm >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm not found in PATH
    echo npm should come with Node.js installation.
    echo.
    pause
    exit /b 1
)

:: Get npm version
for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
echo [INFO] npm version: %NPM_VERSION%
echo.

:: Create server .env if it doesn't exist
if not exist "server\.env" (
    echo [INFO] Creating server .env file...
    if exist "server\.env.example" (
        copy "server\.env.example" "server\.env" >nul
        echo [INFO] Created server/.env from example
    ) else (
        echo [WARNING] server/.env.example not found, creating basic .env
        echo PORT=3000> server\.env
        echo MONGODB_URI=mongodb://localhost:27017/jugshine>> server\.env
    )
    echo.
)

:: Install server dependencies
echo [INFO] Checking server dependencies...
if not exist "server\node_modules" (
    echo [INFO] Installing server dependencies...
    cd server
    call npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install server dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [INFO] Server dependencies installed
) else (
    echo [INFO] Server dependencies already installed
)
echo.

:: Install web client dependencies
echo [INFO] Checking web client dependencies...
if not exist "webclient\node_modules" (
    echo [INFO] Installing web client dependencies...
    cd webclient
    call npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install web client dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [INFO] Web client dependencies installed
) else (
    echo [INFO] Web client dependencies already installed
)
echo.

:: Check if MongoDB is running
echo [INFO] Checking MongoDB connection...
cd server
node -e "const mongoose = require('mongoose'); mongoose.connect('mongodb://localhost:27017/jugshine', {serverSelectionTimeoutMS: 5000}).then(() => {console.log('MongoDB is running'); process.exit(0);}).catch(err => {console.error('MongoDB not available:', err.message); process.exit(1);})" 2>nul
if errorlevel 1 (
    echo [ERROR] MongoDB is not running!
    echo [INFO] Please start MongoDB first using one of these methods:
    echo   1. Run: start-server-mongodb.bat
    echo   2. Or manually start MongoDB service
    echo   3. Or run: mongod --dbpath ./mongodb-data
    cd ..
    echo.
    pause
    exit /b 1
)
cd ..
echo [INFO] MongoDB is running successfully
echo.

:: Seed database
echo [INFO] Seeding database with game content...
cd server
call node scripts/seedContent.js
if errorlevel 1 (
    echo [WARNING] Database seeding failed
    echo [INFO] The server might still work, but game content could be missing
) else (
    echo [INFO] Database seeded successfully
)
cd ..
echo.

echo ========================================
echo    STARTING SERVICES
echo ========================================
echo.

:: Start server
echo [INFO] Starting server...
start "Jugshine Server" cmd /k "cd /d %CD%\server && npm run dev"
echo [INFO] Server starting in new window...

:: Wait a bit
timeout /t 3 /nobreak >nul

:: Start web client
echo [INFO] Starting web client...
start "Jugshine Web Client" cmd /k "cd /d %CD%\webclient && npx ionic serve --port=8100"
echo [INFO] Web client starting in new window...

:: Wait a bit more
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo    SERVICES STARTED!
echo ========================================
echo.
echo [SUCCESS] Jugshine is starting up!
echo.
echo Services:
echo   Server:     http://localhost:3000
echo   Web Client: http://localhost:8100
echo.
echo Next steps:
echo   1. Wait for both services to start (check the new windows)
echo   2. Open Godot and run the Jugshine project
echo   3. Start a game and note the room code
echo   4. Open http://localhost:8100 on your phone/browser
echo   5. Enter room code and play!
echo.
echo Note: This launcher requires MongoDB to be running.
echo If you need to start MongoDB, use: start-server-mongodb.bat
echo.
echo Opening web client in browser...
timeout /t 2 /nobreak >nul
start http://localhost:8100

echo.
echo [INFO] Launcher finished. Check the server and web client windows.
echo [INFO] Press any key to exit this window.
pause >nul
