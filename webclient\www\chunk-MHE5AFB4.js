import{a as at}from"./chunk-QUJFQN2Y.js";import{f as Lt,g as Ot}from"./chunk-XC7QA3KA.js";import{c as q,e as Yt,f as tt,i as It,j as rt,k as Rt}from"./chunk-NLLZB5D2.js";import{a as dt}from"./chunk-ILQCMFU7.js";import"./chunk-XCHF2ADM.js";import{g as At,h as Dt,j as Ct,k as Et,l as st,o as Bt,p as F,q as Tt,s as Mt,t as X}from"./chunk-WDNHH3UA.js";import{a as wt}from"./chunk-VGNGVKU7.js";import{a as xt,b as vt,c as St}from"./chunk-WKAFHTGB.js";import{c as Pt}from"./chunk-55XXYEDJ.js";import{a as f}from"./chunk-PZCS62C4.js";import{a as kt}from"./chunk-FPOZYJOD.js";import{c as bt,d as yt,h as Y,i as z,m as j}from"./chunk-OAHYJD5P.js";import{a as it}from"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-E2SD7K6D.js";import{a as V}from"./chunk-CKP3SGE2.js";import{a as lt,b as et,f as U,i as _t,k as ct,m as H,n as Nt,o as Ht,p as I}from"./chunk-7IRWVP2E.js";import{f as P}from"./chunk-S5EYVFES.js";var Z=function(t){return t.Dark="DARK",t.Light="LIGHT",t.Default="DEFAULT",t}(Z||{}),ft={getEngine(){let t=kt();if(t?.isPluginAvailable("StatusBar"))return t.Plugins.StatusBar},setStyle(t){let e=this.getEngine();e&&e.setStyle(t)},getStyle:function(){return P(this,null,function*(){let t=this.getEngine();if(!t)return Z.Default;let{style:e}=yield t.getInfo();return e})}},pt=(t,e)=>{if(e===1)return 0;let n=1/(1-e),s=-(e*n);return t*n+s},Gt=()=>{!V||V.innerWidth>=768||ft.setStyle({style:Z.Dark})},ht=(t=Z.Default)=>{!V||V.innerWidth>=768||ft.setStyle({style:t})},Kt=(t,e)=>P(null,null,function*(){typeof t.canDismiss!="function"||!(yield t.canDismiss(void 0,F))||(e.isRunning()?e.onFinish(()=>{t.dismiss(void 0,"handler")},{oneTimeCallback:!0}):t.dismiss(void 0,"handler"))}),mt=t=>.00255275*2.71828**(-14.9619*t)-1.00255*2.71828**(-.0380968*t)+1,nt={MIN_PRESENTING_SCALE:.915},Jt=(t,e,n,s)=>{let o=t.offsetHeight,a=!1,i=!1,d=null,g=null,D=.2,S=!0,b=0,y=()=>d&&q(d)?d.scrollY:!0,L=it({el:t,gestureName:"modalSwipeToClose",gesturePriority:Tt,direction:"y",threshold:10,canStart:x=>{let u=x.event.target;return u===null||!u.closest?!0:(d=tt(u),d?(q(d)?g=Y(d).querySelector(".inner-scroll"):g=d,!!!d.querySelector("ion-refresher")&&g.scrollTop===0):u.closest("ion-footer")===null)},onStart:x=>{let{deltaY:u}=x;S=y(),i=t.canDismiss!==void 0&&t.canDismiss!==!0,u>0&&d&&rt(d),e.progressStart(!0,a?1:0)},onMove:x=>{let{deltaY:u}=x;u>0&&d&&rt(d);let v=x.deltaY/o,C=v>=0&&i,O=C?D:.9999,W=C?mt(v/O):v,T=j(1e-4,W,O);e.progressStep(T),T>=.5&&b<.5?ht(n):T<.5&&b>=.5&&Gt(),b=T},onEnd:x=>{let u=x.velocityY,v=x.deltaY/o,C=v>=0&&i,O=C?D:.9999,W=C?mt(v/O):v,T=j(1e-4,W,O),_=(x.deltaY+u*1e3)/o,A=!C&&_>=.5,B=A?-.001:.001;A?(e.easing("cubic-bezier(0.32, 0.72, 0, 1)"),B+=at([0,0],[.32,.72],[0,1],[1,1],T)[0]):(e.easing("cubic-bezier(1, 0, 0.68, 0.28)"),B+=at([0,0],[1,0],[.68,.28],[1,1],T)[0]);let J=$t(A?v*o:(1-T)*o,u);a=A,L.enable(!1),d&&Rt(d,S),e.onFinish(()=>{A||L.enable(!0)}).progressEnd(A?1:0,B,J),C&&T>O/4?Kt(t,e):A&&s()}});return L},$t=(t,e)=>j(400,t/Math.abs(e*1.1),500),zt=t=>{let{currentBreakpoint:e,backdropBreakpoint:n,expandToScroll:s}=t,r=n===void 0||n<e,o=r?`calc(var(--backdrop-opacity) * ${e})`:"0",a=f("backdropAnimation").fromTo("opacity",0,o);r&&a.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let i=f("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-e*100}%)`}]),d=s?void 0:f("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-e)*100}%`},{offset:1,opacity:1,maxHeight:`${e*100}%`}]);return{wrapperAnimation:i,backdropAnimation:a,contentAnimation:d}},jt=t=>{let{currentBreakpoint:e,backdropBreakpoint:n}=t,s=`calc(var(--backdrop-opacity) * ${pt(e,n)})`,r=[{offset:0,opacity:s},{offset:1,opacity:0}],o=[{offset:0,opacity:s},{offset:n,opacity:0},{offset:1,opacity:0}],a=f("backdropAnimation").keyframes(n!==0?o:r);return{wrapperAnimation:f("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-e*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:a}},Qt=()=>{let t=f().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=f().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:t,wrapperAnimation:e,contentAnimation:void 0}},qt=(t,e)=>{let{presentingEl:n,currentBreakpoint:s,expandToScroll:r}=e,o=Y(t),{wrapperAnimation:a,backdropAnimation:i,contentAnimation:d}=s!==void 0?zt(e):Qt();i.addElement(o.querySelector("ion-backdrop")),a.addElement(o.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!r&&d?.addElement(t.querySelector(".ion-page"));let g=f("entering-base").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([a]);if(d&&g.addAnimation(d),n){let D=window.innerWidth<768,S=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,b=Y(n),y=f().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),k=document.body;if(D){let E=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",p=S?"-10px":E,R=nt.MIN_PRESENTING_SCALE,L=`translateY(${p}) scale(${R})`;y.afterStyles({transform:L}).beforeAddWrite(()=>k.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:L,borderRadius:"10px 10px 0 0"}]),g.addAnimation(y)}else if(g.addAnimation(i),!S)a.fromTo("opacity","0","1");else{let p=`translateY(-10px) scale(${S?nt.MIN_PRESENTING_SCALE:1})`;y.afterStyles({transform:p}).addElement(b.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:p}]);let R=f().afterStyles({transform:p}).addElement(b.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:p}]);g.addAnimation([y,R])}}else g.addAnimation(i);return g},Xt=()=>{let t=f().fromTo("opacity","var(--backdrop-opacity)",0),e=f().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:t,wrapperAnimation:e}},Wt=(t,e,n=500)=>{let{presentingEl:s,currentBreakpoint:r}=e,o=Y(t),{wrapperAnimation:a,backdropAnimation:i}=r!==void 0?jt(e):Xt();i.addElement(o.querySelector("ion-backdrop")),a.addElement(o.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let d=f("leaving-base").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(a);if(s){let g=window.innerWidth<768,D=s.tagName==="ION-MODAL"&&s.presentingElement!==void 0,S=Y(s),b=f().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(k=>{if(k!==1)return;s.style.setProperty("overflow",""),Array.from(y.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(p=>p.presentingElement!==void 0).length<=1&&y.style.setProperty("background-color","")}),y=document.body;if(g){let k=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",E=D?"-10px":k,p=nt.MIN_PRESENTING_SCALE,R=`translateY(${E}) scale(${p})`;b.addElement(s).keyframes([{offset:0,filter:"contrast(0.85)",transform:R,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),d.addAnimation(b)}else if(d.addAnimation(i),!D)a.fromTo("opacity","1","0");else{let E=`translateY(-10px) scale(${D?nt.MIN_PRESENTING_SCALE:1})`;b.addElement(S.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:E},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let p=f().addElement(S.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:E},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);d.addAnimation([b,p])}}else d.addAnimation(i);return d},te=()=>{let t=f().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=f().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:t,wrapperAnimation:e,contentAnimation:void 0}},ee=(t,e)=>{let{currentBreakpoint:n,expandToScroll:s}=e,r=Y(t),{wrapperAnimation:o,backdropAnimation:a,contentAnimation:i}=n!==void 0?zt(e):te();a.addElement(r.querySelector("ion-backdrop")),o.addElement(r.querySelector(".modal-wrapper")),!s&&i?.addElement(t.querySelector(".ion-page"));let d=f().addElement(t).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([a,o]);return i&&d.addAnimation(i),d},ne=()=>{let t=f().fromTo("opacity","var(--backdrop-opacity)",0),e=f().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:t,wrapperAnimation:e}},oe=(t,e)=>{let{currentBreakpoint:n}=e,s=Y(t),{wrapperAnimation:r,backdropAnimation:o}=n!==void 0?jt(e):ne();return o.addElement(s.querySelector("ion-backdrop")),r.addElement(s.querySelector(".modal-wrapper")),f().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([o,r])},ie=(t,e,n,s,r,o,a=[],i,d,g,D)=>{let S=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],b=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-r,opacity:0},{offset:1,opacity:0}],y={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:r!==0?b:S,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},k=t.querySelector("ion-content"),E=n.clientHeight,p=s,R=0,L=!1,x=null,u=null,v=null,C=null,O=.95,W=a[a.length-1],T=a[0],_=o.childAnimations.find(h=>h.id==="wrapperAnimation"),A=o.childAnimations.find(h=>h.id==="backdropAnimation"),B=o.childAnimations.find(h=>h.id==="contentAnimation"),J=()=>{t.style.setProperty("pointer-events","auto"),e.style.setProperty("pointer-events","auto"),t.classList.remove(X)},ut=()=>{t.style.setProperty("pointer-events","none"),e.style.setProperty("pointer-events","none"),t.classList.add(X)},$=h=>{if(!u&&(u=Array.from(t.querySelectorAll("ion-footer")),!u.length))return;let l=t.querySelector(".ion-page");if(C=h,h==="stationary")u.forEach(m=>{m.classList.remove("modal-footer-moving"),m.style.removeProperty("position"),m.style.removeProperty("width"),m.style.removeProperty("height"),m.style.removeProperty("top"),m.style.removeProperty("left"),l?.style.removeProperty("padding-bottom"),l?.appendChild(m)});else{let m=0;u.forEach((c,N)=>{let M=c.getBoundingClientRect(),w=document.body.getBoundingClientRect();m+=c.clientHeight;let G=M.top-w.top,K=M.left-w.left;if(c.style.setProperty("--pinned-width",`${c.clientWidth}px`),c.style.setProperty("--pinned-height",`${c.clientHeight}px`),c.style.setProperty("--pinned-top",`${G}px`),c.style.setProperty("--pinned-left",`${K}px`),N===0){v=G;let ot=t.querySelector("ion-header");ot&&(v-=ot.clientHeight)}}),u.forEach(c=>{l?.style.setProperty("padding-bottom",`${m}px`),c.classList.add("modal-footer-moving"),c.style.setProperty("position","absolute"),c.style.setProperty("width","var(--pinned-width)"),c.style.setProperty("height","var(--pinned-height)"),c.style.setProperty("top","var(--pinned-top)"),c.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(c)})}};_&&A&&(_.keyframes([...y.WRAPPER_KEYFRAMES]),A.keyframes([...y.BACKDROP_KEYFRAMES]),B?.keyframes([...y.CONTENT_KEYFRAMES]),o.progressStart(!0,1-p),p>r?J():ut()),k&&p!==W&&i&&(k.scrollY=!1);let Vt=h=>{let l=tt(h.event.target);if(p=d(),!i&&l)return(q(l)?Y(l).querySelector(".inner-scroll"):l).scrollTop===0;if(p===1&&l){let m=q(l)?Y(l).querySelector(".inner-scroll"):l;return!!!l.querySelector("ion-refresher")&&m.scrollTop===0}return!0},Ft=h=>{if(L=t.canDismiss!==void 0&&t.canDismiss!==!0&&T===0,!i){let l=tt(h.event.target);x=l&&q(l)?Y(l).querySelector(".inner-scroll"):l}i||$("moving"),h.deltaY>0&&k&&(k.scrollY=!1),z(()=>{t.focus()}),o.progressStart(!0,1-p)},Ut=h=>{if(!i&&v!==null&&C!==null&&(h.currentY>=v&&C==="moving"?$("stationary"):h.currentY<v&&C==="stationary"&&$("moving")),!i&&h.deltaY<=0&&x)return;h.deltaY>0&&k&&(k.scrollY=!1);let l=1-p,m=a.length>1?1-a[1]:void 0,c=l+h.deltaY/E,N=m!==void 0&&c>=m&&L,M=N?O:.9999,w=N&&m!==void 0?m+mt((c-m)/(M-m)):c;R=j(1e-4,w,M),o.progressStep(R)},Zt=h=>{if(!i&&h.deltaY<=0&&x&&x.scrollTop>0){$("stationary");return}let l=h.velocityY,m=(h.deltaY+l*350)/E,c=p-m,N=a.reduce((M,w)=>Math.abs(w-c)<Math.abs(M-c)?w:M);gt({breakpoint:N,breakpointOffset:R,canDismiss:L,animated:!0})},gt=h=>{let{breakpoint:l,canDismiss:m,breakpointOffset:c,animated:N}=h,M=m&&l===0,w=M?p:l,G=w!==0;return p=0,_&&A&&(_.keyframes([{offset:0,transform:`translateY(${c*100}%)`},{offset:1,transform:`translateY(${(1-w)*100}%)`}]),A.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${pt(1-c,r)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${pt(w,r)})`}]),B&&B.keyframes([{offset:0,maxHeight:`${(1-c)*100}%`},{offset:1,maxHeight:`${w*100}%`}]),o.progressStep(0)),Q.enable(!1),M?Kt(t,o):G||g(),k&&(w===a[a.length-1]||!i)&&(k.scrollY=!0),!i&&w===0&&$("stationary"),new Promise(K=>{o.onFinish(()=>{G?(i||$("stationary"),_&&A?z(()=>{_.keyframes([...y.WRAPPER_KEYFRAMES]),A.keyframes([...y.BACKDROP_KEYFRAMES]),B?.keyframes([...y.CONTENT_KEYFRAMES]),o.progressStart(!0,1-w),p=w,D(p),p>r?J():ut(),Q.enable(!0),K()}):(Q.enable(!0),K())):K()},{oneTimeCallback:!0}).progressEnd(1,0,N?500:0)})},Q=it({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:Vt,onStart:Ft,onMove:Ut,onEnd:Zt});return{gesture:Q,moveSheetToBreakpoint:gt}},se=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',re=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',ae=class{constructor(t){_t(this,t),this.didPresent=I(this,"ionModalDidPresent",7),this.willPresent=I(this,"ionModalWillPresent",7),this.willDismiss=I(this,"ionModalWillDismiss",7),this.didDismiss=I(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=I(this,"ionBreakpointDidChange",7),this.didPresentShorthand=I(this,"didPresent",7),this.willPresentShorthand=I(this,"willPresent",7),this.willDismissShorthand=I(this,"willDismiss",7),this.didDismissShorthand=I(this,"didDismiss",7),this.ionMount=I(this,"ionMount",7),this.lockController=wt(),this.triggerController=Mt(),this.coreDelegate=St(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:e,handleBehavior:n}=this;n!=="cycle"||e!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:e}=this;e===void 0&&this.dismiss(void 0,Bt)},this.onLifecycle=e=>{let n=this.usersElement,s=de[e.type];if(n&&s){let r=new CustomEvent(s,{bubbles:!1,cancelable:!1,detail:e.detail});n.dispatchEvent(r)}}}onIsOpenChange(t,e){t===!0&&e===!1?this.present():t===!1&&e===!0&&this.dismiss()}triggerChanged(){let{trigger:t,el:e,triggerController:n}=this;t&&n.addClickListener(e,t)}breakpointsChanged(t){t!==void 0&&(this.sortedBreakpoints=t.sort((e,n)=>e-n))}connectedCallback(){let{el:t}=this;At(t),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var t;let{breakpoints:e,initialBreakpoint:n,el:s,htmlAttributes:r}=this,o=this.isSheetModal=e!==void 0&&n!==void 0,a=["aria-label","role"];this.inheritedAttributes=yt(s,a),r!==void 0&&a.forEach(i=>{r[i]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[i]:r[i]}),delete r[i])}),o&&(this.currentBreakpoint=this.initialBreakpoint),e!==void 0&&n!==void 0&&!e.includes(n)&&et("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((t=this.htmlAttributes)===null||t===void 0)&&t.id||Dt(this.el)}componentDidLoad(){this.isOpen===!0&&z(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let e=this.el.parentNode,n=this.inline=e!==null&&!this.hasController,s=this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate;return{inline:n,delegate:s}}checkCanDismiss(t,e){return P(this,null,function*(){let{canDismiss:n}=this;return typeof n=="function"?n(t,e):n})}present(){return P(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{presentingElement:e,el:n}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:s,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield xt(r,n,this.component,["ion-page"],this.componentProps,s),bt(n)?yield Ot(this.usersElement):this.keepContentsMounted||(yield Lt()),ct(()=>this.el.classList.add("show-modal"));let o=e!==void 0;o&&U(this)==="ios"&&(this.statusBarStyle=yield ft.getStyle(),Gt()),yield Ct(this,"modalEnter",qt,ee,{presentingEl:e,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),z(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(dt,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():o&&this.initSwipeToClose(),t()})}initSwipeToClose(){var t;if(U(this)!=="ios")return;let{el:e}=this,n=this.leaveAnimation||lt.get("modalLeave",Wt),s=this.animation=n(e,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Yt(e)){It(e);return}let o=(t=this.statusBarStyle)!==null&&t!==void 0?t:Z.Default;this.gesture=Jt(e,s,o,()=>{this.gestureAnimationDismissing=!0,ht(this.statusBarStyle),this.animation.onFinish(()=>P(this,null,function*(){yield this.dismiss(void 0,F),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:t,initialBreakpoint:e,backdropBreakpoint:n}=this;if(!t||e===void 0)return;let s=this.enterAnimation||lt.get("modalEnter",qt),r=this.animation=s(this.el,{presentingEl:this.presentingElement,currentBreakpoint:e,backdropBreakpoint:n,expandToScroll:this.expandToScroll});r.progressStart(!0,1);let{gesture:o,moveSheetToBreakpoint:a}=ie(this.el,this.backdropEl,t,e,n,r,this.sortedBreakpoints,this.expandToScroll,()=>{var i;return(i=this.currentBreakpoint)!==null&&i!==void 0?i:0},()=>this.sheetOnDismiss(),i=>{this.currentBreakpoint!==i&&(this.currentBreakpoint=i,this.ionBreakpointDidChange.emit({breakpoint:i}))});this.gesture=o,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>P(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,F),this.gestureAnimationDismissing=!1}))}dismiss(t,e){return P(this,null,function*(){var n;if(this.gestureAnimationDismissing&&e!==F)return!1;let s=yield this.lockController.lock();if(e!=="handler"&&!(yield this.checkCanDismiss(t,e)))return s(),!1;let{presentingElement:r}=this;r!==void 0&&U(this)==="ios"&&ht(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(dt,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield Et(this,t,e,"modalLeave",Wt,oe,{presentingEl:r,currentBreakpoint:(n=this.currentBreakpoint)!==null&&n!==void 0?n:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:i}=this.getDelegate();yield vt(i,this.usersElement),ct(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy()}return this.currentBreakpoint=void 0,this.animation=void 0,s(),a})}onDidDismiss(){return st(this.el,"ionModalDidDismiss")}onWillDismiss(){return st(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(t){return P(this,null,function*(){if(!this.isSheetModal){et("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(t)){et(`[ion-modal] - Attempted to set invalid breakpoint value ${t}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:e,moveSheetToBreakpoint:n,canDismiss:s,breakpoints:r,animated:o}=this;e!==t&&n&&(this.sheetTransition=n({breakpoint:t,breakpointOffset:1-e,canDismiss:s!==void 0&&s!==!0&&r[0]===0,animated:o}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return P(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return P(this,null,function*(){let{breakpoints:t,currentBreakpoint:e}=this;if(!t||e==null)return!1;let n=t.filter(a=>a!==0),r=(n.indexOf(e)+1)%n.length,o=n[r];return yield this.setCurrentBreakpoint(o),!0})}render(){let{handle:t,isSheetModal:e,presentingElement:n,htmlAttributes:s,handleBehavior:r,inheritedAttributes:o,focusTrap:a,expandToScroll:i}=this,d=t!==!1&&e,g=U(this),D=n!==void 0&&g==="ios",S=r==="cycle";return H(Nt,Object.assign({key:"0bcbdcfcd7d890eb599da3f97f21c317d34f8e0e","no-router":!0,tabindex:"-1"},s,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[g]:!0,"modal-default":!D&&!e,"modal-card":D,"modal-sheet":e,"modal-no-expand-scroll":e&&!i,"overlay-hidden":!0,[X]:a===!1},Pt(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle}),H("ion-backdrop",{key:"d72159e73daa5af7349aa9e8f695aa435eb43069",ref:b=>this.backdropEl=b,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),g==="ios"&&H("div",{key:"fd2d9b13676ae72473881649a397b6eacde03a03",class:"modal-shadow"}),H("div",Object.assign({key:"908eccb1ad982dcde2dbcff0cbb18b6e60f8ba74",role:"dialog"},o,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:b=>this.wrapperEl=b}),d&&H("button",{key:"332dc0b40363a77c7be62331d9f26def91c790e9",class:"modal-handle",tabIndex:S?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:S?this.onHandleClick:void 0,part:"handle"}),H("slot",{key:"c32698350193c450327e97049daf8b8d1fda0d0e"})))}get el(){return Ht(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},de={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};ae.style={ios:se,md:re};export{ae as ion_modal};
