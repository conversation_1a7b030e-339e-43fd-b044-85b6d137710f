extends Control

@onready var nsfw_checkbox: CheckBox = $VBoxContainer/SettingsContainer/NSFWContainer/NSFWCheckBox
@onready var back_button: Button = $VBoxContainer/BackButton

func _ready():
	# Connect signals
	nsfw_checkbox.toggled.connect(_on_nsfw_toggled)
	back_button.pressed.connect(_on_back_pressed)
	
	# Connect to global input handler
	GlobalInput.back_to_menu_requested.connect(_on_back_pressed)
	
	# Connect to settings changes
	GameSettings.settings_changed.connect(_on_settings_changed)
	
	# Initialize UI with current settings
	update_ui()
	
	# Focus the checkbox for controller navigation
	nsfw_checkbox.grab_focus()

func update_ui():
	nsfw_checkbox.button_pressed = GameSettings.nsfw_content_enabled

func _on_nsfw_toggled(button_pressed: bool):
	GameSettings.nsfw_content_enabled = button_pressed
	GameSettings.save_settings()
	GameSettings.settings_changed.emit()
	print("NSFW content ", "enabled" if button_pressed else "disabled")

func _on_settings_changed():
	# Update UI when settings change from elsewhere
	update_ui()

func _on_back_pressed():
	GameSettings.change_scene(GameSettings.MAIN_MENU_SCENE)
