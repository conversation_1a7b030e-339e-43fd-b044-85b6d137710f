[gd_scene load_steps=3 format=3 uid="uid://b4bdm2rfx42am"]

[ext_resource type="Script" path="res://addons/godot-socketio/socketio.gd" id="1_gxu47"]
[ext_resource type="Script" path="res://screen.gd" id="1_pg1om"]

[node name="Main" type="Node2D"]
script = ExtResource("1_pg1om")

[node name="SocketIO" type="Node" parent="."]
script = ExtResource("1_gxu47")
autoconnect = false
base_url = "http://localhost:3000"

[node name="Connect" type="Button" parent="."]
offset_left = 59.0
offset_top = 32.0
offset_right = 340.0
offset_bottom = 109.0
text = "Connect to server"

[node name="ConnectNamespace" type="Button" parent="."]
offset_left = 432.0
offset_top = 32.0
offset_right = 713.0
offset_bottom = 109.0
text = "Connect to /admin"

[node name="EmitInAdmin" type="Button" parent="."]
offset_left = 432.0
offset_top = 151.0
offset_right = 713.0
offset_bottom = 228.0
text = "Emit event in /admin"

[node name="EmitSimple" type="Button" parent="."]
offset_left = 59.0
offset_top = 147.0
offset_right = 340.0
offset_bottom = 224.0
text = "Emit simple event"

[node name="EmitSearch" type="Button" parent="."]
offset_left = 59.0
offset_top = 265.0
offset_right = 340.0
offset_bottom = 342.0
text = "Emit search event"

[node name="LogLabel" type="Label" parent="."]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 19.0
offset_top = 422.0
offset_right = 571.0
offset_bottom = 638.0
grow_vertical = 0
text = "Events from server log:"

[connection signal="pressed" from="Connect" to="." method="_on_connect_pressed"]
[connection signal="pressed" from="ConnectNamespace" to="." method="_on_connect_namespace_pressed"]
[connection signal="pressed" from="EmitInAdmin" to="." method="_on_emit_in_admin_pressed"]
[connection signal="pressed" from="EmitSimple" to="." method="_on_emit_simple_pressed"]
[connection signal="pressed" from="EmitSearch" to="." method="_on_emit_search_pressed"]
