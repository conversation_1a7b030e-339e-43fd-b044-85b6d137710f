extends Control

@onready var loading_label: Label = $VBoxContainer/LoadingLabel
@onready var progress_bar: ProgressBar = $VBoxContainer/ProgressBar
@onready var status_label: Label = $VBoxContainer/StatusLabel

func _ready():
	# Initialize UI
	loading_label.text = "JUGSHINE"
	status_label.text = "Initializing..."
	progress_bar.value = 0
	
	# Connect to signals from our global WebSocket singleton
	WebSocket.connection_status_changed.connect(_on_connection_status_changed)
	
	# Start connection process
	start_connection_process()

func start_connection_process():
	status_label.text = "Connecting to server..."
	progress_bar.value = 20
	
	if WebSocket.is_socket_connected():
		handle_connection_success()
	else:
		WebSocket.connect_to_server()
		# We'll now wait for the connection_status_changed signal

func _on_connection_status_changed(is_now_connected: bool):
	if is_now_connected:
		handle_connection_success()
	else:
		handle_connection_failure()


func handle_connection_failure():
	# This can be expanded with more robust retry logic if needed
	status_label.text = "Could not connect to server. Continuing offline..."
	progress_bar.value = 60
	GameSettings.is_connected_to_server = false
	
	# Still simulate loading and continue to main menu
	await simulate_loading_steps()
	transition_to_main_menu()

func handle_connection_success():
	status_label.text = "Connected to server!"
	progress_bar.value = 60
	
	# Set connection status in GameSettings
	GameSettings.is_connected_to_server = true
	
	# Simulate additional loading steps
	await simulate_loading_steps()
	
	# Transition to main menu
	transition_to_main_menu()


func simulate_loading_steps():
	# Simulate loading game assets
	status_label.text = "Loading game assets..."
	progress_bar.value = 80
	await get_tree().create_timer(1.0).timeout
	
	# Simulate final initialization
	status_label.text = "Finalizing..."
	progress_bar.value = 100
	await get_tree().create_timer(0.5).timeout

func transition_to_main_menu():
	status_label.text = "Ready!"
	await get_tree().create_timer(0.5).timeout
	
	# Change to main menu scene
	GameSettings.change_scene(GameSettings.MAIN_MENU_SCENE)
