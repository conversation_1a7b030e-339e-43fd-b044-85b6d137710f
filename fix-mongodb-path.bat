@echo off
title Fix MongoDB PATH
color 0D

echo.
echo ========================================
echo    FIX MONGODB PATH
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo [ERROR] This script must be run as Administrator to modify PATH
    echo [INFO] Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

:: Check if MongoDB is already in PATH
where mongod >nul 2>&1
if not errorlevel 1 (
    echo [INFO] MongoDB is already available in PATH
    mongod --version
    echo.
    echo [SUCCESS] No action needed!
    pause
    exit /b 0
)

:: Find MongoDB installation
echo [INFO] Searching for MongoDB installation...
set MONGODB_BIN=

:: Check common installation directories
for /d %%d in ("C:\Program Files\MongoDB\Server\*") do (
    if exist "%%d\bin\mongod.exe" (
        set MONGODB_BIN=%%d\bin
        echo [INFO] Found MongoDB at: %%d\bin
        goto :found_mongodb
    )
)

for /d %%d in ("C:\Program Files (x86)\MongoDB\Server\*") do (
    if exist "%%d\bin\mongod.exe" (
        set MONGODB_BIN=%%d\bin
        echo [INFO] Found MongoDB at: %%d\bin
        goto :found_mongodb
    )
)

echo [ERROR] MongoDB installation not found!
echo [INFO] Please install MongoDB first using install-mongodb.bat
echo.
pause
exit /b 1

:found_mongodb
echo [INFO] Adding MongoDB to system PATH...

:: Add to system PATH
setx PATH "%PATH%;%MONGODB_BIN%" /M >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Failed to update system PATH
    echo [INFO] You may need to add it manually:
    echo [INFO] Add this to your PATH: %MONGODB_BIN%
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] MongoDB added to system PATH!
echo [INFO] Path added: %MONGODB_BIN%
echo.
echo [INFO] Please restart your command prompt or computer for changes to take effect.
echo [INFO] Then you can run start-server-mongodb.bat
echo.
pause
