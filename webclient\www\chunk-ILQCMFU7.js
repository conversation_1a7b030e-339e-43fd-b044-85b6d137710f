import{b as n}from"./chunk-XCHF2ADM.js";var p="ionKeyboardDidShow",g="ionKeyboardDidHide",b=150,r={},o={},s=!1,H=()=>{r={},o={},s=!1},L=e=>{if(n.getEngine())y(e);else{if(!e.visualViewport)return;o=c(e.visualViewport),e.visualViewport.onresize=()=>{K(e),l()||D(e)?i(e):d(e)&&a(e)}}},y=e=>{e.addEventListener("keyboardDidShow",t=>i(e,t)),e.addEventListener("keyboardDidHide",()=>a(e))},i=(e,t)=>{E(e,t),s=!0},a=e=>{u(e),s=!1},l=()=>{let e=(r.height-o.height)*o.scale;return!s&&r.width===o.width&&e>b},D=e=>s&&!d(e),d=e=>s&&o.height===e.innerHeight,E=(e,t)=>{let h=t?t.keyboardHeight:e.innerHeight-o.height,f=new CustomEvent(p,{detail:{keyboardHeight:h}});e.dispatchEvent(f)},u=e=>{let t=new CustomEvent(g);e.dispatchEvent(t)},K=e=>{r=Object.assign({},o),o=c(e.visualViewport)},c=e=>({width:Math.round(e.width),height:Math.round(e.height),offsetTop:e.offsetTop,offsetLeft:e.offsetLeft,pageTop:e.pageTop,pageLeft:e.pageLeft,scale:e.scale});export{p as a,g as b,H as c,L as d,i as e,a as f,l as g,D as h,d as i,K as j,c as k};
