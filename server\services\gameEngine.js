const Game = require('../models/Game');
const GameContent = require('../models/GameContent');

class GameEngine {

    async startGame(game, io) {
        if (game.game_status !== 'Waiting for Players') {
            throw new Error('Game has already started.');
        }

        game.game_status = 'Ready';
        await game.save();
        io.to(game.room_code).emit('game-updated', game);
        
        console.log(`Game ${game.room_code} started.`);
        // The first round will be triggered by this state change on the client
        return this.startNextRound(game, io);
    }

    async startNextRound(game, io) {
        if (game.players.some(p => p.score >= game.settings.rounds_to_win)) {
            return this.endGame(game, io);
        }

        game.current_round += 1;
        game.current_phase = 'responding';
        game.game_data.submitted_responses = [];

        // Assign judge (round-robin)
        const judgeIndex = (game.current_round - 1) % game.players.length;
        game.current_judge = game.players[judgeIndex].name;

        // Get content
        const content = await GameContent.getContent(game.game_type, game.settings.mature_content);
        
        // Assign prompt
        game.game_data.current_prompt = this.getRandomItem(content.prompts);

        // Assign responses to non-judge players
        const playerResponses = {};
        for (const player of game.players) {
            if (player.name !== game.current_judge) {
                playerResponses[player.name] = this.getRandomItems(content.responses, 5);
            }
        }
        game.game_data.player_responses = playerResponses;

        game.markModified('game_data');
        await game.save();
        io.to(game.room_code).emit('game-updated', game);

        console.log(`[${game.room_code}] Round ${game.current_round} started. Judge: ${game.current_judge}`);
        
        // Start timer
        this.startTimer(game, io, game.settings.round_time_limit, 'judging');
        
        return game;
    }

    async submitPlayerResponse(game, playerName, data, io) {
        if (game.current_phase !== 'responding') throw new Error('Not in a response phase.');
        if (game.current_judge === playerName) throw new Error('Judge cannot submit a response.');
        
        const alreadySubmitted = game.game_data.submitted_responses.some(r => r.player_name === playerName);
        if (alreadySubmitted) throw new Error('Player has already submitted a response.');
        
        game.game_data.submitted_responses.push({
            player_name: playerName,
            response: data.response,
            timestamp: new Date().toISOString()
        });

        const nonJudgePlayers = game.players.length - 1;
        if (game.game_data.submitted_responses.length >= nonJudgePlayers) {
            // All players have responded, move to judging
            this.clearTimer(game);
            return this.startJudgingPhase(game, io);
        } else {
            game.markModified('game_data');
            await game.save();
            io.to(game.room_code).emit('game-updated', game);
            return game;
        }
    }

    async startJudgingPhase(game, io) {
        game.current_phase = 'judging';
        await game.save();
        io.to(game.room_code).emit('game-updated', game);
        
        console.log(`[${game.room_code}] All responses in. Moving to judging.`);
        this.startTimer(game, io, game.settings.round_time_limit, 'round_end');
        return game;
    }

    async judgeWinner(game, data, io) {
        if (game.current_phase !== 'judging') throw new Error('Not in a judging phase.');
        
        const winningResponse = game.game_data.submitted_responses[data.response_index];
        if (!winningResponse) throw new Error('Invalid response selection.');

        const winnerName = winningResponse.player_name;
        game.updatePlayerScore(winnerName, 1);
        
        game.rounds.push({
            round_number: game.current_round,
            winner: winnerName,
            winning_content: winningResponse.response,
            completed_at: new Date()
        });
        
        game.current_phase = 'round_end';
        game.game_data.current_winner = {
            player_name: winnerName,
            response: winningResponse.response,
            round: game.current_round,
        };
        
        this.clearTimer(game);
        game.markModified('game_data');
        await game.save();
        io.to(game.room_code).emit('game-updated', game);

        // Wait 5 seconds, then start next round or end game
        setTimeout(() => this.startNextRound(game, io), 5000);
        
        return game;
    }

    async resetForNewGame(game, io) {
        game.players.forEach(p => p.score = 0);
        game.current_round = 0;
        game.rounds = [];
        game.game_data = {};
        game.game_status = 'Ready';
        
        await game.save();
        io.to(game.room_code).emit('game-updated', game);

        return this.startNextRound(game, io);
    }

    async closeGame(game, io) {
        game.game_status = 'Closed';
        await game.save();
        io.to(game.room_code).emit('game-closed', { room_code: game.room_code });
        console.log(`[${game.room_code}] Game closed by leader.`);
        return game;
    }

    // --- Timer Management ---
    startTimer(game, io, duration, nextPhase) {
        this.clearTimer(game); // Ensure no old timer is running
        
        const timerId = setTimeout(async () => {
            console.log(`[${game.room_code}] Timer expired. Transitioning to ${nextPhase}.`);
            
            const freshGame = await Game.findById(game._id);
            if (!freshGame || freshGame.current_phase === nextPhase) return;

            if (nextPhase === 'judging') {
                this.startJudgingPhase(freshGame, io);
            } else if (nextPhase === 'round_end') {
                // No winner was chosen
                freshGame.current_phase = 'round_end';
                freshGame.game_data.current_winner = {}; // No winner
                freshGame.markModified('game_data');
                await freshGame.save();
                io.to(freshGame.room_code).emit('game-updated', freshGame);

                setTimeout(() => this.startNextRound(freshGame, io), 5000);
            }
        }, duration * 1000);

        game.timerId = timerId;
    }

    clearTimer(game) {
        if (game.timerId) {
            clearTimeout(game.timerId);
            delete game.timerId;
        }
    }

    // --- Utility Functions ---
    getRandomItem(arr) {
        if (!arr || arr.length === 0) return null;
        return arr[Math.floor(Math.random() * arr.length)];
    }

    getRandomItems(arr, count) {
        if (!arr || arr.length === 0) return [];
        const shuffled = [...arr].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }
}

// Export a singleton instance
module.exports = new GameEngine();