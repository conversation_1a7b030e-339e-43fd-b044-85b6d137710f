import{b as Za,c as Ya,d as Qa,e as <PERSON>,f as Ja}from"./chunk-SM5Y6Q5Y.js";import{g as of,h as sf}from"./chunk-PFDCGU6V.js";import{a as pf,d as gf}from"./chunk-B3E62VDJ.js";import{a as Xa}from"./chunk-M2X7KQLB.js";import{a as Rt,d as df,e as ff,f as hf,h as qa}from"./chunk-XTVTS2NW.js";import{a as we,b as af,c as cf,d as uf,e as ii,f as lf}from"./chunk-C5RQ2IC2.js";import{b as Nt}from"./chunk-42C7ZIID.js";import{a as g,b as T,d as rf,f as ie}from"./chunk-S5EYVFES.js";var ec;function tc(){return ec}function ft(e){let n=ec;return ec=e,n}var ky=Symbol("NotFound"),si=class extends Error{name="\u0275NotFound";constructor(n){super(n)}};function Pn(e){return e===ky||e?.name==="\u0275NotFound"}function di(e,n){return Object.is(e,n)}var ee=null,ai=!1,nc=1,Fy=null,ue=Symbol("SIGNAL");function A(e){let n=ee;return ee=e,n}function fi(){return ee}var en={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Ln(e){if(ai)throw new Error("");if(ee===null)return;ee.consumerOnSignalRead(e);let n=ee.nextProducerIndex++;if(mi(ee),n<ee.producerNode.length&&ee.producerNode[n]!==e&&Vr(ee)){let t=ee.producerNode[n];gi(t,ee.producerIndexOfThis[n])}ee.producerNode[n]!==e&&(ee.producerNode[n]=e,ee.producerIndexOfThis[n]=Vr(ee)?vf(e,ee,n):0),ee.producerLastReadVersion[n]=e.version}function mf(){nc++}function hi(e){if(!(Vr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===nc)){if(!e.producerMustRecompute(e)&&!jr(e)){li(e);return}e.producerRecomputeValue(e),li(e)}}function rc(e){if(e.liveConsumerNode===void 0)return;let n=ai;ai=!0;try{for(let t of e.liveConsumerNode)t.dirty||Py(t)}finally{ai=n}}function oc(){return ee?.consumerAllowSignalWrites!==!1}function Py(e){e.dirty=!0,rc(e),e.consumerMarkedDirty?.(e)}function li(e){e.dirty=!1,e.lastCleanEpoch=nc}function tn(e){return e&&(e.nextProducerIndex=0),A(e)}function Vn(e,n){if(A(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Vr(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)gi(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function jr(e){mi(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(hi(t),r!==t.version))return!0}return!1}function pi(e){if(mi(e),Vr(e))for(let n=0;n<e.producerNode.length;n++)gi(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function vf(e,n,t){if(yf(e),e.liveConsumerNode.length===0&&Df(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=vf(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function gi(e,n){if(yf(e),e.liveConsumerNode.length===1&&Df(e))for(let r=0;r<e.producerNode.length;r++)gi(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];mi(o),o.producerIndexOfThis[r]=n}}function Vr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function mi(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function yf(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Df(e){return e.producerNode!==void 0}function vi(e){Fy?.(e)}function yi(e,n){let t=Object.create(Ly);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(hi(t),Ln(t),t.value===Lr)throw t.error;return t.value};return r[ue]=t,vi(t),r}var ci=Symbol("UNSET"),ui=Symbol("COMPUTING"),Lr=Symbol("ERRORED"),Ly=T(g({},en),{value:ci,dirty:!0,error:null,equal:di,kind:"computed",producerMustRecompute(e){return e.value===ci||e.value===ui},producerRecomputeValue(e){if(e.value===ui)throw new Error("");let n=e.value;e.value=ui;let t=tn(e),r,o=!1;try{r=e.computation(),A(null),o=n!==ci&&n!==Lr&&r!==Lr&&e.equal(n,r)}catch(i){r=Lr,e.error=i}finally{Vn(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function Vy(){throw new Error}var Cf=Vy;function Ef(e){Cf(e)}function ic(e){Cf=e}var jy=null;function sc(e,n){let t=Object.create(Di);t.value=e,n!==void 0&&(t.equal=n);let r=()=>If(t);return r[ue]=t,vi(t),[r,s=>jn(t,s),s=>ac(t,s)]}function If(e){return Ln(e),e.value}function jn(e,n){oc()||Ef(e),e.equal(e.value,n)||(e.value=n,By(e))}function ac(e,n){oc()||Ef(e),jn(e,n(e.value))}var Di=T(g({},en),{equal:di,value:void 0,kind:"signal"});function By(e){e.version++,mf(),rc(e),jy?.(e)}function w(e){return typeof e=="function"}function Bn(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var Ci=Bn(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function Br(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var K=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(w(r))try{r()}catch(i){n=i instanceof Ci?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{wf(i)}catch(s){n=n??[],s instanceof Ci?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Ci(n)}}add(n){var t;if(n&&n!==this)if(this.closed)wf(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&Br(t,n)}remove(n){let{_finalizers:t}=this;t&&Br(t,n),n instanceof e&&n._removeParent(this)}};K.EMPTY=(()=>{let e=new K;return e.closed=!0,e})();var cc=K.EMPTY;function Ei(e){return e instanceof K||e&&"closed"in e&&w(e.remove)&&w(e.add)&&w(e.unsubscribe)}function wf(e){w(e)?e():e.unsubscribe()}var Ue={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Hn={setTimeout(e,n,...t){let{delegate:r}=Hn;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=Hn;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Ii(e){Hn.setTimeout(()=>{let{onUnhandledError:n}=Ue;if(n)n(e);else throw e})}function Hr(){}var _f=uc("C",void 0,void 0);function bf(e){return uc("E",void 0,e)}function Sf(e){return uc("N",e,void 0)}function uc(e,n,t){return{kind:e,value:n,error:t}}var nn=null;function Un(e){if(Ue.useDeprecatedSynchronousErrorHandling){let n=!nn;if(n&&(nn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=nn;if(nn=null,t)throw r}}else e()}function Mf(e){Ue.useDeprecatedSynchronousErrorHandling&&nn&&(nn.errorThrown=!0,nn.error=e)}var rn=class extends K{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,Ei(n)&&n.add(this)):this.destination=$y}static create(n,t,r){return new $n(n,t,r)}next(n){this.isStopped?dc(Sf(n),this):this._next(n)}error(n){this.isStopped?dc(bf(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?dc(_f,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Hy=Function.prototype.bind;function lc(e,n){return Hy.call(e,n)}var fc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){wi(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){wi(r)}else wi(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){wi(t)}}},$n=class extends rn{constructor(n,t,r){super();let o;if(w(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&Ue.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&lc(n.next,i),error:n.error&&lc(n.error,i),complete:n.complete&&lc(n.complete,i)}):o=n}this.destination=new fc(o)}};function wi(e){Ue.useDeprecatedSynchronousErrorHandling?Mf(e):Ii(e)}function Uy(e){throw e}function dc(e,n){let{onStoppedNotification:t}=Ue;t&&Hn.setTimeout(()=>t(e,n))}var $y={closed:!0,next:Hr,error:Uy,complete:Hr};var zn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function he(e){return e}function hc(...e){return pc(e)}function pc(e){return e.length===0?he:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var k=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=Gy(t)?t:new $n(t,r,o);return Un(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Tf(r),new r((o,i)=>{let s=new $n({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[zn](){return this}pipe(...t){return pc(t)(this)}toPromise(t){return t=Tf(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function Tf(e){var n;return(n=e??Ue.Promise)!==null&&n!==void 0?n:Promise}function zy(e){return e&&w(e.next)&&w(e.error)&&w(e.complete)}function Gy(e){return e&&e instanceof rn||zy(e)&&Ei(e)}function gc(e){return w(e?.lift)}function F(e){return n=>{if(gc(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function O(e,n,t,r,o){return new mc(e,n,t,r,o)}var mc=class extends rn{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function Gn(){return F((e,n)=>{let t=null;e._refCount++;let r=O(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var Wn=class extends k{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,gc(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new K;let t=this.getSubject();n.add(this.source.subscribe(O(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=K.EMPTY)}return n}refCount(){return Gn()(this)}};var Af=Bn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var B=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new _i(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Af}next(t){Un(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){Un(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){Un(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?cc:(this.currentObservers=null,i.push(t),new K(()=>{this.currentObservers=null,Br(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new k;return t.source=this,t}}return e.create=(n,t)=>new _i(n,t),e})(),_i=class extends B{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:cc}};var J=class extends B{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var ye=new k(e=>e.complete());function Nf(e){return e&&w(e.schedule)}function Rf(e){return e[e.length-1]}function bi(e){return w(Rf(e))?e.pop():void 0}function xt(e){return Nf(Rf(e))?e.pop():void 0}function Ur(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function Of(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,n||[])).next())})}function xf(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function on(e){return this instanceof on?(this.v=e,this):new on(e)}function kf(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(m){return Promise.resolve(m).then(f,d)}}function a(f,m){r[f]&&(o[f]=function(E){return new Promise(function(S,R){i.push([f,E,S,R])>1||c(f,E)})},m&&(o[f]=m(o[f])))}function c(f,m){try{u(r[f](m))}catch(E){h(i[0][3],E)}}function u(f){f.value instanceof on?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,m){f(m),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Ff(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof xf=="function"?xf(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var qn=e=>e&&typeof e.length=="number"&&typeof e!="function";function Si(e){return w(e?.then)}function Mi(e){return w(e[zn])}function Ti(e){return Symbol.asyncIterator&&w(e?.[Symbol.asyncIterator])}function Ai(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Wy(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ni=Wy();function Ri(e){return w(e?.[Ni])}function xi(e){return kf(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield on(t.read());if(o)return yield on(void 0);yield yield on(r)}}finally{t.releaseLock()}})}function Oi(e){return w(e?.getReader)}function Y(e){if(e instanceof k)return e;if(e!=null){if(Mi(e))return qy(e);if(qn(e))return Zy(e);if(Si(e))return Yy(e);if(Ti(e))return Pf(e);if(Ri(e))return Qy(e);if(Oi(e))return Ky(e)}throw Ai(e)}function qy(e){return new k(n=>{let t=e[zn]();if(w(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Zy(e){return new k(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function Yy(e){return new k(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,Ii)})}function Qy(e){return new k(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function Pf(e){return new k(n=>{Jy(e,n).catch(t=>n.error(t))})}function Ky(e){return Pf(xi(e))}function Jy(e,n){var t,r,o,i;return Of(this,void 0,void 0,function*(){try{for(t=Ff(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function De(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function ki(e,n=0){return F((t,r)=>{t.subscribe(O(r,o=>De(r,e,()=>r.next(o),n),()=>De(r,e,()=>r.complete(),n),o=>De(r,e,()=>r.error(o),n)))})}function Fi(e,n=0){return F((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function Lf(e,n){return Y(e).pipe(Fi(n),ki(n))}function Vf(e,n){return Y(e).pipe(Fi(n),ki(n))}function jf(e,n){return new k(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function Bf(e,n){return new k(t=>{let r;return De(t,n,()=>{r=e[Ni](),De(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>w(r?.return)&&r.return()})}function Pi(e,n){if(!e)throw new Error("Iterable cannot be null");return new k(t=>{De(t,n,()=>{let r=e[Symbol.asyncIterator]();De(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function Hf(e,n){return Pi(xi(e),n)}function Uf(e,n){if(e!=null){if(Mi(e))return Lf(e,n);if(qn(e))return jf(e,n);if(Si(e))return Vf(e,n);if(Ti(e))return Pi(e,n);if(Ri(e))return Bf(e,n);if(Oi(e))return Hf(e,n)}throw Ai(e)}function z(e,n){return n?Uf(e,n):Y(e)}function _(...e){let n=xt(e);return z(e,n)}function Zn(e,n){let t=w(e)?e:()=>e,r=o=>o.error(t());return new k(n?o=>n.schedule(r,0,o):r)}function vc(e){return!!e&&(e instanceof k||w(e.lift)&&w(e.subscribe))}var ht=Bn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function P(e,n){return F((t,r)=>{let o=0;t.subscribe(O(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:Xy}=Array;function eD(e,n){return Xy(n)?e(...n):e(n)}function Yn(e){return P(n=>eD(e,n))}var{isArray:tD}=Array,{getPrototypeOf:nD,prototype:rD,keys:oD}=Object;function Li(e){if(e.length===1){let n=e[0];if(tD(n))return{args:n,keys:null};if(iD(n)){let t=oD(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function iD(e){return e&&typeof e=="object"&&nD(e)===rD}function Vi(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function Qn(...e){let n=xt(e),t=bi(e),{args:r,keys:o}=Li(e);if(r.length===0)return z([],n);let i=new k(sD(r,n,o?s=>Vi(o,s):he));return t?i.pipe(Yn(t)):i}function sD(e,n,t=he){return r=>{$f(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)$f(n,()=>{let u=z(e[c],n),l=!1;u.subscribe(O(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function $f(e,n,t){e?De(t,e,n):n()}function zf(e,n,t,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&n.complete()},f=E=>u<r?m(E):c.push(E),m=E=>{i&&n.next(E),u++;let S=!1;Y(t(E,l++)).subscribe(O(n,R=>{o?.(R),i?f(R):n.next(R)},()=>{S=!0},void 0,()=>{if(S)try{for(u--;c.length&&u<r;){let R=c.shift();s?De(n,s,()=>m(R)):m(R)}h()}catch(R){n.error(R)}}))};return e.subscribe(O(n,f,()=>{d=!0,h()})),()=>{a?.()}}function W(e,n,t=1/0){return w(n)?W((r,o)=>P((i,s)=>n(r,i,o,s))(Y(e(r,o))),t):(typeof n=="number"&&(t=n),F((r,o)=>zf(r,o,e,t)))}function Kn(e=1/0){return W(he,e)}function Gf(){return Kn(1)}function Jn(...e){return Gf()(z(e,xt(e)))}function $r(e){return new k(n=>{Y(e()).subscribe(n)})}function yc(...e){let n=bi(e),{args:t,keys:r}=Li(e),o=new k(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;Y(t[l]).subscribe(O(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?Vi(r,a):a),i.complete())}))}});return n?o.pipe(Yn(n)):o}var aD=["addListener","removeListener"],cD=["addEventListener","removeEventListener"],uD=["on","off"];function zr(e,n,t,r){if(w(t)&&(r=t,t=void 0),r)return zr(e,n,t).pipe(Yn(r));let[o,i]=fD(e)?cD.map(s=>a=>e[s](n,a,t)):lD(e)?aD.map(Wf(e,n)):dD(e)?uD.map(Wf(e,n)):[];if(!o&&qn(e))return W(s=>zr(s,n,t))(Y(e));if(!o)throw new TypeError("Invalid event target");return new k(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function Wf(e,n){return t=>r=>e[t](n,r)}function lD(e){return w(e.addListener)&&w(e.removeListener)}function dD(e){return w(e.on)&&w(e.off)}function fD(e){return w(e.addEventListener)&&w(e.removeEventListener)}function se(e,n){return F((t,r)=>{let o=0;t.subscribe(O(r,i=>e.call(n,i,o++)&&r.next(i)))})}function Je(e){return F((n,t)=>{let r=null,o=!1,i;r=n.subscribe(O(t,void 0,void 0,s=>{i=Y(e(s,Je(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function qf(e,n,t,r,o){return(i,s)=>{let a=t,c=n,u=0;i.subscribe(O(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ot(e,n){return w(n)?W(e,n,1):W(e,1)}function kt(e){return F((n,t)=>{let r=!1;n.subscribe(O(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function pt(e){return e<=0?()=>ye:F((n,t)=>{let r=0;n.subscribe(O(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function Dc(e,n=he){return e=e??hD,F((t,r)=>{let o,i=!0;t.subscribe(O(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function hD(e,n){return e===n}function ji(e=pD){return F((n,t)=>{let r=!1;n.subscribe(O(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function pD(){return new ht}function Gr(e){return F((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function gt(e,n){let t=arguments.length>=2;return r=>r.pipe(e?se((o,i)=>e(o,i,r)):he,pt(1),t?kt(n):ji(()=>new ht))}function Xn(e){return e<=0?()=>ye:F((n,t)=>{let r=[];n.subscribe(O(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function Cc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?se((o,i)=>e(o,i,r)):he,Xn(1),t?kt(n):ji(()=>new ht))}function Ec(e,n){return F(qf(e,n,arguments.length>=2,!0))}function Ic(...e){let n=xt(e);return F((t,r)=>{(n?Jn(e,t,n):Jn(e,t)).subscribe(r)})}function le(e,n){return F((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(O(r,c=>{o?.unsubscribe();let u=0,l=i++;Y(e(c,l)).subscribe(o=O(r,d=>r.next(n?n(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Bi(e){return F((n,t)=>{Y(e).subscribe(O(t,()=>t.complete(),Hr)),!t.closed&&n.subscribe(t)})}function ne(e,n,t){let r=w(e)||n||t?{next:e,error:n,complete:t}:e;return r?F((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(O(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):he}function Zf(e){let n=A(null);try{return e()}finally{A(n)}}var Gi="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",y=class extends Error{code;constructor(n,t){super(Wi(n,t)),this.code=n}};function gD(e){return`NG0${Math.abs(e)}`}function Wi(e,n){return`${gD(e)}${n?": "+n:""}`}var Yr=globalThis;function G(e){for(let n in e)if(e[n]===G)return n;throw Error("")}function Jf(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function Ce(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Ce).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function Fc(e,n){return e?n?`${e} ${n}`:e:n||""}var mD=G({__forward_ref__:G});function Fe(e){return e.__forward_ref__=Fe,e.toString=function(){return Ce(this())},e}function ae(e){return Pc(e)?e():e}function Pc(e){return typeof e=="function"&&e.hasOwnProperty(mD)&&e.__forward_ref__===Fe}function Xf(e,n,t,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${t} ${r} ${n} <=Actual]`))}function D(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function et(e){return{providers:e.providers||[],imports:e.imports||[]}}function Qr(e){return vD(e,qi)}function Lc(e){return Qr(e)!==null}function vD(e,n){return e.hasOwnProperty(n)&&e[n]||null}function yD(e){let n=e?.[qi]??null;return n||null}function _c(e){return e&&e.hasOwnProperty(Ui)?e[Ui]:null}var qi=G({\u0275prov:G}),Ui=G({\u0275inj:G}),C=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=D({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Vc(e){return e&&!!e.\u0275providers}var jc=G({\u0275cmp:G}),Bc=G({\u0275dir:G}),Hc=G({\u0275pipe:G}),Uc=G({\u0275mod:G}),qr=G({\u0275fac:G}),fn=G({__NG_ELEMENT_ID__:G}),Yf=G({__NG_ENV_ID__:G});function hn(e){return typeof e=="string"?e:e==null?"":String(e)}function eh(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():hn(e)}function $c(e,n){throw new y(-200,e)}function Zi(e,n){throw new y(-201,!1)}var bc;function th(){return bc}function _e(e){let n=bc;return bc=e,n}function zc(e,n,t){let r=Qr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&8)return null;if(n!==void 0)return n;Zi(e,"Injector")}var DD={},sn=DD,Sc="__NG_DI_FLAG__",Mc=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=an(t)||0;try{return this.injector.get(n,r&8?null:sn,r)}catch(o){if(Pn(o))return o;throw o}}},$i="ngTempTokenPath",CD="ngTokenPath",ED=/\n/gm,ID="\u0275",Qf="__source";function wD(e,n=0){let t=tc();if(t===void 0)throw new y(-203,!1);if(t===null)return zc(e,void 0,n);{let r=_D(n),o=t.retrieve(e,r);if(Pn(o)){if(r.optional)return null;throw o}return o}}function I(e,n=0){return(th()||wD)(ae(e),n)}function p(e,n){return I(e,an(n))}function an(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function _D(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Tc(e){let n=[];for(let t=0;t<e.length;t++){let r=ae(e[t]);if(Array.isArray(r)){if(r.length===0)throw new y(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=bD(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(I(o,i))}else n.push(I(r))}return n}function Gc(e,n){return e[Sc]=n,e.prototype[Sc]=n,e}function bD(e){return e[Sc]}function SD(e,n,t,r){let o=e[$i];throw n[Qf]&&o.unshift(n[Qf]),e.message=MD(`
`+e.message,o,t,r),e[CD]=o,e[$i]=null,e}function MD(e,n,t,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==ID?e.slice(2):e;let o=Ce(n);if(Array.isArray(n))o=n.map(Ce).join(" -> ");else if(typeof n=="object"){let i=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Ce(a)))}o=`{${i.join(", ")}}`}return`${t}${r?"("+r+")":""}[${o}]: ${e.replace(ED,`
  `)}`}function cn(e,n){let t=e.hasOwnProperty(qr);return t?e[qr]:null}function nh(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function rh(e){return e.flat(Number.POSITIVE_INFINITY)}function Yi(e,n){e.forEach(t=>Array.isArray(t)?Yi(t,n):n(t))}function Wc(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function Kr(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function oh(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function ih(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function sh(e,n,t){let r=tr(e,n);return r>=0?e[r|1]=t:(r=~r,ih(e,r,n,t)),r}function Qi(e,n){let t=tr(e,n);if(t>=0)return e[t|1]}function tr(e,n){return TD(e,n,1)}function TD(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var Pt={},be=[],Lt=new C(""),qc=new C("",-1),Zc=new C(""),Zr=class{get(n,t=sn){if(t===sn)throw new si(`NullInjectorError: No provider for ${Ce(n)}!`);return t}};function Yc(e){return e[Uc]||null}function tt(e){return e[jc]||null}function Qc(e){return e[Bc]||null}function ah(e){return e[Hc]||null}function Ki(e){return{\u0275providers:e}}function ch(...e){return{\u0275providers:Kc(!0,e),\u0275fromNgModule:!0}}function Kc(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return Yi(n,s=>{let a=s;zi(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&uh(o,i),t}function uh(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];Jc(o,i=>{n(i,r)})}}function zi(e,n,t,r){if(e=ae(e),!e)return!1;let o=null,i=_c(e),s=!i&&tt(e);if(!i&&!s){let c=e.ngModule;if(i=_c(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)zi(u,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Yi(i.imports,l=>{zi(l,n,t,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&uh(u,n)}if(!a){let u=cn(o)||(()=>new o);n({provide:o,useFactory:u,deps:be},o),n({provide:Zc,useValue:o,multi:!0},o),n({provide:Lt,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Jc(c,l=>{n(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Jc(e,n){for(let t of e)Vc(t)&&(t=t.\u0275providers),Array.isArray(t)?Jc(t,n):n(t)}var AD=G({provide:String,useValue:G});function lh(e){return e!==null&&typeof e=="object"&&AD in e}function ND(e){return!!(e&&e.useExisting)}function RD(e){return!!(e&&e.useFactory)}function un(e){return typeof e=="function"}function dh(e){return!!e.useClass}var Jr=new C(""),Hi={},Kf={},wc;function nr(){return wc===void 0&&(wc=new Zr),wc}var q=class{},ln=class extends q{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,Nc(n,s=>this.processProvider(s)),this.records.set(qc,er(void 0,this)),o.has("environment")&&this.records.set(q,er(void 0,this));let i=this.records.get(Jr);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Zc,be,{self:!0}))}retrieve(n,t){let r=an(t)||0;try{return this.get(n,sn,r)}catch(o){if(Pn(o))return o;throw o}}destroy(){Wr(this),this._destroyed=!0;let n=A(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),A(n)}}onDestroy(n){return Wr(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){Wr(this);let t=ft(this),r=_e(void 0),o;try{return n()}finally{ft(t),_e(r)}}get(n,t=sn,r){if(Wr(this),n.hasOwnProperty(Yf))return n[Yf](this);let o=an(r),i,s=ft(this),a=_e(void 0);try{if(!(o&4)){let u=this.records.get(n);if(u===void 0){let l=PD(n)&&Qr(n);l&&this.injectableDefInScope(l)?u=er(Ac(n),Hi):u=null,this.records.set(n,u)}if(u!=null)return this.hydrate(n,u)}let c=o&2?nr():this.parent;return t=o&8&&t===sn?null:t,c.get(n,t)}catch(c){if(Pn(c)){if((c[$i]=c[$i]||[]).unshift(Ce(n)),s)throw c;return SD(c,n,"R3InjectorError",this.source)}else throw c}finally{_e(a),ft(s)}}resolveInjectorInitializers(){let n=A(null),t=ft(this),r=_e(void 0),o;try{let i=this.get(Lt,be,{self:!0});for(let s of i)s()}finally{ft(t),_e(r),A(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(Ce(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=ae(n);let t=un(n)?n:ae(n&&n.provide),r=OD(n);if(!un(n)&&n.multi===!0){let o=this.records.get(t);o||(o=er(void 0,Hi,!0),o.factory=()=>Tc(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t){let r=A(null);try{return t.value===Kf?$c(Ce(n)):t.value===Hi&&(t.value=Kf,t.value=t.factory()),typeof t.value=="object"&&t.value&&FD(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{A(r)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=ae(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Ac(e){let n=Qr(e),t=n!==null?n.factory:cn(e);if(t!==null)return t;if(e instanceof C)throw new y(204,!1);if(e instanceof Function)return xD(e);throw new y(204,!1)}function xD(e){if(e.length>0)throw new y(204,!1);let t=yD(e);return t!==null?()=>t.factory(e):()=>new e}function OD(e){if(lh(e))return er(void 0,e.useValue);{let n=Xc(e);return er(n,Hi)}}function Xc(e,n,t){let r;if(un(e)){let o=ae(e);return cn(o)||Ac(o)}else if(lh(e))r=()=>ae(e.useValue);else if(RD(e))r=()=>e.useFactory(...Tc(e.deps||[]));else if(ND(e))r=()=>I(ae(e.useExisting));else{let o=ae(e&&(e.useClass||e.provide));if(kD(e))r=()=>new o(...Tc(e.deps));else return cn(o)||Ac(o)}return r}function Wr(e){if(e.destroyed)throw new y(205,!1)}function er(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function kD(e){return!!e.deps}function FD(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function PD(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Nc(e,n){for(let t of e)Array.isArray(t)?Nc(t,n):t&&Vc(t)?Nc(t.\u0275providers,n):n(t)}function Se(e,n){let t;e instanceof ln?(Wr(e),t=e):t=new Mc(e);let r,o=ft(t),i=_e(void 0);try{return n()}finally{ft(o),_e(i)}}function fh(){return th()!==void 0||tc()!=null}var $e=0,N=1,M=2,re=3,Pe=4,pe=5,rr=6,or=7,fe=8,pn=9,nt=10,Z=11,ir=12,eu=13,gn=14,Ee=15,Vt=16,mn=17,rt=18,Xr=19,tu=20,mt=21,Ji=22,eo=23,Me=24,Xi=25,ge=26,hh=1;var jt=7,to=8,vn=9,me=10;function ot(e){return Array.isArray(e)&&typeof e[hh]=="object"}function ze(e){return Array.isArray(e)&&e[hh]===!0}function es(e){return(e.flags&4)!==0}function Bt(e){return e.componentOffset>-1}function no(e){return(e.flags&1)===1}function it(e){return!!e.template}function sr(e){return(e[M]&512)!==0}function yn(e){return(e[M]&256)===256}var ph="svg",gh="math";function Le(e){for(;Array.isArray(e);)e=e[$e];return e}function nu(e,n){return Le(n[e])}function Ge(e,n){return Le(n[e.index])}function ts(e,n){return e.data[n]}function mh(e,n){return e[n]}function Ve(e,n){let t=n[e];return ot(t)?t:t[$e]}function vh(e){return(e[M]&4)===4}function ns(e){return(e[M]&128)===128}function yh(e){return ze(e[re])}function Dn(e,n){return n==null?null:e[n]}function ru(e){e[mn]=0}function ou(e){e[M]&1024||(e[M]|=1024,ns(e)&&oo(e))}function Dh(e,n){for(;e>0;)n=n[gn],e--;return n}function ro(e){return!!(e[M]&9216||e[Me]?.dirty)}function rs(e){e[nt].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),ro(e)&&oo(e)}function oo(e){e[nt].changeDetectionScheduler?.notify(0);let n=Ft(e);for(;n!==null&&!(n[M]&8192||(n[M]|=8192,!ns(n)));)n=Ft(n)}function iu(e,n){if(yn(e))throw new y(911,!1);e[mt]===null&&(e[mt]=[]),e[mt].push(n)}function Ch(e,n){if(e[mt]===null)return;let t=e[mt].indexOf(n);t!==-1&&e[mt].splice(t,1)}function Ft(e){let n=e[re];return ze(n)?n[re]:n}function su(e){return e[or]??=[]}function au(e){return e.cleanup??=[]}function Eh(e,n,t,r){let o=su(n);o.push(t),e.firstCreatePass&&au(e).push(r,o.length-1)}var x={lFrame:jh(null),bindingsEnabled:!0,skipHydrationRootTNode:null},io=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(io||{}),LD=0,Rc=!1;function Ih(){return x.lFrame.elementDepthCount}function wh(){x.lFrame.elementDepthCount++}function _h(){x.lFrame.elementDepthCount--}function os(){return x.bindingsEnabled}function cu(){return x.skipHydrationRootTNode!==null}function bh(e){return x.skipHydrationRootTNode===e}function Sh(){x.skipHydrationRootTNode=null}function L(){return x.lFrame.lView}function X(){return x.lFrame.tView}function Mh(e){return x.lFrame.contextLView=e,e[fe]}function Th(e){return x.lFrame.contextLView=null,e}function ce(){let e=uu();for(;e!==null&&e.type===64;)e=e.parent;return e}function uu(){return x.lFrame.currentTNode}function Ah(){let e=x.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function Ht(e,n){let t=x.lFrame;t.currentTNode=e,t.isParent=n}function is(){return x.lFrame.isParent}function ss(){x.lFrame.isParent=!1}function Nh(){return x.lFrame.contextLView}function lu(e){Xf("Must never be called in production mode"),LD=e}function du(){return Rc}function fu(e){let n=Rc;return Rc=e,n}function Rh(){let e=x.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function xh(){return x.lFrame.bindingIndex}function Oh(e){return x.lFrame.bindingIndex=e}function so(){return x.lFrame.bindingIndex++}function hu(e){let n=x.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function kh(){return x.lFrame.inI18n}function Fh(e,n){let t=x.lFrame;t.bindingIndex=t.bindingRootIndex=e,as(n)}function Ph(){return x.lFrame.currentDirectiveIndex}function as(e){x.lFrame.currentDirectiveIndex=e}function Lh(e){let n=x.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function pu(){return x.lFrame.currentQueryIndex}function cs(e){x.lFrame.currentQueryIndex=e}function VD(e){let n=e[N];return n.type===2?n.declTNode:n.type===1?e[pe]:null}function gu(e,n,t){if(t&4){let o=n,i=e;for(;o=o.parent,o===null&&!(t&1);)if(o=VD(i),o===null||(i=i[gn],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=x.lFrame=Vh();return r.currentTNode=n,r.lView=e,!0}function us(e){let n=Vh(),t=e[N];x.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function Vh(){let e=x.lFrame,n=e===null?null:e.child;return n===null?jh(e):n}function jh(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function Bh(){let e=x.lFrame;return x.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var mu=Bh;function ls(){let e=Bh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Hh(e){return(x.lFrame.contextLView=Dh(e,x.lFrame.contextLView))[fe]}function Ut(){return x.lFrame.selectedIndex}function $t(e){x.lFrame.selectedIndex=e}function ds(){let e=x.lFrame;return ts(e.tView,e.selectedIndex)}function Uh(){return x.lFrame.currentNamespace}var $h=!0;function ao(){return $h}function co(e){$h=e}function xc(e,n=null,t=null,r){let o=vu(e,n,t,r);return o.resolveInjectorInitializers(),o}function vu(e,n=null,t=null,r,o=new Set){let i=[t||be,ch(e)];return r=r||(typeof e=="object"?void 0:Ce(e)),new ln(i,n||nr(),r||null,o)}var de=class e{static THROW_IF_NOT_FOUND=sn;static NULL=new Zr;static create(n,t){if(Array.isArray(n))return xc({name:""},t,n,"");{let r=n.name??"";return xc({name:r},n.parent,n.providers,r)}}static \u0275prov=D({token:e,providedIn:"any",factory:()=>I(qc)});static __NG_ELEMENT_ID__=-1},te=new C(""),ar=(()=>{class e{static __NG_ELEMENT_ID__=jD;static __NG_ENV_ID__=t=>t}return e})(),Oc=class extends ar{_lView;constructor(n){super(),this._lView=n}get destroyed(){return yn(this._lView)}onDestroy(n){let t=this._lView;return iu(t,n),()=>Ch(t,n)}};function jD(){return new Oc(L())}var Xe=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Te=new C("",{providedIn:"root",factory:()=>{let e=p(q),n;return t=>{e.destroyed&&!n?setTimeout(()=>{throw t}):(n??=e.get(Xe),n.handleError(t))}}}),zh={provide:Lt,useValue:()=>void p(Xe),multi:!0};function yu(e){return typeof e=="function"&&e[ue]!==void 0}function vt(e,n){let[t,r,o]=sc(e,n?.equal),i=t,s=i[ue];return i.set=r,i.update=o,i.asReadonly=Gh.bind(i),i}function Gh(){let e=this[ue];if(e.readonlyFn===void 0){let n=()=>this();n[ue]=e,e.readonlyFn=n}return e.readonlyFn}function Du(e){return yu(e)&&typeof e.set=="function"}var dn=class{},fs=new C("",{providedIn:"root",factory:()=>!1});var Cu=new C(""),Eu=new C("");var zt=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new J(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new k(t=>{t.next(!1),t.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();function uo(...e){}var Iu=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new kc})}return e})(),kc=class{dirtyEffectCount=0;queues=new Map;add(n){this.enqueue(n),this.schedule(n)}schedule(n){n.dirty&&this.dirtyEffectCount++}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),n.dirty&&this.dirtyEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||r.add(n)}flush(){for(;this.dirtyEffectCount>0;){let n=!1;for(let[t,r]of this.queues)t===null?n||=this.flushQueue(r):n||=t.run(()=>this.flushQueue(r));n||(this.dirtyEffectCount=0)}}flushQueue(n){let t=!1;for(let r of n)r.dirty&&(this.dirtyEffectCount--,t=!0,r.run());return t}};function fr(e){return{toString:e}.toString()}var hs="__parameters__";function QD(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function Cp(e,n,t){return fr(()=>{let r=QD(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(hs)?c[hs]:Object.defineProperty(c,hs,{value:[]})[hs];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Ep=Gc(Cp("Optional"),8);var Ip=Gc(Cp("SkipSelf"),4);function KD(e){return typeof e=="function"}var Cs=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function wp(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var Ae=(()=>{let e=()=>_p;return e.ngInherit=!0,e})();function _p(e){return e.type.prototype.ngOnChanges&&(e.setInput=XD),JD}function JD(){let e=Sp(this),n=e?.current;if(n){let t=e.previous;if(t===Pt)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function XD(e,n,t,r,o){let i=this.declaredInputs[r],s=Sp(e)||eC(e,{previous:Pt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Cs(u&&u.currentValue,t,c===Pt),wp(e,n,o,t)}var bp="__ngSimpleChanges__";function Sp(e){return e[bp]||null}function eC(e,n){return e[bp]=n}var Wh=[];var H=function(e,n=null,t){for(let r=0;r<Wh.length;r++){let o=Wh[r];o(e,n,t)}};function tC(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=_p(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function sl(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),u&&((e.viewHooks??=[]).push(t,u),(e.viewCheckHooks??=[]).push(t,u)),l!=null&&(e.destroyHooks??=[]).push(t,l)}}function ms(e,n,t){Mp(e,n,3,t)}function vs(e,n,t,r){(e[M]&3)===t&&Mp(e,n,t,r)}function wu(e,n){let t=e[M];(t&3)===n&&(t&=16383,t+=1,e[M]=t)}function Mp(e,n,t,r){let o=r!==void 0?e[mn]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[mn]+=65536),(a<i||i==-1)&&(nC(e,t,n,c),e[mn]=(e[mn]&**********)+c+2),c++}function qh(e,n){H(4,e,n);let t=A(null);try{n.call(e)}finally{A(t),H(5,e,n)}}function nC(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[M]>>14<e[mn]>>16&&(e[M]&3)===n&&(e[M]+=16384,qh(a,i)):qh(a,i)}var ur=-1,En=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r){this.factory=n,this.canSeeViewProviders=t,this.injectImpl=r}};function rC(e){return(e.flags&8)!==0}function oC(e){return(e.flags&16)!==0}function iC(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];sC(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function Tp(e){return e===3||e===4||e===6}function sC(e){return e.charCodeAt(0)===64}function lr(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?Zh(e,t,o,null,n[++r]):Zh(e,t,o,null,null))}}return e}function Zh(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function Ap(e){return e!==ur}function Es(e){return e&32767}function aC(e){return e>>16}function Is(e,n){let t=aC(e),r=n;for(;t>0;)r=r[gn],t--;return r}var Ou=!0;function Yh(e){let n=Ou;return Ou=e,n}var cC=256,Np=cC-1,Rp=5,uC=0,st={};function lC(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(fn)&&(r=t[fn]),r==null&&(r=t[fn]=uC++);let o=r&Np,i=1<<o;n.data[e+(o>>Rp)]|=i}function ws(e,n){let t=xp(e,n);if(t!==-1)return t;let r=n[N];r.firstCreatePass&&(e.injectorIndex=n.length,_u(r.data,e),_u(n,null),_u(r.blueprint,null));let o=al(e,n),i=e.injectorIndex;if(Ap(o)){let s=Es(o),a=Is(o,n),c=a[N].data;for(let u=0;u<8;u++)n[i+u]=a[s+u]|c[s+u]}return n[i+8]=o,i}function _u(e,n){e.push(0,0,0,0,0,0,0,0,n)}function xp(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function al(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=Lp(o),r===null)return ur;if(t++,o=o[gn],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return ur}function ku(e,n,t){lC(e,n,t)}function dC(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(Tp(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function Op(e,n,t){if(t&8||e!==void 0)return e;Zi(n,"NodeInjector")}function kp(e,n,t,r){if(t&8&&r===void 0&&(r=null),(t&3)===0){let o=e[pn],i=_e(void 0);try{return o?o.get(n,r,t&8):zc(n,r,t&8)}finally{_e(i)}}return Op(r,n,t)}function Fp(e,n,t,r=0,o){if(e!==null){if(n[M]&2048&&!(r&2)){let s=gC(e,n,t,r,st);if(s!==st)return s}let i=Pp(e,n,t,r,st);if(i!==st)return i}return kp(n,t,r,o)}function Pp(e,n,t,r,o){let i=hC(t);if(typeof i=="function"){if(!gu(n,e,r))return r&1?Op(o,t,r):kp(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Zi(t);else return s}finally{mu()}}else if(typeof i=="number"){let s=null,a=xp(e,n),c=ur,u=r&1?n[Ee][pe]:null;for((a===-1||r&4)&&(c=a===-1?al(e,n):n[a+8],c===ur||!Kh(r,!1)?a=-1:(s=n[N],a=Es(c),n=Is(c,n)));a!==-1;){let l=n[N];if(Qh(i,a,l.data)){let d=fC(a,n,t,s,r,u);if(d!==st)return d}c=n[a+8],c!==ur&&Kh(r,n[N].data[a+8]===u)&&Qh(i,a,n)?(s=l,a=Es(c),n=Is(c,n)):a=-1}}return o}function fC(e,n,t,r,o,i){let s=n[N],a=s.data[e+8],c=r==null?Bt(a)&&Ou:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=ys(a,s,t,c,u);return l!==null?ho(n,s,l,a):st}function ys(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let m=s[f];if(f<c&&t===m||f>=c&&m.type===t)return f}if(o){let f=s[c];if(f&&it(f)&&f.type===t)return c}return null}function ho(e,n,t,r){let o=e[t],i=n.data;if(o instanceof En){let s=o;s.resolving&&$c(eh(i[t]));let a=Yh(s.canSeeViewProviders);s.resolving=!0;let c=i[t].type||i[t],u,l=s.injectImpl?_e(s.injectImpl):null,d=gu(e,r,0);try{o=e[t]=s.factory(void 0,i,e,r),n.firstCreatePass&&t>=r.directiveStart&&tC(t,i[t],n)}finally{l!==null&&_e(l),Yh(a),s.resolving=!1,mu()}}return o}function hC(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(fn)?e[fn]:void 0;return typeof n=="number"?n>=0?n&Np:pC:n}function Qh(e,n,t){let r=1<<e;return!!(t[n+(e>>Rp)]&r)}function Kh(e,n){return!(e&2)&&!(e&1&&n)}var Cn=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return Fp(this._tNode,this._lView,n,an(r),t)}};function pC(){return new Cn(ce(),L())}function ct(e){return fr(()=>{let n=e.prototype.constructor,t=n[qr]||Fu(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[qr]||Fu(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Fu(e){return Pc(e)?()=>{let n=Fu(ae(e));return n&&n()}:cn(e)}function gC(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[M]&2048&&!sr(s);){let a=Pp(i,s,t,r|2,st);if(a!==st)return a;let c=i.parent;if(!c){let u=s[tu];if(u){let l=u.get(t,st,r);if(l!==st)return l}c=Lp(s),s=s[gn]}i=c}return o}function Lp(e){let n=e[N],t=n.type;return t===2?n.declTNode:t===1?e[pe]:null}function qt(e){return dC(ce(),e)}function mC(){return hr(ce(),L())}function hr(e,n){return new Q(Ge(e,n))}var Q=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=mC}return e})();function vC(e){return e instanceof Q?e.nativeElement:e}function yC(){return this._results[Symbol.iterator]()}var _s=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new B}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=rh(n);(this._changesDetected=!nh(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=yC};function Vp(e){return(e.flags&128)===128}var cl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(cl||{}),jp=new Map,DC=0;function CC(){return DC++}function EC(e){jp.set(e[Xr],e)}function Pu(e){jp.delete(e[Xr])}var Jh="__ngContext__";function pr(e,n){ot(n)?(e[Jh]=n[Xr],EC(n)):e[Jh]=n}function Bp(e){return Up(e[ir])}function Hp(e){return Up(e[Pe])}function Up(e){for(;e!==null&&!ze(e);)e=e[Pe];return e}var Lu;function ul(e){Lu=e}function $p(){if(Lu!==void 0)return Lu;if(typeof document<"u")return document;throw new y(210,!1)}var Ps=new C("",{providedIn:"root",factory:()=>IC}),IC="ng",Ls=new C(""),gr=new C("",{providedIn:"platform",factory:()=>"unknown"});var Vs=new C("",{providedIn:"root",factory:()=>$p().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var wC="h",_C="b";var zp=!1,Gp=new C("",{providedIn:"root",factory:()=>zp});var bC=(e,n,t,r)=>{};function SC(e,n,t,r){bC(e,n,t,r)}var MC=()=>null;function Wp(e,n,t=!1){return MC(e,n,t)}function qp(e,n){let t=e.contentQueries;if(t!==null){let r=A(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];cs(i),a.contentQueries(2,n[s],s)}}}finally{A(r)}}}function Vu(e,n,t){cs(0);let r=A(null);try{n(e,t)}finally{A(r)}}function ll(e,n,t){if(es(n)){let r=A(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{A(r)}}}var yt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(yt||{});var ps;function TC(){if(ps===void 0&&(ps=null,Yr.trustedTypes))try{ps=Yr.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ps}function Xh(e){return TC()?.createScriptURL(e)||e}var bs=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Gi})`}};function js(e){return e instanceof bs?e.changingThisBreaksApplicationSecurity:e}function dl(e,n){let t=Zp(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${Gi})`)}return t===n}function Zp(e){return e instanceof bs&&e.getTypeName()||null}var AC=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Yp(e){return e=String(e),e.match(AC)?e:"unsafe:"+e}var Bs=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Bs||{});function Qp(e){let n=Jp();return n?n.sanitize(Bs.URL,e)||"":dl(e,"URL")?js(e):Yp(hn(e))}function Kp(e){let n=Jp();if(n)return Xh(n.sanitize(Bs.RESOURCE_URL,e)||"");if(dl(e,"ResourceURL"))return Xh(js(e));throw new y(904,!1)}function NC(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?Kp:Qp}function fl(e,n,t){return NC(n,t)(e)}function Jp(){let e=L();return e&&e[nt].sanitizer}var RC=/^>|^->|<!--|-->|--!>|<!-$/g,xC=/(<|>)/g,OC="\u200B$1\u200B";function kC(e){return e.replace(RC,n=>n.replace(xC,OC))}function Xp(e){return e instanceof Function?e():e}function FC(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var eg="ng-template";function PC(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&FC(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(hl(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function hl(e){return e.type===4&&e.value!==eg}function LC(e,n,t){let r=e.type===4&&!t?eg:e.value;return n===r}function VC(e,n,t){let r=4,o=e.attrs,i=o!==null?HC(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!We(r)&&!We(c))return!1;if(s&&We(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!LC(e,c,t)||c===""&&n.length===1){if(We(r))return!1;s=!0}}else if(r&8){if(o===null||!PC(e,o,c,t)){if(We(r))return!1;s=!0}}else{let u=n[++a],l=jC(c,o,hl(e),t);if(l===-1){if(We(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(We(r))return!1;s=!0}}}}return We(r)||s}function We(e){return(e&1)===0}function jC(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return UC(n,e)}function tg(e,n,t=!1){for(let r=0;r<n.length;r++)if(VC(e,n[r],t))return!0;return!1}function BC(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function HC(e){for(let n=0;n<e.length;n++){let t=e[n];if(Tp(t))return n}return e.length}function UC(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function $C(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function ep(e,n){return e?":not("+n.trim()+")":n}function zC(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!We(s)&&(n+=ep(i,o),o=""),r=s,i=i||!We(r);t++}return o!==""&&(n+=ep(i,o)),n}function GC(e){return e.map(zC).join(",")}function WC(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!We(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var Ze={};function qC(e,n){return e.createText(n)}function ZC(e,n,t){e.setValue(n,t)}function YC(e,n){return e.createComment(kC(n))}function ng(e,n,t){return e.createElement(n,t)}function Ss(e,n,t,r,o){e.insertBefore(n,t,r,o)}function rg(e,n,t){e.appendChild(n,t)}function tp(e,n,t,r,o){r!==null?Ss(e,n,t,r,o):rg(e,n,t)}function QC(e,n,t){e.removeChild(null,n,t)}function KC(e,n,t){e.setAttribute(n,"style",t)}function JC(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function og(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&iC(e,n,r),o!==null&&JC(e,n,o),i!==null&&KC(e,n,i)}function pl(e,n,t,r,o,i,s,a,c,u,l){let d=ge+r,h=d+o,f=XC(d,h),m=typeof u=="function"?u():u;return f[N]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:m,incompleteFirstPass:!1,ssrId:l}}function XC(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:Ze);return t}function eE(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=pl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function gl(e,n,t,r,o,i,s,a,c,u,l){let d=n.blueprint.slice();return d[$e]=o,d[M]=r|4|128|8|64|1024,(u!==null||e&&e[M]&2048)&&(d[M]|=2048),ru(d),d[re]=d[gn]=e,d[fe]=t,d[nt]=s||e&&e[nt],d[Z]=a||e&&e[Z],d[pn]=c||e&&e[pn]||null,d[pe]=i,d[Xr]=CC(),d[rr]=l,d[tu]=u,d[Ee]=n.type==2?e[Ee]:d,d}function tE(e,n,t){let r=Ge(n,e),o=eE(t),i=e[nt].rendererFactory,s=ml(e,gl(e,o,null,ig(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function ig(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function sg(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function ml(e,n){return e[ir]?e[eu][Pe]=n:e[ir]=n,e[eu]=n,n}function nE(e=1){ag(X(),L(),Ut()+e,!1)}function ag(e,n,t,r){if(!r)if((n[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ms(n,i,t)}else{let i=e.preOrderHooks;i!==null&&vs(n,i,0,t)}$t(t)}var Hs=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Hs||{});function ju(e,n,t,r){let o=A(null);try{let[i,s,a]=e.inputs[t],c=null;(s&Hs.SignalBased)!==0&&(c=n[i][ue]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):wp(n,c,i,r)}finally{A(o)}}function cg(e,n,t,r,o){let i=Ut(),s=r&2;try{$t(-1),s&&n.length>ge&&ag(e,n,ge,!1),H(s?2:0,o,t),t(r,o)}finally{$t(i),H(s?3:1,o,t)}}function Us(e,n,t){uE(e,n,t),(t.flags&64)===64&&lE(e,n,t)}function vl(e,n,t=Ge){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function rE(e,n,t,r){let i=r.get(Gp,zp)||t===yt.ShadowDom,s=e.selectRootElement(n,i);return oE(s),s}function oE(e){iE(e)}var iE=()=>null;function sE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function ug(e,n,t,r,o,i){let s=n[N];if(Dl(e,s,n,t,r)){Bt(e)&&cE(n,e.index);return}aE(e,n,t,r,o,i)}function aE(e,n,t,r,o,i){if(e.type&3){let s=Ge(e,n);t=sE(t),r=i!=null?i(r,e.value||"",t):r,o.setProperty(s,t,r)}else e.type&12}function cE(e,n){let t=Ve(n,e);t[M]&16||(t[M]|=64)}function uE(e,n,t){let r=t.directiveStart,o=t.directiveEnd;Bt(t)&&tE(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||ws(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=ho(n,e,s,t);if(pr(c,n),i!==null&&pE(n,s-r,c,a,t,i),it(a)){let u=Ve(t.index,n);u[fe]=ho(n,e,s,t)}}}function lE(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=Ph();try{$t(i);for(let a=r;a<o;a++){let c=e.data[a],u=n[a];as(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&dE(c,u)}}finally{$t(-1),as(s)}}function dE(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function yl(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];tg(n,i.selectors,!1)&&(r??=[],it(i)?r.unshift(i):r.push(i))}return r}function fE(e,n,t,r,o,i){let s=Ge(e,n);hE(n[Z],s,i,e.value,t,r,o)}function hE(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?hn(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function pE(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];ju(r,t,c,u)}}function gE(e,n){let t=e[pn];if(!t)return;t.get(Te,null)?.(n)}function Dl(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=n.data[u];ju(d,t[u],l,o),a=!0}if(i)for(let c of i){let u=t[c],l=n.data[c];ju(l,u,r,o),a=!0}return a}function mE(e,n){let t=Ve(n,e),r=t[N];vE(r,t);let o=t[$e];o!==null&&t[rr]===null&&(t[rr]=Wp(o,t[pn])),H(18),Cl(r,t,t[fe]),H(19,t[fe])}function vE(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function Cl(e,n,t){us(n);try{let r=e.viewQuery;r!==null&&Vu(1,r,t);let o=e.template;o!==null&&cg(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[rt]?.finishViewCreation(e),e.staticContentQueries&&qp(e,n),e.staticViewQueries&&Vu(2,e.viewQuery,t);let i=e.components;i!==null&&yE(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[M]&=-5,ls()}}function yE(e,n){for(let t=0;t<n.length;t++)mE(e,n[t])}function lg(e,n,t,r){let o=A(null);try{let i=n.tView,a=e[M]&4096?4096:16,c=gl(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[n.index];c[Vt]=u;let l=e[rt];return l!==null&&(c[rt]=l.createEmbeddedView(i)),Cl(i,c,t),c}finally{A(o)}}function Bu(e,n){return!n||n.firstChild===null||Vp(e)}var np=!1,DE=new C(""),CE;function El(e,n){return CE(e,n)}var at=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(at||{});function $s(e){return(e.flags&32)===32}function cr(e,n,t,r,o){if(r!=null){let i,s=!1;ze(r)?i=r:ot(r)&&(s=!0,r=r[$e]);let a=Le(r);e===0&&t!==null?o==null?rg(n,t,a):Ss(n,t,a,o||null,!0):e===1&&t!==null?Ss(n,t,a,o||null,!0):e===2?QC(n,a,s):e===3&&n.destroyNode(a),i!=null&&NE(n,e,i,t,o)}}function EE(e,n){dg(e,n),n[$e]=null,n[pe]=null}function IE(e,n,t,r,o,i){r[$e]=o,r[pe]=n,Gs(e,r,t,1,o,i)}function dg(e,n){n[nt].changeDetectionScheduler?.notify(9),Gs(e,n,n[Z],2,null,null)}function wE(e){let n=e[ir];if(!n)return bu(e[N],e);for(;n;){let t=null;if(ot(n))t=n[ir];else{let r=n[me];r&&(t=r)}if(!t){for(;n&&!n[Pe]&&n!==e;)ot(n)&&bu(n[N],n),n=n[re];n===null&&(n=e),ot(n)&&bu(n[N],n),t=n&&n[Pe]}n=t}}function Il(e,n){let t=e[vn],r=t.indexOf(n);t.splice(r,1)}function fg(e,n){if(yn(n))return;let t=n[Z];t.destroyNode&&Gs(e,n,t,3,null,null),wE(n)}function bu(e,n){if(yn(n))return;let t=A(null);try{n[M]&=-129,n[M]|=256,n[Me]&&pi(n[Me]),bE(e,n),_E(e,n),n[N].type===1&&n[Z].destroy();let r=n[Vt];if(r!==null&&ze(n[re])){r!==n[re]&&Il(r,n);let o=n[rt];o!==null&&o.detachView(e)}Pu(n)}finally{A(t)}}function _E(e,n){let t=e.cleanup,r=n[or];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[or]=null);let o=n[mt];if(o!==null){n[mt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[eo];if(i!==null){n[eo]=null;for(let s of i)s.destroy()}}function bE(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof En)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];H(4,a,c);try{c.call(a)}finally{H(5,a,c)}}else{H(4,o,i);try{i.call(o)}finally{H(5,o,i)}}}}}function hg(e,n,t){return SE(e,n.parent,t)}function SE(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[$e];if(Bt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===yt.None||o===yt.Emulated)return null}return Ge(r,t)}function pg(e,n,t){return TE(e,n,t)}function ME(e,n,t){return e.type&40?Ge(e,t):null}var TE=ME,rp;function zs(e,n,t,r){let o=hg(e,r,n),i=n[Z],s=r.parent||n[pe],a=pg(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)tp(i,o,t[c],a,!1);else tp(i,o,t,a,!1);rp!==void 0&&rp(i,r,n,t,o)}function lo(e,n){if(n!==null){let t=n.type;if(t&3)return Ge(n,e);if(t&4)return Hu(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return lo(e,r);{let o=e[n.index];return ze(o)?Hu(-1,o):Le(o)}}else{if(t&128)return lo(e,n.next);if(t&32)return El(n,e)()||Le(e[n.index]);{let r=gg(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=Ft(e[Ee]);return lo(o,r)}else return lo(e,n.next)}}}return null}function gg(e,n){if(n!==null){let r=e[Ee][pe],o=n.projection;return r.projection[o]}return null}function Hu(e,n){let t=me+e+1;if(t<n.length){let r=n[t],o=r[N].firstChild;if(o!==null)return lo(r,o)}return n[jt]}function wl(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&pr(Le(a),r),t.flags|=2),!$s(t))if(c&8)wl(e,n,t.child,r,o,i,!1),cr(n,e,o,a,i);else if(c&32){let u=El(t,r),l;for(;l=u();)cr(n,e,o,l,i);cr(n,e,o,a,i)}else c&16?mg(e,n,r,t,o,i):cr(n,e,o,a,i);t=s?t.projectionNext:t.next}}function Gs(e,n,t,r,o,i){wl(t,r,e.firstChild,n,o,i,!1)}function AE(e,n,t){let r=n[Z],o=hg(e,t,n),i=t.parent||n[pe],s=pg(i,t,n);mg(r,0,n,t,o,s)}function mg(e,n,t,r,o,i){let s=t[Ee],c=s[pe].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];cr(n,e,o,l,i)}else{let u=c,l=s[re];Vp(r)&&(u.flags|=128),wl(e,n,u,l,o,i,!0)}}function NE(e,n,t,r,o){let i=t[jt],s=Le(t);i!==s&&cr(n,e,r,i,o);for(let a=me;a<t.length;a++){let c=t[a];Gs(c[N],c,e,n,r,i)}}function RE(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:at.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=at.Important),e.setStyle(t,r,o,i))}}function po(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(Le(i)),ze(i)&&vg(i,r);let s=t.type;if(s&8)po(e,n,t.child,r);else if(s&32){let a=El(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=gg(n,t);if(Array.isArray(a))r.push(...a);else{let c=Ft(n[Ee]);po(c[N],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function vg(e,n){for(let t=me;t<e.length;t++){let r=e[t],o=r[N].firstChild;o!==null&&po(r[N],r,o,n)}e[jt]!==e[$e]&&n.push(e[jt])}function yg(e){if(e[Xi]!==null){for(let n of e[Xi])n.impl.addSequence(n);e[Xi].length=0}}var Dg=[];function xE(e){return e[Me]??OE(e)}function OE(e){let n=Dg.pop()??Object.create(FE);return n.lView=e,n}function kE(e){e.lView[Me]!==e&&(e.lView=null,Dg.push(e))}var FE=T(g({},en),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{oo(e.lView)},consumerOnSignalRead(){this.lView[Me]=this}});function PE(e){let n=e[Me]??Object.create(LE);return n.lView=e,n}var LE=T(g({},en),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=Ft(e.lView);for(;n&&!Cg(n[N]);)n=Ft(n);n&&ou(n)},consumerOnSignalRead(){this.lView[Me]=this}});function Cg(e){return e.type!==2}function Eg(e){if(e[eo]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[eo])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[M]&8192)}}var VE=100;function _l(e,n=0){let r=e[nt].rendererFactory,o=!1;o||r.begin?.();try{jE(e,n)}finally{o||r.end?.()}}function jE(e,n){let t=du();try{fu(!0),Uu(e,n);let r=0;for(;ro(e);){if(r===VE)throw new y(103,!1);r++,Uu(e,1)}}finally{fu(t)}}function Ig(e,n){lu(n?io.Exhaustive:io.OnlyDirtyViews);try{_l(e)}finally{lu(io.Off)}}function BE(e,n,t,r){if(yn(n))return;let o=n[M],i=!1,s=!1;us(n);let a=!0,c=null,u=null;i||(Cg(e)?(u=xE(n),c=tn(u)):fi()===null?(a=!1,u=PE(n),c=tn(u)):n[Me]&&(pi(n[Me]),n[Me]=null));try{ru(n),Oh(e.bindingStartIndex),t!==null&&cg(e,n,t,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&ms(n,f,null)}else{let f=e.preOrderHooks;f!==null&&vs(n,f,0,null),wu(n,0)}if(s||HE(n),Eg(n),wg(n,0),e.contentQueries!==null&&qp(e,n),!i)if(l){let f=e.contentCheckHooks;f!==null&&ms(n,f)}else{let f=e.contentHooks;f!==null&&vs(n,f,1),wu(n,1)}$E(e,n);let d=e.components;d!==null&&bg(n,d,0);let h=e.viewQuery;if(h!==null&&Vu(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&ms(n,f)}else{let f=e.viewHooks;f!==null&&vs(n,f,2),wu(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[Ji]){for(let f of n[Ji])f();n[Ji]=null}i||(yg(n),n[M]&=-73)}catch(l){throw i||oo(n),l}finally{u!==null&&(Vn(u,c),a&&kE(u)),ls()}}function wg(e,n){for(let t=Bp(e);t!==null;t=Hp(t))for(let r=me;r<t.length;r++){let o=t[r];_g(o,n)}}function HE(e){for(let n=Bp(e);n!==null;n=Hp(n)){if(!(n[M]&2))continue;let t=n[vn];for(let r=0;r<t.length;r++){let o=t[r];ou(o)}}}function UE(e,n,t){H(18);let r=Ve(n,e);_g(r,t),H(19,r[fe])}function _g(e,n){ns(e)&&Uu(e,n)}function Uu(e,n){let r=e[N],o=e[M],i=e[Me],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&jr(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)BE(r,e,r.template,e[fe]);else if(o&8192){let a=A(null);try{Eg(e),wg(e,1);let c=r.components;c!==null&&bg(e,c,1),yg(e)}finally{A(a)}}}function bg(e,n,t){for(let r=0;r<n.length;r++)UE(e,n[r],t)}function $E(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)$t(~o);else{let i=o,s=t[++r],a=t[++r];Fh(s,i);let c=n[i];H(24,c),a(2,c),H(25,c)}}}finally{$t(-1)}}function bl(e,n){let t=du()?64:1088;for(e[nt].changeDetectionScheduler?.notify(n);e;){e[M]|=t;let r=Ft(e);if(sr(e)&&!r)return e;e=r}return null}function Sg(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function Mg(e,n,t,r=!0){let o=n[N];if(zE(o,n,e,t),r){let s=Hu(t,e),a=n[Z],c=a.parentNode(e[jt]);c!==null&&IE(o,e[pe],a,n,c,s)}let i=n[rr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function $u(e,n){if(e.length<=me)return;let t=me+n,r=e[t];if(r){let o=r[Vt];o!==null&&o!==e&&Il(o,r),n>0&&(e[t-1][Pe]=r[Pe]);let i=Kr(e,me+n);EE(r[N],r);let s=i[rt];s!==null&&s.detachView(i[N]),r[re]=null,r[Pe]=null,r[M]&=-129}return r}function zE(e,n,t,r){let o=me+r,i=t.length;r>0&&(t[o-1][Pe]=n),r<i-me?(n[Pe]=t[o],Wc(t,me+r,n)):(t.push(n),n[Pe]=null),n[re]=t;let s=n[Vt];s!==null&&t!==s&&Tg(s,n);let a=n[rt];a!==null&&a.insertView(e),rs(n),n[M]|=128}function Tg(e,n){let t=e[vn],r=n[re];if(ot(r))e[M]|=2;else{let o=r[re][Ee];n[Ee]!==o&&(e[M]|=2)}t===null?e[vn]=[n]:t.push(n)}var Gt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let n=this._lView,t=n[N];return po(t,n,t.firstChild,[])}constructor(n,t){this._lView=n,this._cdRefInjectingView=t}get context(){return this._lView[fe]}set context(n){this._lView[fe]=n}get destroyed(){return yn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[re];if(ze(n)){let t=n[to],r=t?t.indexOf(this):-1;r>-1&&($u(n,r),Kr(t,r))}this._attachedToViewContainer=!1}fg(this._lView[N],this._lView)}onDestroy(n){iu(this._lView,n)}markForCheck(){bl(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){rs(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,_l(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[pn].get(DE,np)}catch{this.exhaustive=np}}attachToViewContainerRef(){if(this._appRef)throw new y(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=sr(this._lView),t=this._lView[Vt];t!==null&&!n&&Il(t,this._lView),dg(this._lView[N],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new y(902,!1);this._appRef=n;let t=sr(this._lView),r=this._lView[Vt];r!==null&&!t&&Tg(r,this._lView),rs(this._lView)}};var qe=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=GE;constructor(t,r,o){this._declarationLView=t,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,r){return this.createEmbeddedViewImpl(t,r)}createEmbeddedViewImpl(t,r,o){let i=lg(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:r,dehydratedView:o});return new Gt(i)}}return e})();function GE(){return Sl(ce(),L())}function Sl(e,n){return e.type&4?new qe(n,e,hr(e,n)):null}function vo(e,n,t,r,o){let i=e.data[n];if(i===null)i=WE(e,n,t,r,o),kh()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=Ah();i.injectorIndex=s===null?-1:s.injectorIndex}return Ht(i,!0),i}function WE(e,n,t,r,o){let i=uu(),s=is(),a=s?i:i&&i.parent,c=e.data[n]=ZE(e,a,t,n,r,o);return qE(e,c,i,s),c}function qE(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function ZE(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return cu()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var Gk=new RegExp(`^(\\d+)*(${_C}|${wC})*(.*)`);var YE=()=>null;function zu(e,n){return YE(e,n)}var Ag=class{},Ws=class{},Gu=class{resolveComponentFactory(n){throw new y(917,!1)}},yo=class{static NULL=new Gu},In=class{},Sn=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>QE()}return e})();function QE(){let e=L(),n=ce(),t=Ve(n.index,e);return(ot(t)?t:e)[Z]}var Ng=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>null})}return e})();var Ds={},Wu=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){let o=this.injector.get(n,Ds,r);return o!==Ds||t===Ds?o:this.parentInjector.get(n,t,r)}};function qu(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=Fc(o,a);else if(i==2){let c=a,u=n[++s];r=Fc(r,c+": "+u+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function v(e,n=0){let t=L();if(t===null)return I(e,n);let r=ce();return Fp(r,t,ae(e),n)}function Ml(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}XE(e,n,t,a,i,c,u)}i!==null&&r!==null&&KE(t,r,i)}function KE(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new y(-301,!1);r.push(n[o],i)}}function JE(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function XE(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&it(f)&&(c=!0,JE(e,t,h)),ku(ws(t,n),e,f.type)}iI(t,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=sg(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(t.mergedAttrs=lr(t.mergedAttrs,f.hostAttrs),tI(e,t,n,d,f),oI(d,f,o),s!==null&&s.has(f)){let[E,S]=s.get(f);t.directiveToIndex.set(f.type,[d,E+t.directiveStart,S+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let m=f.type.prototype;!u&&(m.ngOnChanges||m.ngOnInit||m.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),u=!0),!l&&(m.ngOnChanges||m.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),l=!0),d++}eI(e,t,i)}function eI(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))op(0,n,o,r),op(1,n,o,r),sp(n,r,!1);else{let i=t.get(o);ip(0,n,i,r),ip(1,n,i,r),sp(n,r,!0)}}}function op(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),Rg(n,i)}}function ip(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Rg(n,s)}}function Rg(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function sp(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||hl(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===n){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function tI(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=cn(o.type,!0)),s=new En(i,it(o),v);e.blueprint[r]=s,t[r]=s,nI(e,n,r,sg(e,t,o.hostVars,Ze),o)}function nI(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;rI(s)!=a&&s.push(a),s.push(t,r,i)}}function rI(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function oI(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;it(n)&&(t[""]=e)}}function iI(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function xg(e,n,t,r,o,i,s,a){let c=n.consts,u=Dn(c,s),l=vo(n,e,2,r,u);return i&&Ml(n,t,l,Dn(c,a),o),l.mergedAttrs=lr(l.mergedAttrs,l.attrs),l.attrs!==null&&qu(l,l.attrs,!1),l.mergedAttrs!==null&&qu(l,l.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,l),l}function Og(e,n){sl(e,n),es(n)&&e.queries.elementEnd(n)}function Tl(e){return Fg(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function kg(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function Fg(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function sI(e,n,t){return e[n]=t}function aI(e,n){return e[n]}function wn(e,n,t){if(t===Ze)return!1;let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function cI(e,n,t,r){let o=wn(e,n,t);return wn(e,n+1,r)||o}function Su(e,n,t){return function r(o){let i=Bt(e)?Ve(e.index,n):n;bl(i,5);let s=n[fe],a=ap(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=ap(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function ap(e,n,t,r){let o=A(null);try{return H(6,n,t),t(r)!==!1}catch(i){return gE(e,i),!1}finally{H(7,n,t),A(o)}}function uI(e,n,t,r,o,i,s,a){let c=no(e),u=!1,l=null;if(!r&&c&&(l=lI(n,t,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Ge(e,t),h=r?r(d):d;SC(t,h,i,a);let f=o.listen(h,i,a),m=r?E=>r(Le(E[e.index])):e.index;Pg(m,n,t,i,a,f,!1)}return u}function lI(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[or],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Pg(e,n,t,r,o,i,s){let a=n.firstCreatePass?au(n):null,c=su(t),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function cp(e,n,t,r,o,i){let s=n[t],a=n[N],u=a.data[t].outputs[r],d=s[u].subscribe(i);Pg(e.index,a,n,o,i,d,!0)}var Zu=Symbol("BINDING");var Ms=class extends yo{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=tt(n);return new Wt(t,this.ngModule)}};function dI(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&Hs.SignalBased)!==0};return o&&(i.transform=o),i})}function fI(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function hI(e,n,t){let r=n instanceof q?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Wu(t,r):t}function pI(e){let n=e.get(In,null);if(n===null)throw new y(407,!1);let t=e.get(Ng,null),r=e.get(dn,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r,ngReflect:!1}}function gI(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return ng(n,t,t==="svg"?ph:t==="math"?gh:null)}var Wt=class extends Ws{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=dI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=fI(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=GC(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o,i,s){H(22);let a=A(null);try{let c=this.componentDef,u=mI(r,c,s,i),l=hI(c,o||this.ngModule,n),d=pI(l),h=d.rendererFactory.createRenderer(null,c),f=r?rE(h,r,c.encapsulation,l):gI(c,h),m=s?.some(up)||i?.some(R=>typeof R!="function"&&R.bindings.some(up)),E=gl(null,u,null,512|ig(c),null,null,d,h,l,null,Wp(f,l,!0));E[ge]=f,us(E);let S=null;try{let R=xg(ge,u,E,"#host",()=>u.directiveRegistry,!0,0);f&&(og(h,f,R),pr(f,E)),Us(u,E,R),ll(u,R,E),Og(u,R),t!==void 0&&yI(R,this.ngContentSelectors,t),S=Ve(R.index,E),E[fe]=S[fe],Cl(u,E,null)}catch(R){throw S!==null&&Pu(S),Pu(E),R}finally{H(23),ls()}return new Ts(this.componentType,E,!!m)}finally{A(a)}}};function mI(e,n,t,r){let o=e?["ng-version","20.0.6"]:WC(n.selectors[0]),i=null,s=null,a=0;if(t)for(let l of t)a+=l[Zu].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[Zu].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[n];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=Qc(d);c.push(h)}return pl(0,null,vI(i,s),1,a,c,null,null,null,[o],null)}function vI(e,n){return!e&&!n?null:t=>{if(t&1&&e)for(let r of e)r.create();if(t&2&&n)for(let r of n)r.update()}}function up(e){let n=e[Zu].kind;return n==="input"||n==="twoWay"}var Ts=class extends Ag{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t,r){super(),this._rootLView=t,this._hasInputBindings=r,this._tNode=ts(t[N],ge),this.location=hr(this._tNode,t),this.instance=Ve(this._tNode.index,t)[fe],this.hostView=this.changeDetectorRef=new Gt(t,void 0),this.componentType=n}setInput(n,t){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=Dl(r,o[N],o,n,t);this.previousInputValues.set(n,t);let s=Ve(r.index,o);bl(s,1)}get injector(){return new Cn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function yI(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var je=(()=>{class e{static __NG_ELEMENT_ID__=DI}return e})();function DI(){let e=ce();return Vg(e,L())}var CI=je,Lg=class extends CI{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return hr(this._hostTNode,this._hostLView)}get injector(){return new Cn(this._hostTNode,this._hostLView)}get parentInjector(){let n=al(this._hostTNode,this._hostLView);if(Ap(n)){let t=Is(n,this._hostLView),r=Es(n),o=t[N].data[r+8];return new Cn(o,t)}else return new Cn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=lp(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-me}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=zu(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,Bu(this._hostTNode,s)),a}createComponent(n,t,r,o,i,s,a){let c=n&&!KD(n),u;if(c)u=t;else{let S=t||{};u=S.index,r=S.injector,o=S.projectableNodes,i=S.environmentInjector||S.ngModuleRef,s=S.directives,a=S.bindings}let l=c?n:new Wt(tt(n)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let R=(c?d:this.parentInjector).get(q,null);R&&(i=R)}let h=tt(l.componentType??{}),f=zu(this._lContainer,h?.id??null),m=f?.firstChild??null,E=l.create(d,o,m,i,s,a);return this.insertImpl(E.hostView,u,Bu(this._hostTNode,f)),E}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(yh(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[re],u=new Lg(c,c[pe],c[re]);u.detach(u.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return Mg(s,o,i,r),n.attachToViewContainerRef(),Wc(Mu(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=lp(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=$u(this._lContainer,t);r&&(Kr(Mu(this._lContainer),t),fg(r[N],r))}detach(n){let t=this._adjustIndex(n,-1),r=$u(this._lContainer,t);return r&&Kr(Mu(this._lContainer),t)!=null?new Gt(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function lp(e){return e[to]}function Mu(e){return e[to]||(e[to]=[])}function Vg(e,n){let t,r=n[e.index];return ze(r)?t=r:(t=Sg(r,n,null,e),n[e.index]=t,ml(n,t)),II(t,n,e,r),new Lg(t,e,n)}function EI(e,n){let t=e[Z],r=t.createComment(""),o=Ge(n,e),i=t.parentNode(o);return Ss(t,i,r,t.nextSibling(o),!1),r}var II=bI,wI=()=>!1;function _I(e,n,t){return wI(e,n,t)}function bI(e,n,t,r){if(e[jt])return;let o;t.type&8?o=Le(r):o=EI(n,t),e[jt]=o}var Yu=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Qu=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)Al(n,t).matches!==null&&this.queries[t].setDirty()}},As=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=OI(n):this.predicate=n}},Ku=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},Ju=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,SI(t,i)),this.matchTNodeWithReadOption(n,t,ys(t,n,i,!1,!1))}else r===qe?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,ys(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Q||o===je||o===qe&&t.type&4)this.addMatch(t.index,-2);else{let i=ys(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function SI(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function MI(e,n){return e.type&11?hr(e,n):e.type&4?Sl(e,n):null}function TI(e,n,t,r){return t===-1?MI(n,e):t===-2?AI(e,n,r):ho(e,e[N],t,n)}function AI(e,n,t){if(t===Q)return hr(n,e);if(t===qe)return Sl(n,e);if(t===je)return Vg(n,e)}function jg(e,n,t,r){let o=n[rt].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(TI(n,l,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function Xu(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=jg(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=n[-c];for(let d=me;d<l.length;d++){let h=l[d];h[Vt]===h[re]&&Xu(h[N],h,u,r)}if(l[vn]!==null){let d=l[vn];for(let h=0;h<d.length;h++){let f=d[h];Xu(f[N],f,u,r)}}}}}return r}function NI(e,n){return e[rt].queries[n].queryList}function Bg(e,n,t){let r=new _s((t&4)===4);return Eh(e,n,r,r.destroy),(n[rt]??=new Qu).queries.push(new Yu(r))-1}function RI(e,n,t){let r=X();return r.firstCreatePass&&(Hg(r,new As(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Bg(r,L(),n)}function xI(e,n,t,r){let o=X();if(o.firstCreatePass){let i=ce();Hg(o,new As(n,t,r),i.index),kI(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return Bg(o,L(),t)}function OI(e){return e.split(",").map(n=>n.trim())}function Hg(e,n,t){e.queries===null&&(e.queries=new Ku),e.queries.track(new Ju(n,t))}function kI(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function Al(e,n){return e.queries.getByIndex(n)}function FI(e,n){let t=e[N],r=Al(t,n);return r.crossesNgTemplate?Xu(t,e,n,[]):jg(t,e,r,n)}var dp=new Set;function Nl(e){dp.has(e)||(dp.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var _n=class{},qs=class{};var Ns=class extends _n{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ms(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=Yc(n);this._bootstrapComponents=Xp(i.bootstrap),this._r3Injector=vu(n,t,[{provide:_n,useValue:this},{provide:yo,useValue:this.componentFactoryResolver},...r],Ce(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},Rs=class extends qs{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new Ns(this.moduleType,n,[])}};var go=class extends _n{injector;componentFactoryResolver=new Ms(this);instance=null;constructor(n){super();let t=new ln([...n.providers,{provide:_n,useValue:this},{provide:yo,useValue:this.componentFactoryResolver}],n.parent||nr(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function mr(e,n,t=null){return new go({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var PI=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=Kc(!1,t.type),o=r.length>0?mr([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e(I(q))})}return e})();function Rl(e){return fr(()=>{let n=Ug(e),t=T(g({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===cl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(PI).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||yt.Emulated,styles:e.styles||be,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&Nl("NgStandalone"),$g(t);let r=e.dependencies;return t.directiveDefs=fp(r,!1),t.pipeDefs=fp(r,!0),t.id=HI(t),t})}function LI(e){return tt(e)||Qc(e)}function VI(e){return e!==null}function Dt(e){return fr(()=>({type:e.type,bootstrap:e.bootstrap||be,declarations:e.declarations||be,imports:e.imports||be,exports:e.exports||be,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function jI(e,n){if(e==null)return Pt;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Hs.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function BI(e){if(e==null)return Pt;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function V(e){return fr(()=>{let n=Ug(e);return $g(n),n})}function Ug(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||Pt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||be,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:jI(e.inputs,n),outputs:BI(e.outputs),debugInfo:null}}function $g(e){e.features?.forEach(n=>n(e))}function fp(e,n){if(!e)return null;let t=n?ah:LI;return()=>(typeof e=="function"?e():e).map(r=>t(r)).filter(VI)}function HI(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function UI(e){return Object.getPrototypeOf(e.prototype).constructor}function Ne(e){let n=UI(e.type),t=!0,r=[e];for(;n;){let o;if(it(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new y(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=Tu(e.inputs),s.declaredInputs=Tu(e.declaredInputs),s.outputs=Tu(e.outputs);let a=o.hostBindings;a&&qI(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&GI(e,c),u&&WI(e,u),$I(e,o),Jf(e.outputs,o.outputs),it(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Ne&&(t=!1)}}n=Object.getPrototypeOf(n)}zI(r)}function $I(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function zI(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=lr(o.hostAttrs,t=lr(t,o.hostAttrs))}}function Tu(e){return e===Pt?{}:e===be?[]:e}function GI(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function WI(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function qI(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function ZI(e,n,t,r,o,i,s,a,c){let u=n.consts,l=vo(n,e,4,s||null,a||null);os()&&Ml(n,t,l,Dn(u,c),yl),l.mergedAttrs=lr(l.mergedAttrs,l.attrs),sl(n,l);let d=l.tView=pl(2,l,r,o,i,n.directiveRegistry,n.pipeRegistry,null,n.schemas,u,null);return n.queries!==null&&(n.queries.template(n,l),d.queries=n.queries.embeddedTView(l)),l}function zg(e,n,t,r,o,i,s,a,c,u,l){let d=t+ge,h=n.firstCreatePass?ZI(d,n,e,r,o,i,s,a,u):n.data[d];c&&(h.flags|=c),Ht(h,!1);let f=YI(n,e,h,t);ao()&&zs(n,e,f,h),pr(f,e);let m=Sg(f,e,f,h);return e[d]=m,ml(e,m),_I(m,h,e),no(h)&&Us(n,e,h),u!=null&&vl(e,h,l),h}function Gg(e,n,t,r,o,i,s,a){let c=L(),u=X(),l=Dn(u.consts,i);return zg(c,u,e,n,t,r,o,l,void 0,s,a),Gg}var YI=QI;function QI(e,n,t,r){return co(!0),n[Z].createComment("")}var xl=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(xl||{}),Do=new C(""),Wg=!1,el=class extends B{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,fh()&&(this.destroyRef=p(ar,{optional:!0})??void 0,this.pendingTasks=p(zt,{optional:!0})??void 0)}emit(n){let t=A(null);try{super.next(n)}finally{A(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof K&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},U=el;function qg(e){let n,t;function r(){e=uo;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function hp(e){return queueMicrotask(()=>e()),()=>{e=uo}}var Ol="isAngularZone",xs=Ol+"_ID",KI=0,$=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new U(!1);onMicrotaskEmpty=new U(!1);onStable=new U(!1);onError=new U(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Wg}=n;if(typeof Zone>"u")throw new y(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,ew(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Ol)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new y(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new y(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,JI,uo,uo);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},JI={};function kl(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function XI(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){qg(()=>{e.callbackScheduled=!1,tl(e),e.isCheckStableRunning=!0,kl(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),tl(e)}function ew(e){let n=()=>{XI(e)},t=KI++;e._inner=e._inner.fork({name:"angular",properties:{[Ol]:!0,[xs]:t,[xs+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(tw(c))return r.invokeTask(i,s,a,c);try{return pp(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),gp(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return pp(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!nw(c)&&n(),gp(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,tl(e),kl(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function tl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function pp(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function gp(e){e._nesting--,kl(e)}var Os=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new U;onMicrotaskEmpty=new U;onStable=new U;onError=new U;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function tw(e){return Zg(e,"__ignore_ng_zone__")}function nw(e){return Zg(e,"__scheduler_tick__")}function Zg(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var Yg=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();var Fl=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Pl=new C("");function Zt(e){return!!e&&typeof e.then=="function"}function Ll(e){return!!e&&typeof e.subscribe=="function"}var Qg=new C("");var Vl=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=p(Qg,{optional:!0})??[];injector=p(de);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=Se(this.injector,o);if(Zt(i))t.push(i);else if(Ll(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Zs=new C("");function Kg(){ic(()=>{let e="";throw new y(600,e)})}function Jg(e){return e.isBoundToModule}var rw=10;var Ct=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Te);afterRenderManager=p(Yg);zonelessEnabled=p(fs);rootEffectScheduler=p(Iu);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new B;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(zt);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(P(t=>!t))}constructor(){p(Do,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=p(q);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=de.NULL){return this._injector.get($).run(()=>{H(10);let s=t instanceof Ws;if(!this._injector.get(Vl).done){let m="";throw new y(405,m)}let c;s?c=t:c=this._injector.get(yo).resolveComponentFactory(t),this.componentTypes.push(c.componentType);let u=Jg(c)?void 0:this._injector.get(_n),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(Pl,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),fo(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),H(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){H(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(xl.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new y(101,!1);let t=A(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,A(t),this.afterTick.next(),H(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(In,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<rw;)H(14),this.synchronizeOnce(),H(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let t=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!ro(o))continue;let i=r&&!this.zonelessEnabled?0:1;_l(o,i),t=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}t||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>ro(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;fo(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(t),this._injector.get(Zs,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>fo(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new y(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fo(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Et(e,n,t,r){let o=L(),i=so();if(wn(o,i,n)){let s=X(),a=ds();fE(a,o,e,n,t,r)}return Et}function Xg(e,n,t){let r=L(),o=so();if(wn(r,o,n)){let i=X(),s=ds();ug(s,r,e,n,r[Z],t)}return Xg}function mp(e,n,t,r,o){Dl(n,e,t,o?"class":"style",r)}function jl(e,n,t,r){let o=L(),i=X(),s=ge+e,a=o[Z],c=i.firstCreatePass?xg(s,i,o,n,yl,os(),t,r):i.data[s],u=ow(i,o,c,a,n,e);o[s]=u;let l=no(c);return Ht(c,!0),og(a,u,c),!$s(c)&&ao()&&zs(i,o,u,c),(Ih()===0||l)&&pr(u,o),wh(),l&&(Us(i,o,c),ll(i,c,o)),r!==null&&vl(o,c),jl}function Bl(){let e=ce();is()?ss():(e=e.parent,Ht(e,!1));let n=e;bh(n)&&Sh(),_h();let t=X();return t.firstCreatePass&&Og(t,n),n.classesWithoutHost!=null&&rC(n)&&mp(t,n,L(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&oC(n)&&mp(t,n,L(),n.stylesWithoutHost,!1),Bl}function Ys(e,n,t,r){return jl(e,n,t,r),Bl(),Ys}var ow=(e,n,t,r,o,i)=>(co(!0),ng(r,o,Uh()));function iw(e,n,t,r,o){let i=n.consts,s=Dn(i,r),a=vo(n,e,8,"ng-container",s);s!==null&&qu(a,s,!0);let c=Dn(i,o);return os()&&Ml(n,t,a,c,yl),a.mergedAttrs=lr(a.mergedAttrs,a.attrs),n.queries!==null&&n.queries.elementStart(n,a),a}function Hl(e,n,t){let r=L(),o=X(),i=e+ge,s=o.firstCreatePass?iw(i,o,r,n,t):o.data[i];Ht(s,!0);let a=sw(o,r,s,e);return r[i]=a,ao()&&zs(o,r,a,s),pr(a,r),no(s)&&(Us(o,r,s),ll(o,s,r)),t!=null&&vl(r,s),Hl}function Ul(){let e=ce(),n=X();return is()?ss():(e=e.parent,Ht(e,!1)),n.firstCreatePass&&(sl(n,e),es(e)&&n.queries.elementEnd(e)),Ul}function em(e,n,t){return Hl(e,n,t),Ul(),em}var sw=(e,n,t,r)=>(co(!0),YC(n[Z],""));function aw(){return L()}var Co="en-US";var cw=Co;function tm(e){typeof e=="string"&&(cw=e.toLowerCase().replace(/_/g,"-"))}function Re(e,n,t){let r=L(),o=X(),i=ce();return nm(o,r,r[Z],i,e,n,t),Re}function nm(e,n,t,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Su(r,n,i),uI(r,e,n,s,t,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=Su(r,n,i),cp(r,n,h,f,o,c)}if(u&&u.length)for(let d of u)c??=Su(r,n,i),cp(r,n,d,o,o,c)}}function uw(e=1){return Hh(e)}function lw(e,n){let t=null,r=BC(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?tg(e,i,!0):$C(r,i))return o}return t}function dw(e){let n=L()[Ee][pe];if(!n.projection){let t=e?e.length:1,r=n.projection=oh(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?lw(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function fw(e,n=0,t,r,o,i){let s=L(),a=X(),c=r?e+1:null;c!==null&&zg(s,a,c,r,o,i,null,t);let u=vo(a,ge+e,16,null,t||null);u.projection===null&&(u.projection=n),ss();let d=!s[rr]||cu();s[Ee][pe].projection[u.projection]===null&&c!==null?hw(s,a,c):d&&!$s(u)&&AE(a,s,u)}function hw(e,n,t){let r=ge+t,o=n.data[r],i=e[r],s=zu(i,o.tView.ssrId),a=lg(e,o,void 0,{dehydratedView:s});Mg(i,a,0,Bu(o,s))}function Eo(e,n,t,r){xI(e,n,t,r)}function $l(e,n,t){RI(e,n,t)}function vr(e){let n=L(),t=X(),r=pu();cs(r+1);let o=Al(t,r);if(e.dirty&&vh(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=FI(n,r);e.reset(i,vC),e.notifyOnChanges()}return!0}return!1}function yr(){return NI(L(),pu())}function pw(e){let n=Nh();return mh(n,ge+e)}function gs(e,n){return e<<17|n<<2}function bn(e){return e>>17&32767}function gw(e){return(e&2)==2}function mw(e,n){return e&131071|n<<17}function nl(e){return e|2}function dr(e){return(e&131068)>>2}function Au(e,n){return e&-131069|n<<2}function vw(e){return(e&1)===1}function rl(e){return e|1}function yw(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=bn(s),c=dr(s);e[r]=t;let u=!1,l;if(Array.isArray(t)){let d=t;l=d[1],(l===null||tr(d,l)>0)&&(u=!0)}else l=t;if(o)if(c!==0){let h=bn(e[a+1]);e[r+1]=gs(h,a),h!==0&&(e[h+1]=Au(e[h+1],r)),e[a+1]=mw(e[a+1],r)}else e[r+1]=gs(a,0),a!==0&&(e[a+1]=Au(e[a+1],r)),a=r;else e[r+1]=gs(c,0),a===0?a=r:e[c+1]=Au(e[c+1],r),c=r;u&&(e[r+1]=nl(e[r+1])),vp(e,l,r,!0),vp(e,l,r,!1),Dw(n,l,e,r,i),s=gs(a,c),i?n.classBindings=s:n.styleBindings=s}function Dw(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&tr(i,n)>=0&&(t[r+1]=rl(t[r+1]))}function vp(e,n,t,r){let o=e[t+1],i=n===null,s=r?bn(o):dr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];Cw(c,n)&&(a=!0,e[s+1]=r?rl(u):nl(u)),s=r?bn(u):dr(u)}a&&(e[t+1]=r?nl(o):rl(o))}function Cw(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?tr(e,n)>=0:!1}function Io(e,n){return Ew(e,n,null,!0),Io}function Ew(e,n,t,r){let o=L(),i=X(),s=hu(2);if(i.firstUpdatePass&&ww(i,e,s,r),n!==Ze&&wn(o,s,n)){let a=i.data[Ut()];Tw(i,a,o,o[Z],e,o[s+1]=Aw(n,t),r,s)}}function Iw(e,n){return n>=e.expandoStartIndex}function ww(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[Ut()],s=Iw(e,t);Nw(i,r)&&n===null&&!s&&(n=!1),n=_w(o,i,n,r),yw(o,i,n,t,s,r)}}function _w(e,n,t,r){let o=Lh(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=Nu(null,e,n,t,r),t=mo(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=Nu(o,e,n,t,r),i===null){let c=bw(e,n,r);c!==void 0&&Array.isArray(c)&&(c=Nu(null,e,n,c[1],r),c=mo(c,n.attrs,r),Sw(e,n,r,c))}else i=Mw(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function bw(e,n,t){let r=t?n.classBindings:n.styleBindings;if(dr(r)!==0)return e[bn(r)]}function Sw(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[bn(o)]=r}function Mw(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=mo(r,s,t)}return mo(r,n.attrs,t)}function Nu(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=mo(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function mo(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),sh(e,s,t?!0:n[++i]))}return e===void 0?null:e}function Tw(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,u=c[a+1],l=vw(u)?yp(c,n,t,o,dr(u),s):void 0;if(!ks(l)){ks(i)||gw(u)&&(i=yp(c,null,t,o,a,s));let d=nu(Ut(),t);RE(r,s,d,o,i)}}function yp(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=t[o+1];h===Ze&&(h=d?be:void 0);let f=d?Qi(h,r):l===r?h:void 0;if(u&&!ks(f)&&(f=Qi(c,r)),ks(f)&&(a=f,s))return a;let m=e[o+1];o=s?bn(m):dr(m)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=Qi(c,r))}return a}function ks(e){return e!==void 0}function Aw(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=Ce(js(e)))),e}function Nw(e,n){return(e.flags&(n?8:16))!==0}function Rw(e,n=""){let t=L(),r=X(),o=e+ge,i=r.firstCreatePass?vo(r,o,1,n,null):r.data[o],s=xw(r,t,i,n,e);t[o]=s,ao()&&zs(r,t,s,i),Ht(i,!1)}var xw=(e,n,t,r,o)=>(co(!0),qC(n[Z],r));function Ow(e,n,t,r=""){return wn(e,so(),t)?n+hn(t)+r:Ze}function kw(e,n,t,r,o,i=""){let s=xh(),a=cI(e,s,t,o);return hu(2),a?n+hn(t)+r+hn(o)+i:Ze}function rm(e){return zl("",e),rm}function zl(e,n,t){let r=L(),o=Ow(r,e,n,t);return o!==Ze&&im(r,Ut(),o),zl}function om(e,n,t,r,o){let i=L(),s=kw(i,e,n,t,r,o);return s!==Ze&&im(i,Ut(),s),om}function im(e,n,t){let r=nu(n,e);ZC(e[Z],r,t)}function sm(e,n,t){Du(n)&&(n=n());let r=L(),o=so();if(wn(r,o,n)){let i=X(),s=ds();ug(s,r,e,n,r[Z],t)}return sm}function Fw(e,n){let t=Du(e);return t&&e.set(n),t}function am(e,n){let t=L(),r=X(),o=ce();return nm(r,t,t[Z],o,e,n),am}function Pw(e,n,t){let r=X();if(r.firstCreatePass){let o=it(e);ol(t,r.data,r.blueprint,o,!0),ol(n,r.data,r.blueprint,o,!1)}}function ol(e,n,t,r,o){if(e=ae(e),Array.isArray(e))for(let i=0;i<e.length;i++)ol(e[i],n,t,r,o);else{let i=X(),s=L(),a=ce(),c=un(e)?e:ae(e.provide),u=Xc(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(un(e)||!e.multi){let f=new En(u,o,v),m=xu(c,n,o?l:l+h,d);m===-1?(ku(ws(a,s),i,c),Ru(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[m]=f,s[m]=f)}else{let f=xu(c,n,l+h,d),m=xu(c,n,l,l+h),E=f>=0&&t[f],S=m>=0&&t[m];if(o&&!S||!o&&!E){ku(ws(a,s),i,c);let R=jw(o?Vw:Lw,t.length,o,r,u);!o&&S&&(t[m].providerFactory=R),Ru(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(R),s.push(R)}else{let R=cm(t[o?m:f],u,!o&&r);Ru(i,e,f>-1?f:m,R)}!o&&r&&S&&t[m].componentProviders++}}}function Ru(e,n,t,r){let o=un(n),i=dh(n);if(o||i){let c=(i?ae(n.useClass):n).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let l=u.indexOf(t);l===-1?u.push(t,[r,c]):u[l+1].push(r,c)}else u.push(t,c)}}}function cm(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function xu(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function Lw(e,n,t,r){return il(this.multi,[])}function Vw(e,n,t,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=ho(t,t[N],this.providerFactory.index,r);i=a.slice(0,s),il(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],il(o,i);return i}function il(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function jw(e,n,t,r,o){let i=new En(e,t,v);return i.multi=[],i.index=n,i.componentProviders=0,cm(i,o,r&&!t),i}function It(e,n=[]){return t=>{t.providersResolver=(r,o)=>Pw(r,o?o(e):e,n)}}function Bw(e,n,t){let r=Rh()+e,o=L();return o[r]===Ze?sI(o,r,t?n.call(t):n()):aI(o,r)}var Fs=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},Gl=(()=>{class e{compileModuleSync(t){return new Rs(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=Yc(t),i=Xp(o.declarations).reduce((s,a)=>{let c=tt(a);return c&&s.push(new Wt(c)),s},[]);return new Fs(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Hw=(()=>{class e{zone=p($);changeDetectionScheduler=p(dn);applicationRef=p(Ct);applicationErrorHandler=p(Te);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(t){this.applicationErrorHandler(t)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function um({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new $(T(g({},lm()),{scheduleInRootZone:t})),[{provide:$,useFactory:e},{provide:Lt,multi:!0,useFactory:()=>{let r=p(Hw,{optional:!0});return()=>r.initialize()}},{provide:Lt,multi:!0,useFactory:()=>{let r=p(Uw);return()=>{r.initialize()}}},n===!0?{provide:Cu,useValue:!0}:[],{provide:Eu,useValue:t??Wg},{provide:Te,useFactory:()=>{let r=p($),o=p(q),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(Xe),i.handleError(s))})}}}]}function lm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Uw=(()=>{class e{subscription=new K;initialized=!1;zone=p($);pendingTasks=p(zt);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{$.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{$.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var dm=(()=>{class e{applicationErrorHandler=p(Te);appRef=p(Ct);taskService=p(zt);ngZone=p($);zonelessEnabled=p(fs);tracing=p(Do,{optional:!0});disableScheduling=p(Cu,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new K;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(xs):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(Eu,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Os||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?hp:qg;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(xs+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(t),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,hp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function $w(){return typeof $localize<"u"&&$localize.locale||Co}var Qs=new C("",{providedIn:"root",factory:()=>p(Qs,{optional:!0,skipSelf:!0})||$w()});function Be(e){return Zf(e)}function Dr(e,n){return yi(e,n?.equal)}var fm=class{[ue];constructor(n){this[ue]=n}destroy(){this[ue].destroy()}};var mm=Symbol("InputSignalNode#UNSET"),s_=T(g({},Di),{transformFn:void 0,applyValueToInputSignal(e,n){jn(e,n)}});function vm(e,n){let t=Object.create(s_);t.value=e,t.transformFn=n?.transform;function r(){if(Ln(t),t.value===mm){let o=null;throw new y(-950,o)}return t.value}return r[ue]=t,r}var Js=class{attributeName;constructor(n){this.attributeName=n}__NG_ELEMENT_ID__=()=>qt(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},a_=new C("");a_.__NG_ELEMENT_ID__=e=>{let n=ce();if(n===null)throw new y(204,!1);if(n.type&2)return n.value;if(e&8)return null;throw new y(204,!1)};function hm(e,n){return vm(e,n)}function c_(e){return vm(mm,e)}var ym=(hm.required=c_,hm);var Wl=new C(""),u_=new C("");function wo(e){return!e.moduleRef}function l_(e){let n=wo(e)?e.r3Injector:e.moduleRef.injector,t=n.get($);return t.run(()=>{wo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Te),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:r})}),wo(e)){let i=()=>n.destroy(),s=e.platformInjector.get(Wl);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Wl);s.add(i),e.moduleRef.onDestroy(()=>{fo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return f_(r,t,()=>{let i=n.get(Vl);return i.runInitializers(),i.donePromise.then(()=>{let s=n.get(Qs,Co);if(tm(s||Co),!n.get(u_,!0))return wo(e)?n.get(Ct):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(wo(e)){let c=n.get(Ct);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return d_?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var d_;function f_(e,n,t){try{let r=t();return Zt(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e(r)),r}}var Ks=null;function h_(e=[],n){return de.create({name:n,providers:[{provide:Jr,useValue:"platform"},{provide:Wl,useValue:new Set([()=>Ks=null])},...e]})}function p_(e=[]){if(Ks)return Ks;let n=h_(e);return Ks=n,Kg(),g_(n),n}function g_(e){let n=e.get(Ls,null);Se(e,()=>{n?.forEach(t=>t())})}var Ye=(()=>{class e{static __NG_ELEMENT_ID__=m_}return e})();function m_(e){return v_(ce(),L(),(e&16)===16)}function v_(e,n,t){if(Bt(e)&&!t){let r=Ve(e.index,n);return new Gt(r,r)}else if(e.type&175){let r=n[Ee];return new Gt(r,n)}return null}var ql=class{constructor(){}supports(n){return Tl(n)}create(n){return new Zl(n)}},y_=(e,n)=>n,Zl=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||y_}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<pm(r,o,i)?t:r,a=pm(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,m=f+h;l<=m&&m<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!Tl(n))throw new y(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,kg(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new Yl(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new Xs),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Xs),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},Yl=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},Ql=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},Xs=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new Ql,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function pm(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function gm(){return new Kl([new ql])}var Kl=(()=>{class e{factories;static \u0275prov=D({token:e,providedIn:"root",factory:gm});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||gm()),deps:[[e,new Ip,new Ep]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new y(901,!1)}}return e})();function Dm(e){H(8);try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,o=p_(r),i=[um({}),{provide:dn,useExisting:dm},zh,...t||[]],s=new go({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return l_({r3Injector:s.injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}finally{H(9)}}function Mn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Cm(e,n){let t=tt(e),r=n.elementInjector||nr();return new Wt(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector,n.directives,n.bindings)}function Jl(e){let n=tt(e);if(!n)return null;let t=new Wt(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var wm=null;function He(){return wm}function Xl(e){wm??=e}var _o=class{},ed=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(_m),providedIn:"platform"})}return e})();var _m=(()=>{class e extends ed{_location;_history;_doc=p(te);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return He().getBaseHref(this._doc)}onPopState(t){let r=He().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=He().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function bm(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Em(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function Yt(e){return e&&e[0]!=="?"?`?${e}`:e}var _t=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Mm),providedIn:"root"})}return e})(),Sm=new C(""),Mm=(()=>{class e extends _t{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(te).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return bm(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+Yt(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+Yt(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+Yt(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(I(ed),I(Sm,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bt=(()=>{class e{_subject=new B;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=E_(Em(Im(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+Yt(r))}normalize(t){return e.stripTrailingSlash(C_(this._basePath,Im(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Yt(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Yt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Yt;static joinWithSlash=bm;static stripTrailingSlash=Em;static \u0275fac=function(r){return new(r||e)(I(_t))};static \u0275prov=D({token:e,factory:()=>D_(),providedIn:"root"})}return e})();function D_(){return new bt(I(_t))}function C_(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function Im(e){return e.replace(/\/index.html$/,"")}function E_(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var ea=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Nm=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new ea(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Tm(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Tm(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v(je),v(qe),v(Kl))};static \u0275dir=V({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Tm(e,n){e.context.$implicit=n.item}var I_=(()=>{class e{_viewContainer;_context=new ta;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Am(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Am(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v(je),v(qe))};static \u0275dir=V({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),ta=class{$implicit=null;ngIf=null};function Am(e,n){if(e&&!e.createEmbeddedView)throw new y(2020,!1)}var w_=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(v(je))};static \u0275dir=V({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Ae]})}return e})();var Rm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Dt({type:e});static \u0275inj=et({})}return e})();function td(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var bo=class{};var xm="browser",__="server";function Om(e){return e===__}var oa=new C(""),id=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new y(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(I(oa),I($))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),So=class{_doc;constructor(n){this._doc=n}manager},na="ng-app-id";function Fm(e){for(let n of e)n.remove()}function Pm(e,n){let t=n.createElement("style");return t.textContent=e,t}function b_(e,n,t,r){let o=e.head?.querySelectorAll(`style[${na}="${n}"],link[${na}="${n}"]`);if(o)for(let i of o)i.removeAttribute(na),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function rd(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var sd=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,this.isServer=Om(i),b_(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,Pm);r?.forEach(o=>this.addUsage(o,this.external,rd))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(Fm(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])Fm(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,Pm(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,rd(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(na,this.appId),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(te),I(Ps),I(Vs,8),I(gr))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),nd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},ad=/%COMP%/g;var Vm="%COMP%",S_=`_nghost-${Vm}`,M_=`_ngcontent-${Vm}`,T_=!0,A_=new C("",{providedIn:"root",factory:()=>T_});function N_(e){return M_.replace(ad,e)}function R_(e){return S_.replace(ad,e)}function jm(e,n){return n.map(t=>t.replace(ad,e))}var cd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,u=null,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new Mo(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(t,r);return o instanceof ra?o.applyToHost(t):o instanceof To&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case yt.Emulated:i=new ra(c,u,r,this.appId,l,s,a,d,h);break;case yt.ShadowDom:return new od(c,u,t,r,s,a,this.nonce,d,h);default:i=new To(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(I(id),I(sd),I(Ps),I(A_),I(te),I(gr),I($),I(Vs),I(Do,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Mo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(nd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(Lm(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(Lm(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new y(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=nd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=nd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(at.DashCase|at.Important)?n.style.setProperty(t,r,o&at.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&at.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=He().getGlobalEventTarget(this.doc,n),!n))throw new y(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;n(t)===!1&&t.preventDefault()}}};function Lm(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var od=class extends Mo{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,c,u),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=jm(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=rd(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},To=class extends Mo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?jm(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},ra=class extends To{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(n,t,r,i,s,a,c,u,l),this.contentAttr=N_(l),this.hostAttr=R_(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var ia=class e extends _o{supportsDOMEvents=!0;static makeCurrent(){Xl(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=x_();return t==null?null:O_(t)}resetBaseElement(){Ao=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return td(document.cookie,n)}},Ao=null;function x_(){return Ao=Ao||document.head.querySelector("base"),Ao?Ao.getAttribute("href"):null}function O_(e){return new URL(e,document.baseURI).pathname}var k_=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Hm=(()=>{class e extends So{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(te))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Bm=["alt","control","meta","shift"],F_={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},P_={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Um=(()=>{class e extends So{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>He().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Bm.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=F_[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Bm.forEach(s=>{if(s!==o){let a=P_[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(I(te))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function L_(e,n){return Dm(g({rootComponent:e},V_(n)))}function V_(e){return{appProviders:[...$_,...e?.providers??[]],platformProviders:U_}}function j_(){ia.makeCurrent()}function B_(){return new Xe}function H_(){return ul(document),document}var U_=[{provide:gr,useValue:xm},{provide:Ls,useValue:j_,multi:!0},{provide:te,useFactory:H_}];var $_=[{provide:Jr,useValue:"root"},{provide:Xe,useFactory:B_},{provide:oa,useClass:Hm,multi:!0,deps:[te]},{provide:oa,useClass:Um,multi:!0,deps:[te]},cd,sd,id,{provide:In,useExisting:cd},{provide:bo,useClass:k_},[]];var $m=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(I(te))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var b="primary",zo=Symbol("RouteTitle"),hd=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Nn(e){return new hd(e)}function Km(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function G_(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!ut(e[t],n[t]))return!1;return!0}function ut(e,n){let t=e?pd(e):void 0,r=n?pd(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!Jm(e[o],n[o]))return!1;return!0}function pd(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Jm(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function Xm(e){return e.length>0?e[e.length-1]:null}function Tt(e){return vc(e)?e:Zt(e)?z(Promise.resolve(e)):_(e)}var W_={exact:tv,subset:nv},ev={exact:q_,subset:Z_,ignored:()=>!0};function zm(e,n,t){return W_[t.paths](e.root,n.root,t.matrixParams)&&ev[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function q_(e,n){return ut(e,n)}function tv(e,n,t){if(!Tn(e.segments,n.segments)||!ca(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!tv(e.children[r],n.children[r],t))return!1;return!0}function Z_(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>Jm(e[t],n[t]))}function nv(e,n,t){return rv(e,n,n.segments,t)}function rv(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!Tn(o,t)||n.hasChildren()||!ca(o,t,r))}else if(e.segments.length===t.length){if(!Tn(e.segments,t)||!ca(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!nv(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!Tn(e.segments,o)||!ca(e.segments,o,r)||!e.children[b]?!1:rv(e.children[b],n,i,r)}}function ca(e,n,t){return n.every((r,o)=>ev[t](e[o].parameters,r.parameters))}var dt=class{root;queryParams;fragment;_queryParamMap;constructor(n=new j([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Nn(this.queryParams),this._queryParamMap}toString(){return K_.serialize(this)}},j=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ua(this)}},Qt=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Nn(this.parameters),this._parameterMap}toString(){return iv(this)}};function Y_(e,n){return Tn(e,n)&&e.every((t,r)=>ut(t.parameters,n[r].parameters))}function Tn(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function Q_(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===b&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==b&&(t=t.concat(n(o,r)))}),t}var xn=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new Rn,providedIn:"root"})}return e})(),Rn=class{parse(n){let t=new md(n);return new dt(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${No(n.root,!0)}`,r=eb(n.queryParams),o=typeof n.fragment=="string"?`#${J_(n.fragment)}`:"";return`${t}${r}${o}`}},K_=new Rn;function ua(e){return e.segments.map(n=>iv(n)).join("/")}function No(e,n){if(!e.hasChildren())return ua(e);if(n){let t=e.children[b]?No(e.children[b],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==b&&r.push(`${o}:${No(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=Q_(e,(r,o)=>o===b?[No(e.children[b],!1)]:[`${o}:${No(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[b]!=null?`${ua(e)}/${t[0]}`:`${ua(e)}/(${t.join("//")})`}}function ov(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function sa(e){return ov(e).replace(/%3B/gi,";")}function J_(e){return encodeURI(e)}function gd(e){return ov(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function la(e){return decodeURIComponent(e)}function Gm(e){return la(e.replace(/\+/g,"%20"))}function iv(e){return`${gd(e.path)}${X_(e.parameters)}`}function X_(e){return Object.entries(e).map(([n,t])=>`;${gd(n)}=${gd(t)}`).join("")}function eb(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${sa(t)}=${sa(o)}`).join("&"):`${sa(t)}=${sa(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var tb=/^[^\/()?;#]+/;function ud(e){let n=e.match(tb);return n?n[0]:""}var nb=/^[^\/()?;=#]+/;function rb(e){let n=e.match(nb);return n?n[0]:""}var ob=/^[^=?&#]+/;function ib(e){let n=e.match(ob);return n?n[0]:""}var sb=/^[^&#]+/;function ab(e){let n=e.match(sb);return n?n[0]:""}var md=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new j([],{}):new j([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[b]=new j(n,t)),r}parseSegment(){let n=ud(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new y(4009,!1);return this.capture(n),new Qt(la(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=rb(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=ud(this.remaining);o&&(r=o,this.capture(r))}n[la(t)]=la(r)}parseQueryParam(n){let t=ib(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=ab(this.remaining);s&&(r=s,this.capture(r))}let o=Gm(t),i=Gm(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=ud(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new y(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=b);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[b]:new j([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new y(4011,!1)}};function sv(e){return e.segments.length>0?new j([],{[b]:e}):e}function av(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=av(o);if(r===b&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new j(e.segments,n);return cb(t)}function cb(e){if(e.numberOfChildren===1&&e.children[b]){let n=e.children[b];return new j(e.segments.concat(n.segments),n.children)}return e}function Kt(e){return e instanceof dt}function cv(e,n,t=null,r=null){let o=uv(e);return lv(o,n,t,r)}function uv(e){let n;function t(i){let s={};for(let c of i.children){let u=t(c);s[c.outlet]=u}let a=new j(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=sv(r);return n??o}function lv(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return ld(o,o,o,t,r);let i=ub(n);if(i.toRoot())return ld(o,o,new j([],{}),t,r);let s=lb(i,o,e),a=s.processChildren?xo(s.segmentGroup,s.index,i.commands):fv(s.segmentGroup,s.index,i.commands);return ld(o,s.segmentGroup,a,t,r)}function da(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Fo(e){return typeof e=="object"&&e!=null&&e.outlets}function ld(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===n?s=t:s=dv(e,n,t);let a=sv(av(s));return new dt(a,i,o)}function dv(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=dv(i,n,t)}),new j(e.segments,r)}var fa=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&da(r[0]))throw new y(4003,!1);let o=r.find(Fo);if(o&&o!==Xm(r))throw new y(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function ub(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new fa(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new fa(t,n,r)}var Ir=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function lb(e,n,t){if(e.isAbsolute)return new Ir(n,!0,0);if(!t)return new Ir(n,!1,NaN);if(t.parent===null)return new Ir(t,!0,0);let r=da(e.commands[0])?0:1,o=t.segments.length-1+r;return db(t,o,e.numberOfDoubleDots)}function db(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new y(4005,!1);o=r.segments.length}return new Ir(r,!1,o-i)}function fb(e){return Fo(e[0])?e[0].outlets:{[b]:e}}function fv(e,n,t){if(e??=new j([],{}),e.segments.length===0&&e.hasChildren())return xo(e,n,t);let r=hb(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new j(e.segments.slice(0,r.pathIndex),{});return i.children[b]=new j(e.segments.slice(r.pathIndex),e.children),xo(i,0,o)}else return r.match&&o.length===0?new j(e.segments,{}):r.match&&!e.hasChildren()?vd(e,n,t):r.match?xo(e,0,o):vd(e,n,t)}function xo(e,n,t){if(t.length===0)return new j(e.segments,{});{let r=fb(t),o={};if(Object.keys(r).some(i=>i!==b)&&e.children[b]&&e.numberOfChildren===1&&e.children[b].segments.length===0){let i=xo(e.children[b],n,t);return new j(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=fv(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new j(e.segments,o)}}function hb(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(Fo(a))break;let c=`${a}`,u=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!qm(c,u,s))return i;r+=2}else{if(!qm(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function vd(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(Fo(i)){let c=pb(i.outlets);return new j(r,c)}if(o===0&&da(t[0])){let c=e.segments[n];r.push(new Qt(c.path,Wm(t[0]))),o++;continue}let s=Fo(i)?i.outlets[b]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&da(a)?(r.push(new Qt(s,Wm(a))),o+=2):(r.push(new Qt(s,{})),o++)}return new j(r,{})}function pb(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=vd(new j([],{}),0,r))}),n}function Wm(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function qm(e,n,t){return e==t.path&&ut(n,t.parameters)}var Oo="imperative",oe=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(oe||{}),Oe=class{id;url;constructor(n,t){this.id=n,this.url=t}},St=class extends Oe{type=oe.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ke=class extends Oe{urlAfterRedirects;type=oe.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},ve=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(ve||{}),Po=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Po||{}),lt=class extends Oe{reason;code;type=oe.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Mt=class extends Oe{reason;code;type=oe.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},_r=class extends Oe{error;target;type=oe.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Lo=class extends Oe{urlAfterRedirects;state;type=oe.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ha=class extends Oe{urlAfterRedirects;state;type=oe.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},pa=class extends Oe{urlAfterRedirects;state;shouldActivate;type=oe.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ga=class extends Oe{urlAfterRedirects;state;type=oe.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ma=class extends Oe{urlAfterRedirects;state;type=oe.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},va=class{route;type=oe.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},ya=class{route;type=oe.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Da=class{snapshot;type=oe.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ca=class{snapshot;type=oe.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ea=class{snapshot;type=oe.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ia=class{snapshot;type=oe.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Vo=class{},br=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function gb(e){return!(e instanceof Vo)&&!(e instanceof br)}function mb(e,n){return e.providers&&!e._injector&&(e._injector=mr(e.providers,n,`Route: ${e.path}`)),e._injector??n}function Qe(e){return e.outlet||b}function vb(e,n){let t=e.filter(r=>Qe(r)===n);return t.push(...e.filter(r=>Qe(r)!==n)),t}function Go(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var wa=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Go(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new At(this.rootInjector)}},At=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new wa(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(I(q))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),_a=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=yd(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=yd(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=Dd(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return Dd(n,this._root).map(t=>t.value)}};function yd(e,n){if(e===n.value)return n;for(let t of n.children){let r=yd(e,t);if(r)return r}return null}function Dd(e,n){if(e===n.value)return[n];for(let t of n.children){let r=Dd(e,t);if(r.length)return r.unshift(n),r}return[]}var xe=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Er(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var jo=class extends _a{snapshot;constructor(n,t){super(n),this.snapshot=t,Md(this,n)}toString(){return this.snapshot.toString()}};function hv(e){let n=yb(e),t=new J([new Qt("",{})]),r=new J({}),o=new J({}),i=new J({}),s=new J(""),a=new Ie(t,r,i,s,o,b,e,n.root);return a.snapshot=n.root,new jo(new xe(a,[]),n)}function yb(e){let n={},t={},r={},o="",i=new An([],n,r,o,t,b,e,null,{});return new Bo("",new xe(i,[]))}var Ie=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(P(u=>u[zo]))??_(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(P(n=>Nn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(P(n=>Nn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ba(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:g(g({},n.params),e.params),data:g(g({},n.data),e.data),resolve:g(g(g(g({},e.data),n.data),o?.data),e._resolvedData)}:r={params:g({},e.params),data:g({},e.data),resolve:g(g({},e.data),e._resolvedData??{})},o&&gv(o)&&(r.resolve[zo]=o.title),r}var An=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[zo]}constructor(n,t,r,o,i,s,a,c,u){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Nn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Nn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},Bo=class extends _a{url;constructor(n,t){super(t),this.url=n,Md(this,t)}toString(){return pv(this._root)}};function Md(e,n){n.value._routerState=e,n.children.forEach(t=>Md(e,t))}function pv(e){let n=e.children.length>0?` { ${e.children.map(pv).join(", ")} } `:"";return`${e.value}${n}`}function dd(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,ut(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),ut(n.params,t.params)||e.paramsSubject.next(t.params),G_(n.url,t.url)||e.urlSubject.next(t.url),ut(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Cd(e,n){let t=ut(e.params,n.params)&&Y_(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||Cd(e.parent,n.parent))}function gv(e){return typeof e.title=="string"||e.title===null}var mv=new C(""),Td=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=b;activateEvents=new U;deactivateEvents=new U;attachEvents=new U;detachEvents=new U;routerOutletData=ym(void 0);parentContexts=p(At);location=p(je);changeDetector=p(Ye);inputBinder=p(Aa,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new y(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new y(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new y(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new y(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Ed(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ae]})}return e})(),Ed=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===Ie?this.route:n===At?this.childContexts:n===mv?this.outletData:this.parent.get(n,t)}},Aa=new C("");var Ad=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Rl({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Ys(0,"router-outlet")},dependencies:[Td],encapsulation:2})}return e})();function Nd(e){let n=e.children&&e.children.map(Nd),t=n?T(g({},e),{children:n}):g({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==b&&(t.component=Ad),t}function Db(e,n,t){let r=Ho(e,n._root,t?t._root:void 0);return new jo(r,n)}function Ho(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=Cb(e,n,t);return new xe(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>Ho(e,a)),s}}let r=Eb(n.value),o=n.children.map(i=>Ho(e,i));return new xe(r,o)}}function Cb(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Ho(e,r,o);return Ho(e,r)})}function Eb(e){return new Ie(new J(e.url),new J(e.params),new J(e.queryParams),new J(e.fragment),new J(e.data),e.outlet,e.component,e)}var Sr=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},vv="ngNavigationCancelingError";function Sa(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=Kt(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=yv(!1,ve.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function yv(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[vv]=!0,t.cancellationCode=n,t}function Ib(e){return Dv(e)&&Kt(e.url)}function Dv(e){return!!e&&e[vv]}var wb=(e,n,t,r)=>P(o=>(new Id(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),Id=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),dd(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=Er(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Er(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Er(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=Er(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Ia(i.value.snapshot))}),n.children.length&&this.forwardEvent(new Ca(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(dd(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),dd(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Ma=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},wr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function _b(e,n,t){let r=e._root,o=n?n._root:null;return Ro(r,o,t,[r.value])}function bb(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function Tr(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!Lc(e)?e:n.get(e):r}function Ro(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Er(n);return e.children.forEach(s=>{Sb(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>ko(a,t.getContext(s),o)),o}function Sb(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=Mb(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Ma(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Ro(e,n,a?a.children:null,r,o):Ro(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new wr(a.outlet.component,s))}else s&&ko(n,a,o),o.canActivateChecks.push(new Ma(r)),i.component?Ro(e,null,a?a.children:null,r,o):Ro(e,null,t,r,o);return o}function Mb(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!Tn(e.url,n.url);case"pathParamsOrQueryParamsChange":return!Tn(e.url,n.url)||!ut(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Cd(e,n)||!ut(e.queryParams,n.queryParams);case"paramsChange":default:return!Cd(e,n)}}function ko(e,n,t){let r=Er(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?ko(s,n.children.getContext(i),t):ko(s,null,t):ko(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new wr(n.outlet.component,o)):t.canDeactivateChecks.push(new wr(null,o)):t.canDeactivateChecks.push(new wr(null,o))}function Wo(e){return typeof e=="function"}function Tb(e){return typeof e=="boolean"}function Ab(e){return e&&Wo(e.canLoad)}function Nb(e){return e&&Wo(e.canActivate)}function Rb(e){return e&&Wo(e.canActivateChild)}function xb(e){return e&&Wo(e.canDeactivate)}function Ob(e){return e&&Wo(e.canMatch)}function Cv(e){return e instanceof ht||e?.name==="EmptyError"}var aa=Symbol("INITIAL_VALUE");function Mr(){return le(e=>Qn(e.map(n=>n.pipe(pt(1),Ic(aa)))).pipe(P(n=>{for(let t of n)if(t!==!0){if(t===aa)return aa;if(t===!1||kb(t))return t}return!0}),se(n=>n!==aa),pt(1)))}function kb(e){return Kt(e)||e instanceof Sr}function Fb(e,n){return W(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?_(T(g({},t),{guardsResult:!0})):Pb(s,r,o,e).pipe(W(a=>a&&Tb(a)?Lb(r,i,e,n):_(a)),P(a=>T(g({},t),{guardsResult:a})))})}function Pb(e,n,t,r){return z(e).pipe(W(o=>Ub(o.component,o.route,t,n,r)),gt(o=>o!==!0,!0))}function Lb(e,n,t,r){return z(n).pipe(Ot(o=>Jn(jb(o.route.parent,r),Vb(o.route,r),Hb(e,o.path,t),Bb(e,o.route,t))),gt(o=>o!==!0,!0))}function Vb(e,n){return e!==null&&n&&n(new Ea(e)),_(!0)}function jb(e,n){return e!==null&&n&&n(new Da(e)),_(!0)}function Bb(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return _(!0);let o=r.map(i=>$r(()=>{let s=Go(n)??t,a=Tr(i,s),c=Nb(a)?a.canActivate(n,e):Se(s,()=>a(n,e));return Tt(c).pipe(gt())}));return _(o).pipe(Mr())}function Hb(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>bb(s)).filter(s=>s!==null).map(s=>$r(()=>{let a=s.guards.map(c=>{let u=Go(s.node)??t,l=Tr(c,u),d=Rb(l)?l.canActivateChild(r,e):Se(u,()=>l(r,e));return Tt(d).pipe(gt())});return _(a).pipe(Mr())}));return _(i).pipe(Mr())}function Ub(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return _(!0);let s=i.map(a=>{let c=Go(n)??o,u=Tr(a,c),l=xb(u)?u.canDeactivate(e,n,t,r):Se(c,()=>u(e,n,t,r));return Tt(l).pipe(gt())});return _(s).pipe(Mr())}function $b(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return _(!0);let i=o.map(s=>{let a=Tr(s,e),c=Ab(a)?a.canLoad(n,t):Se(e,()=>a(n,t));return Tt(c)});return _(i).pipe(Mr(),Ev(r))}function Ev(e){return hc(ne(n=>{if(typeof n!="boolean")throw Sa(e,n)}),P(n=>n===!0))}function zb(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return _(!0);let i=o.map(s=>{let a=Tr(s,e),c=Ob(a)?a.canMatch(n,t):Se(e,()=>a(n,t));return Tt(c)});return _(i).pipe(Mr(),Ev(r))}var Uo=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},$o=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function Cr(e){return Zn(new Uo(e))}function Gb(e){return Zn(new y(4e3,!1))}function Wb(e){return Zn(yv(!1,ve.GuardRejected))}var wd=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return _(r);if(o.numberOfChildren>1||!o.children[b])return Gb(`${n.redirectTo}`);o=o.children[b]}}applyRedirectCommands(n,t,r,o,i){return qb(t,o,i).pipe(P(s=>{if(s instanceof dt)throw new $o(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),n,r);if(s[0]==="/")throw new $o(a);return a}))}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new dt(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new j(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new y(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}};function qb(e,n,t){if(typeof e=="string")return _(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=n;return Tt(Se(t,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var _d={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Zb(e,n,t,r,o){let i=Iv(e,n,t);return i.matched?(r=mb(n,r),zb(r,n,t,o).pipe(P(s=>s===!0?i:g({},_d)))):_(i)}function Iv(e,n,t){if(n.path==="**")return Yb(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?g({},_d):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||Km)(t,e,n);if(!o)return g({},_d);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?g(g({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function Yb(e){return{matched:!0,parameters:e.length>0?Xm(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Zm(e,n,t,r){return t.length>0&&Jb(e,t,r)?{segmentGroup:new j(n,Kb(r,new j(t,e.children))),slicedSegments:[]}:t.length===0&&Xb(e,t,r)?{segmentGroup:new j(e.segments,Qb(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new j(e.segments,e.children),slicedSegments:t}}function Qb(e,n,t,r){let o={};for(let i of t)if(Na(e,n,i)&&!r[Qe(i)]){let s=new j([],{});o[Qe(i)]=s}return g(g({},r),o)}function Kb(e,n){let t={};t[b]=n;for(let r of e)if(r.path===""&&Qe(r)!==b){let o=new j([],{});t[Qe(r)]=o}return t}function Jb(e,n,t){return t.some(r=>Na(e,n,r)&&Qe(r)!==b)}function Xb(e,n,t){return t.some(r=>Na(e,n,r))}function Na(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function eS(e,n,t){return n.length===0&&!e.children[t]}var bd=class{};function tS(e,n,t,r,o,i,s="emptyOnly"){return new Sd(e,n,t,r,o,s,i).recognize()}var nS=31,Sd=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new wd(this.urlSerializer,this.urlTree)}noMatchError(n){return new y(4002,`'${n.segmentGroup}'`)}recognize(){let n=Zm(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(P(({children:t,rootSnapshot:r})=>{let o=new xe(r,t),i=new Bo("",o),s=cv(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new An([],Object.freeze({}),Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),b,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,b,t).pipe(P(r=>({children:r,rootSnapshot:t})),Je(r=>{if(r instanceof $o)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Uo?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(P(s=>s instanceof xe?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return z(i).pipe(Ot(s=>{let a=r.children[s],c=vb(t,s);return this.processSegmentGroup(n,c,a,s,o)}),Ec((s,a)=>(s.push(...a),s)),kt(null),Cc(),W(s=>{if(s===null)return Cr(r);let a=wv(s);return rS(a),_(a)}))}processSegment(n,t,r,o,i,s,a){return z(t).pipe(Ot(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(Je(u=>{if(u instanceof Uo)return _(null);throw u}))),gt(c=>!!c),Je(c=>{if(Cv(c))return eS(r,o,i)?_(new bd):Cr(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return Qe(r)!==s&&(s===b||!Na(o,i,r))?Cr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):Cr(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=Iv(t,o,i);if(!c)return Cr(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>nS&&(this.allowRedirects=!1));let f=new An(i,u,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Ym(o),Qe(o),o.component??o._loadedComponent??null,o,Qm(o)),m=ba(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(m.params),f.data=Object.freeze(m.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,n).pipe(le(S=>this.applyRedirects.lineralizeSegments(o,S)),W(S=>this.processSegment(n,r,t,S.concat(h),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=Zb(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(le(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(le(({routes:u})=>{let l=r._loadedInjector??n,{parameters:d,consumedSegments:h,remainingSegments:f}=c,m=new An(h,d,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Ym(r),Qe(r),r.component??r._loadedComponent??null,r,Qm(r)),E=ba(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(E.params),m.data=Object.freeze(E.data);let{segmentGroup:S,slicedSegments:R}=Zm(t,h,f,u);if(R.length===0&&S.hasChildren())return this.processChildren(l,u,S,m).pipe(P(Xt=>new xe(m,Xt)));if(u.length===0&&R.length===0)return _(new xe(m,[]));let Fn=Qe(r)===i;return this.processSegment(l,u,S,R,Fn?b:i,!0,m).pipe(P(Xt=>new xe(m,Xt instanceof xe?[Xt]:[])))}))):Cr(t)))}getChildConfig(n,t,r){return t.children?_({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?_({routes:t._loadedRoutes,injector:t._loadedInjector}):$b(n,t,r,this.urlSerializer).pipe(W(o=>o?this.configLoader.loadChildren(n,t).pipe(ne(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):Wb(t))):_({routes:[],injector:n})}};function rS(e){e.sort((n,t)=>n.value.outlet===b?-1:t.value.outlet===b?1:n.value.outlet.localeCompare(t.value.outlet))}function oS(e){let n=e.value.routeConfig;return n&&n.path===""}function wv(e){let n=[],t=new Set;for(let r of e){if(!oS(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=wv(r.children);n.push(new xe(r.value,o))}return n.filter(r=>!t.has(r))}function Ym(e){return e.data||{}}function Qm(e){return e.resolve||{}}function iS(e,n,t,r,o,i){return W(s=>tS(e,n,t,r,s.extractedUrl,o,i).pipe(P(({state:a,tree:c})=>T(g({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function sS(e,n){return W(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return _(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of _v(c))s.add(u);let a=0;return z(s).pipe(Ot(c=>i.has(c)?aS(c,r,e,n):(c.data=ba(c,c.parent,e).resolve,_(void 0))),ne(()=>a++),Xn(1),W(c=>a===s.size?_(t):ye))})}function _v(e){let n=e.children.map(t=>_v(t)).flat();return[e,...n]}function aS(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!gv(o)&&(i[zo]=o.title),$r(()=>(e.data=ba(e,e.parent,t).resolve,cS(i,e,n,r).pipe(P(s=>(e._resolvedData=s,e.data=g(g({},e.data),s),null)))))}function cS(e,n,t,r){let o=pd(e);if(o.length===0)return _({});let i={};return z(o).pipe(W(s=>uS(e[s],n,t,r).pipe(gt(),ne(a=>{if(a instanceof Sr)throw Sa(new Rn,a);i[s]=a}))),Xn(1),P(()=>i),Je(s=>Cv(s)?ye:Zn(s)))}function uS(e,n,t,r){let o=Go(n)??r,i=Tr(e,o),s=i.resolve?i.resolve(n,t):Se(o,()=>i(n,t));return Tt(s)}function fd(e){return le(n=>{let t=e(n);return t?z(t).pipe(P(()=>n)):_(n)})}var Rd=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===b);return r}getResolvedTitleForRoute(t){return t.data[zo]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(bv),providedIn:"root"})}return e})(),bv=(()=>{class e extends Rd{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I($m))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ar=new C("",{providedIn:"root",factory:()=>({})}),qo=new C(""),xd=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(Gl);loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return _(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let r=Tt(t.loadComponent()).pipe(P(Mv),ne(i=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=i}),Gr(()=>{this.componentLoaders.delete(t)})),o=new Wn(r,()=>new B).pipe(Gn());return this.componentLoaders.set(t,o),o}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return _({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Sv(r,this.compiler,t,this.onLoadEndListener).pipe(Gr(()=>{this.childrenLoaders.delete(r)})),s=new Wn(i,()=>new B).pipe(Gn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Sv(e,n,t,r){return Tt(e.loadChildren()).pipe(P(Mv),W(o=>o instanceof qs||Array.isArray(o)?_(o):z(n.compileModuleAsync(o))),P(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(qo,[],{optional:!0,self:!0}).flat()),{routes:s.map(Nd),injector:i}}))}function lS(e){return e&&typeof e=="object"&&"default"in e}function Mv(e){return lS(e)?e.default:e}var Ra=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(dS),providedIn:"root"})}return e})(),dS=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Tv=new C("");var Av=new C(""),Nv=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new B;transitionAbortWithErrorSubject=new B;configLoader=p(xd);environmentInjector=p(q);destroyRef=p(ar);urlSerializer=p(xn);rootContexts=p(At);location=p(bt);inputBindingEnabled=p(Aa,{optional:!0})!==null;titleStrategy=p(Rd);options=p(Ar,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(Ra);createViewTransition=p(Tv,{optional:!0});navigationErrorHandler=p(Av,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>_(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new va(o)),r=o=>this.events.next(new ya(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(T(g({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(t){return this.transitions=new J(null),this.transitions.pipe(se(r=>r!==null),le(r=>{let o=!1;return _(r).pipe(le(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",ve.SupersededByNewNavigation),ye;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?T(g({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new Mt(i.id,this.urlSerializer.serialize(i.rawUrl),c,Po.IgnoredSameUrlNavigation)),i.resolve(!1),ye}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return _(i).pipe(le(c=>(this.events.next(new St(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?ye:Promise.resolve(c))),iS(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),ne(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=T(g({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new Lo(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new St(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let m=hv(this.rootComponentType).snapshot;return this.currentTransition=r=T(g({},i),{targetSnapshot:m,urlAfterRedirects:u,extras:T(g({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,_(r)}else{let c="";return this.events.next(new Mt(i.id,this.urlSerializer.serialize(i.extractedUrl),c,Po.IgnoredByUrlHandlingStrategy)),i.resolve(!1),ye}}),ne(i=>{let s=new ha(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),P(i=>(this.currentTransition=r=T(g({},i),{guards:_b(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),Fb(this.environmentInjector,i=>this.events.next(i)),ne(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw Sa(this.urlSerializer,i.guardsResult);let s=new pa(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),se(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",ve.GuardRejected),!1)),fd(i=>{if(i.guards.canActivateChecks.length!==0)return _(i).pipe(ne(s=>{let a=new ga(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),le(s=>{let a=!1;return _(s).pipe(sS(this.paramsInheritanceStrategy,this.environmentInjector),ne({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",ve.NoDataFromResolver)}}))}),ne(s=>{let a=new ma(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),fd(i=>{let s=a=>{let c=[];a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(a.routeConfig).pipe(ne(u=>{a.component=u}),P(()=>{})));for(let u of a.children)c.push(...s(u));return c};return Qn(s(i.targetSnapshot.root)).pipe(kt(null),pt(1))}),fd(()=>this.afterPreactivation()),le(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?z(a).pipe(P(()=>r)):_(r)}),P(i=>{let s=Db(t.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=T(g({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),ne(()=>{this.events.next(new Vo)}),wb(this.rootContexts,t.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),pt(1),Bi(new k(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(se(()=>!o&&!r.targetRouterState),ne(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",ve.Aborted)}))),ne({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ke(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),Bi(this.transitionAbortWithErrorSubject.pipe(ne(i=>{throw i}))),Gr(()=>{o||this.cancelNavigationTransition(r,"",ve.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),Je(i=>{if(this.destroyed)return r.resolve(!1),ye;if(o=!0,Dv(i))this.events.next(new lt(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),Ib(i)?this.events.next(new br(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new _r(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=Se(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof Sr){let{message:c,cancellationCode:u}=Sa(this.urlSerializer,a);this.events.next(new lt(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new br(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return ye}))}))}cancelNavigationTransition(t,r,o){let i=new lt(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fS(e){return e!==Oo}var Rv=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(hS),providedIn:"root"})}return e})(),Ta=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},hS=(()=>{class e extends Ta{static \u0275fac=(()=>{let t;return function(o){return(t||(t=ct(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),xv=(()=>{class e{urlSerializer=p(xn);options=p(Ar,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(bt);urlHandlingStrategy=p(Ra);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new dt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof dt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=hv(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(pS),providedIn:"root"})}return e})(),pS=(()=>{class e extends xv{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof St?this.updateStateMemento():t instanceof Mt?this.commitTransition(r):t instanceof Lo?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof Vo?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof lt&&t.code!==ve.SupersededByNewNavigation&&t.code!==ve.Redirect?this.restoreHistory(r):t instanceof _r?this.restoreHistory(r,!0):t instanceof Ke&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=g(g({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=g(g({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=ct(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Od(e,n){e.events.pipe(se(t=>t instanceof Ke||t instanceof lt||t instanceof _r||t instanceof Mt),P(t=>t instanceof Ke||t instanceof Mt?0:(t instanceof lt?t.code===ve.Redirect||t.code===ve.SupersededByNewNavigation:!1)?2:1),se(t=>t!==2),pt(1)).subscribe(()=>{n()})}var gS={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},mS={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ke=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Fl);stateManager=p(xv);options=p(Ar,{optional:!0})||{};pendingTasks=p(zt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(Nv);urlSerializer=p(xn);location=p(bt);urlHandlingStrategy=p(Ra);injector=p(q);_events=new B;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(Rv);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(qo,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Aa,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new K;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof lt&&r.code!==ve.Redirect&&r.code!==ve.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Ke)this.navigated=!0;else if(r instanceof br){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=g({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||fS(o.source)},s);this.scheduleNavigation(a,Oo,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}gb(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Oo,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=g({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i).catch(c=>{this.injector.get(Te)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Nd),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=g(g({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=uv(h)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),d=this.currentUrlTree.root}return lv(d,t,l,u??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=Kt(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Oo,null,r)}navigate(t,r={skipLocationChange:!1}){return vS(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=g({},gS):r===!1?o=g({},mS):o=r,Kt(t))return zm(this.currentUrlTree,t,o);let i=this.parseUrl(t);return zm(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return Od(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function vS(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new y(4008,!1)}var Yo=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=vt(null);get href(){return Be(this.reactiveHref)}set href(t){this.reactiveHref.set(t)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new B;applicationErrorHandler=p(Te);options=p(Ar,{optional:!0});constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.reactiveHref.set(p(new Js("href"),{optional:!0}));let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let t=this.preserveFragment,r=o=>o==="merge"||o==="preserve";t||=r(this.queryParamsHandling),t||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),t&&(this.subscription=this.router.events.subscribe(o=>{o instanceof Ke&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(Kt(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(u=>{this.applicationErrorHandler(u)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.reactiveHref.set(t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t))??"":null)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:Kt(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(v(ke),v(Ie),qt("tabindex"),v(Sn),v(Q),v(_t))};static \u0275dir=V({type:e,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,o){r&1&&Re("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Et("href",o.reactiveHref(),fl)("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Mn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Mn],replaceUrl:[2,"replaceUrl","replaceUrl",Mn],routerLink:"routerLink"},features:[Ae]})}return e})();var Zo=class{},DS=(()=>{class e{preload(t,r){return r().pipe(Je(()=>_(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Ov=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i){this.router=t,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(se(t=>t instanceof Ke),Ot(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=mr(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return z(o).pipe(Kn())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=_(null);let i=o.pipe(W(s=>s===null?_(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return z([i,s]).pipe(Kn())}else return i})}static \u0275fac=function(r){return new(r||e)(I(ke),I(q),I(Zo),I(xd))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),CS=new C("");function ES(e,...n){return Ki([{provide:qo,multi:!0,useValue:e},[],{provide:Ie,useFactory:IS,deps:[ke]},{provide:Zs,multi:!0,useFactory:_S},n.map(t=>t.\u0275providers)])}function IS(e){return e.routerState.root}function wS(e,n){return{\u0275kind:e,\u0275providers:n}}function _S(){let e=p(de);return n=>{let t=e.get(Ct);if(n!==t.components[0])return;let r=e.get(ke),o=e.get(bS);e.get(SS)===1&&r.initialNavigation(),e.get(kv,null,{optional:!0})?.setUpPreloading(),e.get(CS,null,{optional:!0})?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var bS=new C("",{factory:()=>new B}),SS=new C("",{providedIn:"root",factory:()=>1});var kv=new C("");function MS(e){return wS(0,[{provide:kv,useExisting:Ov},{provide:Zo,useExisting:e}])}var $v=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(v(Sn),v(Q))};static \u0275dir=V({type:e})}return e})(),AS=(()=>{class e extends $v{static \u0275fac=(()=>{let t;return function(o){return(t||(t=ct(e)))(o||e)}})();static \u0275dir=V({type:e,features:[Ne]})}return e})(),zv=new C("");var NS={provide:zv,useExisting:Fe(()=>Gv),multi:!0};function RS(){let e=He()?He().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var xS=new C(""),Gv=(()=>{class e extends $v{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!RS())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(v(Sn),v(Q),v(xS,8))};static \u0275dir=V({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&Re("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[It([NS]),Ne]})}return e})();function OS(e){return e==null||Wv(e)===0}function Wv(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var Or=new C(""),qv=new C("");function kS(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function FS(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function PS(e){return OS(e.value)?{required:!0}:null}function LS(e){return n=>{let t=n.value?.length??Wv(n.value);return t!==null&&t>e?{maxlength:{requiredLength:e,actualLength:t}}:null}}function Fv(e){return null}function Zv(e){return e!=null}function Yv(e){return Zt(e)?z(e):e}function Qv(e){let n={};return e.forEach(t=>{n=t!=null?g(g({},n),t):n}),Object.keys(n).length===0?null:n}function Kv(e,n){return n.map(t=>t(e))}function VS(e){return!e.validate}function Jv(e){return e.map(n=>VS(n)?n:t=>n.validate(t))}function jS(e){if(!e)return null;let n=e.filter(Zv);return n.length==0?null:function(t){return Qv(Kv(t,n))}}function Ld(e){return e!=null?jS(Jv(e)):null}function BS(e){if(!e)return null;let n=e.filter(Zv);return n.length==0?null:function(t){let r=Kv(t,n).map(Yv);return yc(r).pipe(P(Qv))}}function Vd(e){return e!=null?BS(Jv(e)):null}function Pv(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function HS(e){return e._rawValidators}function US(e){return e._rawAsyncValidators}function kd(e){return e?Array.isArray(e)?e:[e]:[]}function Oa(e,n){return Array.isArray(e)?e.includes(n):e===n}function Lv(e,n){let t=kd(n);return kd(e).forEach(o=>{Oa(t,o)||t.push(o)}),t}function Vv(e,n){return kd(n).filter(t=>!Oa(e,t))}var ka=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=Ld(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=Vd(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},xr=class extends ka{name;get formDirective(){return null}get path(){return null}},On=class extends ka{_parent=null;name=null;valueAccessor=null},Fa=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},$S={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},C$=T(g({},$S),{"[class.ng-submitted]":"isSubmitted"}),E$=(()=>{class e extends Fa{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(On,2))};static \u0275dir=V({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&Io("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[Ne]})}return e})(),I$=(()=>{class e extends Fa{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(xr,10))};static \u0275dir=V({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&Io("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[Ne]})}return e})();var Qo="VALID",xa="INVALID",Nr="PENDING",Ko="DISABLED",Jt=class{},Pa=class extends Jt{value;source;constructor(n,t){super(),this.value=n,this.source=t}},Xo=class extends Jt{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},ei=class extends Jt{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},Rr=class extends Jt{status;source;constructor(n,t){super(),this.status=n,this.source=t}},Fd=class extends Jt{source;constructor(n){super(),this.source=n}},Pd=class extends Jt{source;constructor(n){super(),this.source=n}};function Xv(e){return(ja(e)?e.validators:e)||null}function zS(e){return Array.isArray(e)?Ld(e):e||null}function ey(e,n){return(ja(n)?n.asyncValidators:e)||null}function GS(e){return Array.isArray(e)?Vd(e):e||null}function ja(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function WS(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new y(1e3,"");if(!r[t])throw new y(1001,"")}function qS(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new y(1002,"")})}var La=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Be(this.statusReactive)}set status(n){Be(()=>this.statusReactive.set(n))}_status=Dr(()=>this.statusReactive());statusReactive=vt(void 0);get valid(){return this.status===Qo}get invalid(){return this.status===xa}get pending(){return this.status==Nr}get disabled(){return this.status===Ko}get enabled(){return this.status!==Ko}errors;get pristine(){return Be(this.pristineReactive)}set pristine(n){Be(()=>this.pristineReactive.set(n))}_pristine=Dr(()=>this.pristineReactive());pristineReactive=vt(!0);get dirty(){return!this.pristine}get touched(){return Be(this.touchedReactive)}set touched(n){Be(()=>this.touchedReactive.set(n))}_touched=Dr(()=>this.touchedReactive());touchedReactive=vt(!1);get untouched(){return!this.touched}_events=new B;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(Lv(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(Lv(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(Vv(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(Vv(n,this._rawAsyncValidators))}hasValidator(n){return Oa(this._rawValidators,n)}hasAsyncValidator(n){return Oa(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(T(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ei(!0,r))}markAllAsDirty(n={}){this.markAsDirty({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsDirty(n))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new ei(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(T(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new Xo(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new Xo(!0,r))}markAsPending(n={}){this.status=Nr;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Rr(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(T(g({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Ko,this.errors=null,this._forEachChild(o=>{o.disable(T(g({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Pa(this.value,r)),this._events.next(new Rr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(T(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Qo,this._forEachChild(r=>{r.enable(T(g({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(T(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Qo||this.status===Nr)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Pa(this.value,t)),this._events.next(new Rr(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(T(g({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Ko:Qo}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=Nr,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1,shouldHaveEmitted:n!==!1};let r=Yv(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Rr(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new U,this.statusChanges=new U}_calculateStatus(){return this._allControlsDisabled()?Ko:this.errors?xa:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Nr)?Nr:this._anyControlsHaveStatus(xa)?xa:Qo}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new Xo(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new ei(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){ja(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=zS(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=GS(this._rawAsyncValidators)}},Va=class extends La{constructor(n,t,r){super(Xv(t),ey(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){qS(this,!0,n),Object.keys(n).forEach(r=>{WS(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var jd=new C("",{providedIn:"root",factory:()=>Bd}),Bd="always";function ZS(e,n){return[...n.path,e]}function ty(e,n,t=Bd){ny(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),QS(e,n),JS(e,n),KS(e,n),YS(e,n)}function jv(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function YS(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function ny(e,n){let t=HS(e);n.validator!==null?e.setValidators(Pv(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=US(e);n.asyncValidator!==null?e.setAsyncValidators(Pv(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();jv(n._rawValidators,o),jv(n._rawAsyncValidators,o)}function QS(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&ry(e,n)})}function KS(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&ry(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function ry(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function JS(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function XS(e,n){e==null,ny(e,n)}function eM(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function tM(e){return Object.getPrototypeOf(e.constructor)===AS}function nM(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function rM(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===Gv?t=i:tM(i)?r=i:o=i}),o||r||t||null}var oM={provide:xr,useExisting:Fe(()=>iM)},Jo=Promise.resolve(),iM=(()=>{class e extends xr{callSetDisabledState;get submitted(){return Be(this.submittedReactive)}_submitted=Dr(()=>this.submittedReactive());submittedReactive=vt(!1);_directives=new Set;form;ngSubmit=new U;options;constructor(t,r,o){super(),this.callSetDisabledState=o,this.form=new Va({},Ld(t),Vd(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(t){Jo.then(()=>{let r=this._findContainer(t.path);t.control=r.registerControl(t.name,t.control),ty(t.control,t,this.callSetDisabledState),t.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(t)})}getControl(t){return this.form.get(t.path)}removeControl(t){Jo.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name),this._directives.delete(t)})}addFormGroup(t){Jo.then(()=>{let r=this._findContainer(t.path),o=new Va({});XS(o,t),r.registerControl(t.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(t){Jo.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name)})}getFormGroup(t){return this.form.get(t.path)}updateModel(t,r){Jo.then(()=>{this.form.get(t.path).setValue(r)})}setValue(t){this.control.setValue(t)}onSubmit(t){return this.submittedReactive.set(!0),nM(this.form,this._directives),this.ngSubmit.emit(t),this.form._events.next(new Fd(this.control)),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submittedReactive.set(!1),this.form._events.next(new Pd(this.form))}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(t){return t.pop(),t.length?this.form.get(t):this.form}static \u0275fac=function(r){return new(r||e)(v(Or,10),v(qv,10),v(jd,8))};static \u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,o){r&1&&Re("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[It([oM]),Ne]})}return e})();function Bv(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Hv(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var sM=class extends La{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(Xv(t),ey(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),ja(t)&&(t.nonNullable||t.initialValueIsDefault)&&(Hv(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){Bv(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){Bv(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){Hv(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var aM={provide:On,useExisting:Fe(()=>cM)},Uv=Promise.resolve(),cM=(()=>{class e extends On{_changeDetectorRef;callSetDisabledState;control=new sM;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new U;constructor(t,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=rM(this,i)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),eM(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){ty(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){Uv.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let r=t.isDisabled.currentValue,o=r!==0&&Mn(r);Uv.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?ZS(t,this._parent):[t]}static \u0275fac=function(r){return new(r||e)(v(xr,9),v(Or,10),v(qv,10),v(zv,10),v(Ye,8),v(jd,8))};static \u0275dir=V({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[It([aM]),Ne,Ae]})}return e})();var _$=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();function uM(e){return typeof e=="number"?e:parseInt(e,10)}function oy(e){return typeof e=="number"?e:parseFloat(e)}var Ba=(()=>{class e{_validator=Fv;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):Fv,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,features:[Ae]})}return e})(),lM={provide:Or,useExisting:Fe(()=>dM),multi:!0},dM=(()=>{class e extends Ba{max;inputName="max";normalizeInput=t=>oy(t);createValidator=t=>FS(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=ct(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Et("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[It([lM]),Ne]})}return e})(),fM={provide:Or,useExisting:Fe(()=>hM),multi:!0},hM=(()=>{class e extends Ba{min;inputName="min";normalizeInput=t=>oy(t);createValidator=t=>kS(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=ct(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Et("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[It([fM]),Ne]})}return e})(),pM={provide:Or,useExisting:Fe(()=>gM),multi:!0};var gM=(()=>{class e extends Ba{required;inputName="required";normalizeInput=Mn;createValidator=t=>PS;enabled(t){return t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=ct(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,o){r&2&&Et("required",o._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[It([pM]),Ne]})}return e})();var mM={provide:Or,useExisting:Fe(()=>vM),multi:!0},vM=(()=>{class e extends Ba{maxlength;inputName="maxlength";normalizeInput=t=>uM(t);createValidator=t=>LS(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=ct(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Et("maxlength",o._enabled?o.maxlength:null)},inputs:{maxlength:"maxlength"},standalone:!1,features:[It([mM]),Ne]})}return e})();var yM=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Dt({type:e});static \u0275inj=et({})}return e})();var b$=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:jd,useValue:t.callSetDisabledState??Bd}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Dt({type:e});static \u0275inj=et({imports:[yM]})}return e})();var T$=(e,n,t,r,o)=>CM(e[1],n[1],t[1],r[1],o).map(i=>DM(e[0],n[0],t[0],r[0],i)),DM=(e,n,t,r,o)=>{let i=3*n*Math.pow(o-1,2),s=-3*t*o+3*t+r*o,a=e*Math.pow(o-1,3);return o*(i+o*s)-a},CM=(e,n,t,r,o)=>(e-=o,n-=o,t-=o,r-=o,IM(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),EM=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},IM=(e,n,t,r)=>{if(e===0)return EM(n,t,r);n/=e,t/=e,r/=e;let o=(3*t-n*n)/3,i=(2*n*n*n-9*n*t+27*r)/27;if(o===0)return[Math.pow(-i,.3333333333333333)];if(i===0)return[Math.sqrt(-o),-Math.sqrt(-o)];let s=Math.pow(i/2,2)+Math.pow(o/3,3);if(s===0)return[Math.pow(i/2,.5)-n/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),.3333333333333333)-Math.pow(i/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(o/3),3)),c=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(o/3),3))))),u=2*Math.pow(a,1/3);return[u*Math.cos(c/3)-n/3,u*Math.cos((c+2*Math.PI)/3)-n/3,u*Math.cos((c+4*Math.PI)/3)-n/3]};var Ha=e=>sy(e),Fr=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),Ha(e).includes(n)),sy=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=wM(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},wM=e=>{let n=we.get("platform");return Object.keys(iy).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):iy[t](e)})},_M=e=>Ua(e)&&!cy(e),Hd=e=>!!(kn(e,/iPad/i)||kn(e,/Macintosh/i)&&Ua(e)),bM=e=>kn(e,/iPhone/i),SM=e=>kn(e,/iPhone|iPod/i)||Hd(e),ay=e=>kn(e,/android|sink/i),MM=e=>ay(e)&&!kn(e,/mobile/i),TM=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},AM=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return Hd(e)||MM(e)||r>460&&r<820&&o>780&&o<1400},Ua=e=>OM(e,"(any-pointer:coarse)"),NM=e=>!Ua(e),cy=e=>uy(e)||ly(e),uy=e=>!!(e.cordova||e.phonegap||e.PhoneGap),ly=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},RM=e=>kn(e,/electron/i),xM=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},kn=(e,n)=>n.test(e.navigator.userAgent),OM=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},iy={ipad:Hd,iphone:bM,ios:SM,android:ay,phablet:TM,tablet:AM,cordova:uy,capacitor:ly,electron:RM,pwa:xM,mobile:Ua,mobileweb:_M,desktop:NM,hybrid:cy},kr,Ud=e=>e&&sf(e)||kr,kM=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},af(t)),{persistConfig:!1}),r.config),uf(t)),e);we.reset(o),we.getBoolean("persistConfig")&&cf(t,o),sy(t),r.config=we,r.mode=kr=we.get("mode",n.documentElement.getAttribute("mode")||(Fr(t,"ios")?"ios":"md")),we.set("mode",kr),n.documentElement.setAttribute("mode",kr),n.documentElement.classList.add(kr),we.getBoolean("_testing")&&we.set("animated",!1);let i=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);of(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;i(a)&&ii('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return kr})};var FM=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],O$=e=>{let n={};return FM(e).forEach(t=>n[t]=!0),n};var L$=(e,n,t,r,o,i)=>ie(null,null,function*(){var s;if(e)return e.attachViewToDom(n,t,o,r);if(!i&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),o&&Object.assign(a,o),n.appendChild(a),yield new Promise(c=>Rt(a,c)),a}),V$=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},j$=()=>{let e,n;return{attachViewToDom:(c,u,...l)=>ie(null,[c,u,...l],function*(o,i,s={},a=[]){var d,h;e=o;let f;if(i){let E=typeof i=="string"?(d=e.ownerDocument)===null||d===void 0?void 0:d.createElement(i):i;a.forEach(S=>E.classList.add(S)),Object.assign(E,s),e.appendChild(E),f=E,yield new Promise(S=>Rt(E,S))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let S=(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement("div");S.classList.add("ion-delegate-host"),a.forEach(R=>S.classList.add(R)),S.append(...e.children),e.appendChild(S),f=S}let m=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),m.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var ni='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',dy=(e,n)=>{let t=e.querySelector(ni);gy(t,n??e)},fy=(e,n)=>{let t=Array.from(e.querySelectorAll(ni)),r=t.length>0?t[t.length-1]:null;gy(r,n??e)},gy=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(ni)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():qa(t)}else n.focus()},$d=0,PM=0,$a=new WeakMap,my=e=>({create(t){return jM(e,t)},dismiss(t,r,o){return $M(document,t,r,e,o)},getTop(){return ie(this,null,function*(){return ti(document,e)})}});var LM=my("ion-modal");var VM=my("ion-popover");var Z$=e=>{typeof document<"u"&&UM(document);let n=$d++;e.overlayIndex=n},Y$=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++PM}`),e.id),jM=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),yy(document).appendChild(t),new Promise(r=>Rt(t,r))}):Promise.resolve(),BM=e=>e.classList.contains("overlay-hidden"),hy=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(ni)||e),t?qa(t):n.focus()},HM=(e,n)=>{let t=ti(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(QM))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")hy(t.lastFocus,t);else{let s=hf(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;dy(a,t),c===n.activeElement&&fy(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")hy(t.lastFocus,t);else{let s=t.lastFocus;dy(t),s===n.activeElement&&fy(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},UM=e=>{$d===0&&($d=1,e.addEventListener("focus",n=>{HM(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=ti(e);t?.backdropDismiss&&n.detail.register(gf,()=>{t.dismiss(void 0,py)})}),pf()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=ti(e);t?.backdropDismiss&&t.dismiss(void 0,py)}}))},$M=(e,n,t,r,o)=>{let i=ti(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},zM=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),za=(e,n)=>zM(e,n).filter(t=>!BM(t)),ti=(e,n,t)=>{let r=za(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},vy=(e=!1)=>{let t=yy(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},Q$=(e,n,t,r,o)=>ie(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(vy(!0),document.body.classList.add(Xa)),ZM(e.el),Cy(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=Ud(e),c=e.enterAnimation?e.enterAnimation:we.get(n,a==="ios"?t:r);(yield Dy(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&GM(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),GM=e=>ie(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(ni)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),K$=(e,n,t,r,o,i,s)=>ie(null,null,function*(){var a,c;if(!e.presented)return!1;let l=(Nt!==void 0?za(Nt):[]).filter(h=>h.tagName!=="ION-TOAST");l.length===1&&l[0].id===e.el.id&&(vy(!1),document.body.classList.remove(Xa)),e.presented=!1;try{Cy(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let h=Ud(e),f=e.leaveAnimation?e.leaveAnimation:we.get(r,h==="ios"?o:i);t!==qM&&(yield Dy(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),($a.get(e)||[]).forEach(E=>E.destroy()),$a.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(h){lf(`[${e.el.tagName.toLowerCase()}] - `,h)}return e.el.remove(),YM(),!0}),yy=e=>e.querySelector("ion-app")||e.body,Dy=(e,n,t,r)=>ie(null,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!we.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=$a.get(e)||[];return $a.set(e,[...s,i]),yield i.play(),!0}),J$=(e,n)=>{let t,r=new Promise(o=>t=o);return WM(e,n,o=>{t(o.detail)}),r},WM=(e,n,t)=>{let r=o=>{ff(e,n,r),t(o)};df(e,n,r)};var py="backdrop",qM="gesture",X$=39;var e2=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){ii(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let u=()=>{c.present()};return a.addEventListener("click",u),()=>{a.removeEventListener("click",u)}})(i,r)},removeClickListener:n}},Cy=e=>{Nt!==void 0&&Fr("android")&&e.setAttribute("aria-hidden","true")},ZM=e=>{var n;if(Nt===void 0)return;let t=za(Nt);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},YM=()=>{if(Nt===void 0)return;let e=za(Nt);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},QM="ion-disable-focus-trap";var KM=["tabsInner"];var JM=(()=>{class e{doc;_readyPromise;win;backButton=new B;keyboardDidShow=new B;keyboardDidHide=new B;pause=new B;resume=new B;resize=new B;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},Pr(this.pause,t,"pause",r),Pr(this.resume,t,"resume",r),Pr(this.backButton,t,"ionBackButton",r),Pr(this.resize,this.win,"resize",r),Pr(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),Pr(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return Fr(this.win,t)}platforms(){return Ha(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return XM(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(I(te),I($))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),XM=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},Pr=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},oi=(()=>{class e{location;serializer;router;topOutlet;direction=Ey;animated=Iy;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof St){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return ie(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=eT(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=Ey,this.animated=Iy,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=g({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(I(JM),I(bt),I(xn),I(ke,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),eT=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},Ey="auto",Iy=void 0,Sy=(()=>{class e{get(t,r){let o=zd();return o?o.get(t,r):null}getBoolean(t,r){let o=zd();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=zd();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),tT=new C("USERCONFIG"),zd=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},Ga=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},nT=(()=>{class e{zone=p($);applicationRef=p(Ct);config=p(tT);create(t,r,o){return new Wd(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Wd=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=g({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=rT(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},rT=(e,n,t,r,o,i,s,a,c,u,l,d)=>{let h=de.create({providers:iT(c),parent:t}),f=Cm(a,{environmentInjector:n,elementInjector:h}),m=f.instance,E=f.location.nativeElement;if(c)if(l&&m[l]!==void 0&&console.error(`[Ionic Error]: ${l} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${l}" property from ${a.name}.`),d===!0&&f.setInput!==void 0){let R=c,{modal:Fn,popover:Xt}=R,tf=rf(R,["modal","popover"]);for(let nf in tf)f.setInput(nf,tf[nf]);Fn!==void 0&&Object.assign(m,{modal:Fn}),Xt!==void 0&&Object.assign(m,{popover:Xt})}else Object.assign(m,c);if(u)for(let Fn of u)E.classList.add(Fn);let S=My(e,m,E);return s.appendChild(E),r.attachView(f.hostView),o.set(E,f),i.set(E,S),E},oT=[Za,Ya,Qa,Ka,Ja],My=(e,n,t)=>e.run(()=>{let r=oT.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),wy=new C("NavParamsToken"),iT=e=>[{provide:wy,useValue:e},{provide:Ga,useFactory:sT,deps:[wy]}],sT=e=>new Ga(e),aT=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},cT=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},ef=(e,n,t)=>{t.forEach(r=>e[r]=zr(n,r))};function Wa(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&aT(t,o),i&&cT(t,i),t}}var uT=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],lT=["present","dismiss","onDidDismiss","onWillDismiss"],O2=(()=>{let e=class qd{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),ef(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||qd)(v(Ye),v(Q),v($))};static \u0275dir=V({type:qd,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&Eo(i,qe,5),r&2){let s;vr(s=yr())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=Ur([Wa({inputs:uT,methods:lT})],e),e})(),dT=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],fT=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],k2=(()=>{let e=class Zd{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),ef(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Zd)(v(Ye),v(Q),v($))};static \u0275dir=V({type:Zd,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&Eo(i,qe,5),r&2){let s;vr(s=yr())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=Ur([Wa({inputs:dT,methods:fT})],e),e})(),hT=(e,n,t)=>t==="root"?Ty(e,n):t==="forward"?pT(e,n):gT(e,n),Ty=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),pT=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),gT=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):Ty(e,n),Yd=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},Ay=(e,n)=>n?e.stackId!==n.stackId:!0,mT=(e,n)=>{if(!e)return;let t=Ny(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},Ny=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),Ry=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Qd=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?Ny(n):void 0}createView(n,t){let r=Yd(this.router,t),o=n?.location?.nativeElement,i=My(this.zone,n.instance,o);return{id:this.nextId++,stackId:mT(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=Yd(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=Ay(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),u,l=this.router;l.getCurrentNavigation?u=l.getCurrentNavigation():l.navigations?.value&&(u=l.navigations.value),u?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let d=this.views.includes(n),h=this.insertView(n,r);d||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>vT(n,h,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,T(g({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&xy(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(Ry),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=hT(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,u=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),u.commit)?u.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return ie(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},vT=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{xy(e,n,t,r,o),i()})}):Promise.resolve(),xy=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(Ry)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},yT=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new J(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=b;stackWillChange=new U;stackDidChange=new U;activateEvents=new U;deactivateEvents=new U;parentContexts=p(At);location=p(je);environmentInjector=p(q);inputBinder=p(Oy,{optional:!0});supportsBindingToComponentInputs=!0;config=p(Sy);navCtrl=p(oi);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,u){this.parentOutlet=u,this.nativeEl=i.nativeElement,this.name=t||b,this.tabsPrefix=r==="true"?Yd(s,c):void 0,this.stackCtrl=new Qd(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>Rt(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=g({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,u=new J(null),l=this.createActivatedRouteProxy(u,t),d=new Kd(l,c,this.location.injector),h=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(h,{index:this.outletContent.length,injector:d,environmentInjector:r??this.environmentInjector}),u.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,l),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:Ay(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new Ie;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(se(o=>!!o),le(o=>this.currentActivatedRoute$.pipe(se(i=>i!==null&&i.component===o),le(i=>i&&i.activatedRoute[r]),Dc())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(qt("name"),qt("tabs"),v(bt),v(Q),v(ke),v($),v(Ie),v(e,12))};static \u0275dir=V({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),Kd=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Ie?this.route:n===At?this.childContexts:this.parent.get(n,t)}},Oy=new C(""),DT=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=Qn([r.queryParams,r.params,r.data]).pipe(le(([i,s,a],c)=>(a=g(g(g({},i),s),a),c===0?_(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=Jl(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),F2=()=>({provide:Oy,useFactory:CT,deps:[ke]});function CT(e){return e?.componentInputBindingEnabled?new DT:null}var ET=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],P2=(()=>{let e=class Jd{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||Jd)(v(yT,8),v(oi),v(Sy),v(Q),v($),v(Ye))};static \u0275dir=V({type:Jd,hostBindings:function(r,o){r&1&&Re("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=Ur([Wa({inputs:ET})],e),e})(),L2=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(v(_t),v(oi),v(Q),v(ke),v(Yo,8))};static \u0275dir=V({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&Re("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Ae]})}return e})(),V2=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(v(_t),v(oi),v(Q),v(ke),v(Yo,8))};static \u0275dir=V({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&Re("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Ae]})}return e})(),IT=["animated","animation","root","rootParams","swipeGesture"],wT=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],j2=(()=>{let e=class Xd{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),ef(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||Xd)(v(Q),v(q),v(de),v(nT),v($),v(Ye))};static \u0275dir=V({type:Xd,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=Ur([Wa({inputs:IT,methods:wT})],e),e})(),B2=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new U;ionTabsDidChange=new U;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let u=this.outlet.getRootView(o),l=u&&s===u.url&&u.savedExtras;return this.navCtrl.navigateRoot(s,T(g({},l),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,u=a?.savedExtras;return this.navCtrl.navigateRoot(c,T(g({},u),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(v(oi))};static \u0275dir=V({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&$l(KM,7,Q),r&2){let i;vr(i=yr())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&Re("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),_T=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),H2=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,ri(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),ri(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),ri(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(On)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>ri(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),ri(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(v(de),v(Q))};static \u0275dir=V({type:e,hostBindings:function(r,o){r&1&&Re("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),ri=e=>{_T(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=bT(n);Gd(n,r);let o=n.closest("ion-item");o&&(t?Gd(o,[...r,"item-has-value"]):Gd(o,r))})},bT=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&ST(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},Gd=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},ST=(e,n)=>e.substring(0,n.length)===n,_y=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},by=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};export{J as a,Ur as b,zr as c,Fe as d,D as e,et as f,p as g,Ki as h,q as i,Mh as j,Th as k,de as l,te as m,Ae as n,ct as o,qt as p,Q as q,nE as r,v as s,je as t,Rl as u,Dt as v,V as w,Ne as x,Gg as y,U as z,$ as A,Qg as B,Et as C,Xg as D,jl as E,Bl as F,Ys as G,Hl as H,Ul as I,em as J,aw as K,Re as L,uw as M,dw as N,fw as O,Eo as P,$l as Q,vr as R,yr as S,pw as T,Rw as U,rm as V,zl as W,om as X,sm as Y,Fw as Z,am as _,It as $,Bw as aa,Ye as ba,bt as ca,Nm as da,I_ as ea,w_ as fa,Rm as ga,L_ as ha,Ie as ia,Rv as ja,ke as ka,DS as la,ES as ma,MS as na,T$ as oa,Fr as pa,Ud as qa,kM as ra,O$ as sa,L$ as ta,V$ as ua,j$ as va,dy as wa,LM as xa,VM as ya,Z$ as za,Y$ as Aa,Q$ as Ba,K$ as Ca,J$ as Da,py as Ea,qM as Fa,X$ as Ga,e2 as Ha,QM as Ia,zv as Ja,Or as Ka,E$ as La,I$ as Ma,iM as Na,cM as Oa,_$ as Pa,dM as Qa,hM as Ra,gM as Sa,vM as Ta,b$ as Ua,oi as Va,Sy as Wa,tT as Xa,nT as Ya,Wa as Za,O2 as _a,k2 as $a,yT as ab,F2 as bb,P2 as cb,L2 as db,V2 as eb,j2 as fb,B2 as gb,_T as hb,H2 as ib,ri as jb,_y as kb,by as lb};
