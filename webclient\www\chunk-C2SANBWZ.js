import{c as I,d as w,e as C}from"./chunk-HRORXLUI.js";import{b as S}from"./chunk-55XXYEDJ.js";import"./chunk-FPOZYJOD.js";import{h as x,i as f}from"./chunk-OAHYJD5P.js";import{b as y}from"./chunk-CKP3SGE2.js";import{e as m,f as A,i as E,m as o,n as O,o as P,p as N}from"./chunk-7IRWVP2E.js";import{f as u}from"./chunk-S5EYVFES.js";var L=":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;max-width:100%;height:200px;font-size:22px;text-align:center}.assistive-focusable{left:0;right:0;top:0;bottom:0;position:absolute;z-index:1;pointer-events:none}.assistive-focusable:focus{outline:none}.picker-opts{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-width:26px;max-height:200px;outline:none;text-align:inherit;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none}.picker-item-empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.picker-opts::-webkit-scrollbar{display:none}::slotted(ion-picker-column-option){display:block;scroll-snap-align:center}.picker-item-empty,:host(:not([disabled])) ::slotted(ion-picker-column-option.option-disabled){scroll-snap-align:none}::slotted([slot=prefix]),::slotted([slot=suffix]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}::slotted([slot=prefix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:end;justify-content:end}::slotted([slot=suffix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:start;justify-content:start}:host(.picker-column-disabled) .picker-opts{overflow-y:hidden}:host(.picker-column-disabled) ::slotted(ion-picker-column-option){cursor:default;opacity:0.4;pointer-events:none}@media (any-hover: hover){:host(:focus) .picker-opts{outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}",V=class{constructor(s){E(this,s),this.ionChange=N(this,"ionChange",7),this.isScrolling=!1,this.isColumnVisible=!1,this.canExitInputMode=!0,this.updateValueTextOnScroll=!1,this.ariaLabel=null,this.isActive=!1,this.disabled=!1,this.color="primary",this.numericInput=!1,this.centerPickerItemInView=(e,t=!0,i=!0)=>{let{isColumnVisible:n,scrollEl:a}=this;if(n&&a){let c=e.offsetTop-3*e.clientHeight+e.clientHeight/2;a.scrollTop!==c&&(this.canExitInputMode=i,this.updateValueTextOnScroll=!1,a.scroll({top:c,left:0,behavior:t?"smooth":void 0}))}},this.setPickerItemActiveState=(e,t)=>{t?e.classList.add(b):e.classList.remove(b)},this.inputModeChange=e=>{if(!this.numericInput)return;let{useInputMode:t,inputModeColumn:i}=e.detail,n=i===void 0||i===this.el;if(!t||!n){this.setInputModeActive(!1);return}this.setInputModeActive(!0)},this.setInputModeActive=e=>{if(this.isScrolling){this.scrollEndCallback=()=>{this.isActive=e};return}this.isActive=e},this.initializeScrollListener=()=>{let e=m("ios"),{el:t,scrollEl:i}=this,n,a=this.activeItem,c=()=>{f(()=>{var p;if(!i)return;n&&(clearTimeout(n),n=void 0),this.isScrolling||(e&&I(),this.isScrolling=!0);let d=i.getBoundingClientRect(),v=d.x+d.width/2,g=d.y+d.height/2,k=t.getRootNode(),h=k instanceof ShadowRoot?k:y;if(h===void 0)return;let l=h.elementsFromPoint(v,g).find(r=>r.tagName==="ION-PICKER-COLUMN-OPTION");if(l===void 0){let r=h.elementFromPoint(v,g);r?.tagName==="ION-PICKER-COLUMN-OPTION"&&(l=r)}a!==void 0&&this.setPickerItemActiveState(a,!1),!(l===void 0||l.disabled)&&(l!==a&&(e&&w(),this.canExitInputMode&&this.exitInputMode()),a=l,this.setPickerItemActiveState(l,!0),this.updateValueTextOnScroll&&((p=this.assistiveFocusable)===null||p===void 0||p.setAttribute("aria-valuetext",this.getOptionValueText(l))),n=setTimeout(()=>{this.isScrolling=!1,this.updateValueTextOnScroll=!0,e&&C();let{scrollEndCallback:r}=this;r&&(r(),this.scrollEndCallback=void 0),this.canExitInputMode=!0,this.setValue(l.value)},250))})};f(()=>{i&&(i.addEventListener("scroll",c),this.destroyScrollListener=()=>{i.removeEventListener("scroll",c)})})},this.exitInputMode=()=>{let{parentEl:e}=this;e!=null&&(e.exitInputMode(),this.el.classList.remove("picker-column-active"))},this.findNextOption=(e=1)=>{let{activeItem:t}=this;if(!t)return null;let i=t,n=t.nextElementSibling;for(;n!=null;){if(e>0&&e--,n.tagName==="ION-PICKER-COLUMN-OPTION"&&!n.disabled&&e===0)return n;i=n,n=n.nextElementSibling}return i},this.findPreviousOption=(e=1)=>{let{activeItem:t}=this;if(!t)return null;let i=t,n=t.previousElementSibling;for(;n!=null;){if(e>0&&e--,n.tagName==="ION-PICKER-COLUMN-OPTION"&&!n.disabled&&e===0)return n;i=n,n=n.previousElementSibling}return i},this.onKeyDown=e=>{let t=m("mobile"),i=null;switch(e.key){case"ArrowDown":i=t?this.findPreviousOption():this.findNextOption();break;case"ArrowUp":i=t?this.findNextOption():this.findPreviousOption();break;case"PageUp":i=t?this.findNextOption(5):this.findPreviousOption(5);break;case"PageDown":i=t?this.findPreviousOption(5):this.findNextOption(5);break;case"Home":i=this.el.querySelector("ion-picker-column-option:first-of-type");break;case"End":i=this.el.querySelector("ion-picker-column-option:last-of-type");break}i!==null&&(this.setValue(i.value),e.preventDefault())},this.getOptionValueText=e=>{var t;return e?(t=e.getAttribute("aria-label"))!==null&&t!==void 0?t:e.innerText:""},this.renderAssistiveFocusable=()=>{let{activeItem:e}=this,t=this.getOptionValueText(e);return o("div",{ref:i=>this.assistiveFocusable=i,class:"assistive-focusable",role:"slider",tabindex:this.disabled?void 0:0,"aria-label":this.ariaLabel,"aria-valuemin":0,"aria-valuemax":0,"aria-valuenow":0,"aria-valuetext":t,"aria-orientation":"vertical",onKeyDown:i=>this.onKeyDown(i)})}}ariaLabelChanged(s){this.ariaLabel=s}valueChange(){this.isColumnVisible&&this.scrollActiveItemIntoView(!0)}componentWillLoad(){let s=this.parentEl=this.el.closest("ion-picker"),e=t=>{if(t[t.length-1].isIntersecting){let{activeItem:n,el:a}=this;this.isColumnVisible=!0;let c=x(a).querySelector(`.${b}`);c&&this.setPickerItemActiveState(c,!1),this.scrollActiveItemIntoView(),n&&this.setPickerItemActiveState(n,!0),this.initializeScrollListener()}else this.isColumnVisible=!1,this.destroyScrollListener&&(this.destroyScrollListener(),this.destroyScrollListener=void 0)};new IntersectionObserver(e,{threshold:.001,root:this.parentEl}).observe(this.el),s!==null&&s.addEventListener("ionInputModeChange",t=>this.inputModeChange(t))}componentDidRender(){let{el:s,activeItem:e,isColumnVisible:t,value:i}=this;if(t&&!e){let n=s.querySelector("ion-picker-column-option");n!==null&&n.value!==i&&this.setValue(n.value)}}scrollActiveItemIntoView(s=!1){return u(this,null,function*(){let e=this.activeItem;e&&this.centerPickerItemInView(e,s,!1)})}setValue(s){return u(this,null,function*(){this.disabled===!0||this.value===s||(this.value=s,this.ionChange.emit({value:s}))})}setFocus(){return u(this,null,function*(){this.assistiveFocusable&&this.assistiveFocusable.focus()})}connectedCallback(){var s;this.ariaLabel=(s=this.el.getAttribute("aria-label"))!==null&&s!==void 0?s:"Select a value"}get activeItem(){let{value:s}=this;return Array.from(this.el.querySelectorAll("ion-picker-column-option")).find(t=>!this.disabled&&t.disabled?!1:t.value===s)}render(){let{color:s,disabled:e,isActive:t,numericInput:i}=this,n=A(this);return o(O,{key:"ea0280355b2f87895bf7dddd289ccf473aa759f3",class:S(s,{[n]:!0,"picker-column-active":t,"picker-column-numeric-input":i,"picker-column-disabled":e})},this.renderAssistiveFocusable(),o("slot",{key:"482992131cdeb85b1f61430d7fe1322a16345769",name:"prefix"}),o("div",{key:"43f7f80d621d411ef366b3ca1396299e8c9a0c97","aria-hidden":"true",class:"picker-opts",ref:a=>{this.scrollEl=a},tabIndex:-1},o("div",{key:"13a9ee686132af32240710730765de4c0003a9e8",class:"picker-item-empty","aria-hidden":"true"},"\xA0"),o("div",{key:"dbccba4920833cfcebe9b0fc763458ec3053705a",class:"picker-item-empty","aria-hidden":"true"},"\xA0"),o("div",{key:"682b43f83a5ea2e46067457f3af118535e111edb",class:"picker-item-empty","aria-hidden":"true"},"\xA0"),o("slot",{key:"d27e1e1dc0504b2f4627a29912a05bb91e8e413a"}),o("div",{key:"61c948dbb9cf7469aed3018542bc0954211585ba",class:"picker-item-empty","aria-hidden":"true"},"\xA0"),o("div",{key:"cf46c277fbee65e35ff44ce0d53ce12aa9cbf9db",class:"picker-item-empty","aria-hidden":"true"},"\xA0"),o("div",{key:"bbc0e2d491d3f836ab849493ade2f7fa6ad9244e",class:"picker-item-empty","aria-hidden":"true"},"\xA0")),o("slot",{key:"d25cbbe14b2914fe7b878d43b4e3f4a8c8177d24",name:"suffix"}))}get el(){return P(this)}static get watchers(){return{"aria-label":["ariaLabelChanged"],value:["valueChange"]}}},b="option-active";V.style=L;export{V as ion_picker_column};
