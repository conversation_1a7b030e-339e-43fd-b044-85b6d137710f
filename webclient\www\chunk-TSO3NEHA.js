import{n as s}from"./chunk-WDNHH3UA.js";import"./chunk-WKAFHTGB.js";import{c as a}from"./chunk-55XXYEDJ.js";import"./chunk-OAHYJD5P.js";import"./chunk-WI5MSH4N.js";import"./chunk-E2SD7K6D.js";import"./chunk-CKP3SGE2.js";import{f as d,i as l,m as i,n as m,o as h,q as b}from"./chunk-7IRWVP2E.js";import"./chunk-S5EYVFES.js";var p=".sc-ion-select-modal-ionic-h{height:100%}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(container){display:none}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-ionic{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-ionic{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-ionic{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}",u='.sc-ion-select-modal-ios-h{height:100%}ion-item.sc-ion-select-modal-ios{--inner-padding-end:0}ion-radio.sc-ion-select-modal-ios::after{bottom:0;position:absolute;width:calc(100% - 0.9375rem - 16px);border-width:0px 0px 0.55px 0px;border-style:solid;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));content:""}ion-radio.sc-ion-select-modal-ios::after{inset-inline-start:calc(0.9375rem + 16px)}',k=".sc-ion-select-modal-md-h{height:100%}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(container){display:none}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}",j=(()=>{let n=class{constructor(o){l(this,o),this.options=[]}closeModal(){let o=this.el.closest("ion-modal");o&&o.dismiss()}findOptionFromEvent(o){let{options:e}=this;return e.find(t=>t.value===o.target.value)}getValues(o){let{multiple:e,options:t}=this;if(e)return t.filter(c=>c.checked).map(c=>c.value);let r=o?this.findOptionFromEvent(o):null;return r?r.value:void 0}callOptionHandler(o){let e=this.findOptionFromEvent(o),t=this.getValues(o);e?.handler&&s(e.handler,t)}setChecked(o){let{multiple:e}=this,t=this.findOptionFromEvent(o);e&&t&&(t.checked=o.detail.checked)}renderRadioOptions(){let o=this.options.filter(e=>e.checked).map(e=>e.value)[0];return i("ion-radio-group",{value:o,onIonChange:e=>this.callOptionHandler(e)},this.options.map(e=>i("ion-item",{lines:"none",class:Object.assign({"item-radio-checked":e.value===o},a(e.cssClass))},i("ion-radio",{value:e.value,disabled:e.disabled,justify:"start",labelPlacement:"end",onClick:()=>this.closeModal(),onKeyUp:t=>{t.key===" "&&this.closeModal()}},e.text))))}renderCheckboxOptions(){return this.options.map(o=>i("ion-item",{class:Object.assign({"item-checkbox-checked":o.checked},a(o.cssClass))},i("ion-checkbox",{value:o.value,disabled:o.disabled,checked:o.checked,justify:"start",labelPlacement:"end",onIonChange:e=>{this.setChecked(e),this.callOptionHandler(e),b(this)}},o.text)))}render(){return i(m,{key:"b6c0dec240b2e41985b15fdf4e5a6d3a145c1567",class:d(this)},i("ion-header",{key:"cd177e85ee0f62a60a3a708342d6ab6eb19a44dc"},i("ion-toolbar",{key:"aee8222a5a4daa540ad202b2e4cac1ef93d9558c"},this.header!==void 0&&i("ion-title",{key:"5f8fecc764d97bf840d3d4cfddeeccd118ab4436"},this.header),i("ion-buttons",{key:"919033950d7c2b0101f96a9c9698219de9f568ea",slot:"end"},i("ion-button",{key:"34b571cab6dced4bde555a077a21e91800829931",onClick:()=>this.closeModal()},"Close")))),i("ion-content",{key:"3c9153d26ba7a5a03d3b20fcd628d0c3031661a7"},i("ion-list",{key:"e00b222c071bc97c82ad1bba4db95a8a5c43ed6d"},this.multiple===!0?this.renderCheckboxOptions():this.renderRadioOptions())))}get el(){return h(this)}};return n.style={ionic:p,ios:u,md:k},n})();export{j as ion_select_modal};
