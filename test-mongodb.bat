@echo off
title Test MongoDB Connection
color 0B

echo.
echo ========================================
echo    TEST MONGODB CONNECTION
echo ========================================
echo.

:: Check if MongoDB process is running
echo [INFO] Checking if MongoDB process is running...
tasklist /fi "imagename eq mongod.exe" 2>nul | find /i "mongod.exe" >nul
if errorlevel 1 (
    echo [ERROR] MongoDB process (mongod.exe) is not running
    echo [INFO] Please start MongoDB first using start-server-mongodb.bat
    echo.
    pause
    exit /b 1
) else (
    echo [INFO] MongoDB process is running
)

:: Test connection
echo [INFO] Testing MongoDB connection...
cd server
node -e "const mongoose = require('mongoose'); mongoose.connect('mongodb://localhost:27017/jugshine', {serverSelectionTimeoutMS: 5000}).then(() => {console.log('✅ MongoDB connection successful'); console.log('📊 Database: jugshine'); mongoose.disconnect(); process.exit(0);}).catch(err => {console.error('❌ MongoDB connection failed:', err.message); process.exit(1);})"
if errorlevel 1 (
    echo [ERROR] MongoDB connection test failed
    cd ..
    echo.
    pause
    exit /b 1
) else (
    echo [SUCCESS] MongoDB is working correctly!
)
cd ..

echo.
echo [INFO] MongoDB test completed successfully.
echo [INFO] You can now start the server with confidence.
echo.
pause
