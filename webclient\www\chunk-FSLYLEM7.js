import{b as L,c as M,d as A,e as O,f as S,g as I,h as W,i as w,j as B,k as N,l as $,m as E,o as Y,p as H,q as U,r as K,s as z,t as q,u as Q,v as T,w as X}from"./chunk-GYCWT3F4.js";import"./chunk-NWICGN3P.js";import"./chunk-QUJFQN2Y.js";import"./chunk-33ZKSDCD.js";import"./chunk-VX6CYJTR.js";import"./chunk-XC7QA3KA.js";import"./chunk-XOTTEB5B.js";import"./chunk-WDNHH3UA.js";import"./chunk-WKAFHTGB.js";import"./chunk-55XXYEDJ.js";import"./chunk-PZCS62C4.js";import"./chunk-OAHYJD5P.js";import"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-E2SD7K6D.js";import"./chunk-CKP3SGE2.js";import"./chunk-7IRWVP2E.js";import{D as l,E as n,F as i,G as d,K as x,L as u,M as g,U as a,V as C,W as _,X as k,aa as D,da as G,ea as b,ga as v,j as f,k as h,ka as J,n as j,r as o,s as V,u as y,y as p,z as R}from"./chunk-FXSFTU3P.js";import"./chunk-EX6SW4ET.js";import"./chunk-BIG7K77F.js";import"./chunk-SM5Y6Q5Y.js";import"./chunk-PFDCGU6V.js";import"./chunk-B3E62VDJ.js";import"./chunk-M2X7KQLB.js";import"./chunk-XTVTS2NW.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import{f as P}from"./chunk-S5EYVFES.js";function oe(e,r){if(e&1&&(n(0,"ion-card",9)(1,"ion-card-content")(2,"div",10),d(3,"ion-icon",11),n(4,"h3"),a(5,"Waiting for Players"),i(),n(6,"p"),a(7,"Share the room code "),n(8,"strong"),a(9),i(),a(10," with your friends!"),i(),n(11,"p"),a(12),i()()()()),e&2){let t=g();o(9),C(t.game.room_code),o(3),k("",t.game.players.length," / ",t.game.settings.max_players," players joined")}}function ae(e,r){if(e&1&&(n(0,"ion-card",12)(1,"ion-card-header")(2,"ion-card-title"),a(3,"Current Prompt"),i()(),n(4,"ion-card-content")(5,"div",13)(6,"h2"),a(7),i()()()()),e&2){let t=g();o(7),C(t.game.game_data.current_prompt||"Loading prompt...")}}function re(e,r){if(e&1){let t=x();n(0,"ion-button",18),u("click",function(){let c=f(t).index,m=g(2);return h(m.selectResponse(c))}),a(1),i()}if(e&2){let t=r.$implicit,s=r.index,c=g(2);l("color",c.selectedResponseIndex===s?"primary":"medium"),o(),_(" ",t," ")}}function se(e,r){e&1&&d(0,"ion-spinner",22)}function ce(e,r){e&1&&(n(0,"span"),a(1,"Submit Response"),i())}function le(e,r){e&1&&(n(0,"span"),a(1,"Submitting..."),i())}function me(e,r){if(e&1){let t=x();n(0,"ion-button",19),u("click",function(){f(t);let c=g(2);return h(c.submitResponse())}),p(1,se,1,0,"ion-spinner",20)(2,ce,2,0,"span",21)(3,le,2,0,"span",21),i()}if(e&2){let t=g(2);l("disabled",t.isSubmitting),o(),l("ngIf",t.isSubmitting),o(),l("ngIf",!t.isSubmitting),o(),l("ngIf",t.isSubmitting)}}function ge(e,r){if(e&1&&(n(0,"ion-card",14)(1,"ion-card-header")(2,"ion-card-title"),a(3,"Choose Your Response"),i(),n(4,"ion-card-subtitle"),a(5,"Pick the funniest response to the prompt"),i()(),n(6,"ion-card-content")(7,"div",15),p(8,re,2,2,"ion-button",16),i(),p(9,me,4,4,"ion-button",17),i()()),e&2){let t=g();o(8),l("ngForOf",t.playerResponses),o(),l("ngIf",t.selectedResponseIndex!==null)}}function pe(e,r){if(e&1&&(n(0,"ion-card",23)(1,"ion-card-content")(2,"div",24),d(3,"ion-icon",25),n(4,"h3"),a(5,"You are the Judge!"),i(),n(6,"p"),a(7,"Wait for other players to submit their responses."),i(),n(8,"p"),a(9,"You'll choose the winner once everyone has responded."),i(),n(10,"div",26)(11,"p"),a(12),i(),d(13,"ion-progress-bar",27),i()()()()),e&2){let t=g();o(12),k("",t.getSubmittedResponsesCount()," / ",t.getExpectedResponsesCount()," responses received"),o(),l("value",t.getResponseProgress())}}function de(e,r){if(e&1&&(n(0,"ion-card",28)(1,"ion-card-content")(2,"div",29),d(3,"ion-icon",30),n(4,"h3"),a(5,"Response Submitted!"),i(),n(6,"p"),a(7,"Wait for other players to submit their responses."),i(),n(8,"p"),a(9,"Your response: "),n(10,"strong"),a(11),i()()()()()),e&2){let t=g();o(11),_('"',t.getPlayerSubmittedResponse(),'"')}}function _e(e,r){if(e&1){let t=x();n(0,"ion-button",18),u("click",function(){let c=f(t).index,m=g(2);return h(m.selectWinner(c))}),a(1),i()}if(e&2){let t=r.$implicit,s=r.index,c=g(2);l("color",c.selectedWinnerIndex===s?"success":"medium"),o(),_(' "',t.response,'" ')}}function ue(e,r){e&1&&d(0,"ion-spinner",22)}function fe(e,r){e&1&&(n(0,"span"),a(1,"Choose Winner"),i())}function he(e,r){e&1&&(n(0,"span"),a(1,"Selecting..."),i())}function xe(e,r){if(e&1){let t=x();n(0,"ion-button",34),u("click",function(){f(t);let c=g(2);return h(c.submitJudgment())}),p(1,ue,1,0,"ion-spinner",20)(2,fe,2,0,"span",21)(3,he,2,0,"span",21),i()}if(e&2){let t=g(2);l("disabled",t.isSubmitting),o(),l("ngIf",t.isSubmitting),o(),l("ngIf",!t.isSubmitting),o(),l("ngIf",t.isSubmitting)}}function Ce(e,r){if(e&1&&(n(0,"ion-card",31)(1,"ion-card-header")(2,"ion-card-title"),a(3,"Choose the Winner!"),i(),n(4,"ion-card-subtitle"),a(5),i()(),n(6,"ion-card-content")(7,"div",32),p(8,_e,2,2,"ion-button",16),i(),p(9,xe,4,4,"ion-button",33),i()()),e&2){let t=g();o(5),_('Select the best response to: "',t.game.game_data.current_prompt,'"'),o(3),l("ngForOf",t.game.game_data.submitted_responses),o(),l("ngIf",t.selectedWinnerIndex!==null)}}function Pe(e,r){if(e&1&&(n(0,"div",40),a(1),i()),e&2){let t=r.$implicit;o(),_(' "',t.response,'" ')}}function ye(e,r){if(e&1&&(n(0,"ion-card",35)(1,"ion-card-content")(2,"div",36),d(3,"ion-icon",37),n(4,"h3"),a(5,"Waiting for Judge"),i(),n(6,"p")(7,"strong"),a(8),i(),a(9," is choosing the winner..."),i(),n(10,"div",38)(11,"h4"),a(12,"Submitted Responses:"),i(),p(13,Pe,2,1,"div",39),i()()()()),e&2){let t=g();o(8),C(t.game.current_judge),o(5),l("ngForOf",t.game.game_data.submitted_responses)}}function be(e,r){if(e&1&&(n(0,"ion-card",41)(1,"ion-card-content")(2,"div",42),d(3,"ion-icon",43),n(4,"h3"),a(5,"\u{1F3C6} Winner! \u{1F3C6}"),i(),n(6,"h2"),a(7),i(),n(8,"div",44),a(9),i(),n(10,"p"),a(11,"+1 point awarded!"),i()()()()),e&2){let t=g();o(7),C(t.game.game_data.current_winner==null?null:t.game.game_data.current_winner.player_name),o(2),_(' "',t.game.game_data.current_winner==null?null:t.game.game_data.current_winner.response,'" ')}}function ve(e,r){if(e&1){let t=x();n(0,"div",50)(1,"ion-button",51),u("click",function(){f(t);let c=g(2);return h(c.playAgain())}),a(2," Play Again "),i(),n(3,"ion-button",52),u("click",function(){f(t);let c=g(2);return h(c.endGame())}),a(4," End Game "),i()()}}function Me(e,r){e&1&&(n(0,"p",53),a(1," Waiting for game leader to decide... "),i())}function Oe(e,r){if(e&1&&(n(0,"ion-card",45)(1,"ion-card-content")(2,"div",46),d(3,"ion-icon",47),n(4,"h3"),a(5,"\u{1F389} Game Complete! \u{1F389}"),i(),n(6,"h2"),a(7),i(),n(8,"p"),a(9),i(),p(10,ve,5,0,"div",48)(11,Me,2,0,"p",49),i()()()),e&2){let t,s,c=g();o(7),_("",(t=c.getGameWinner())==null?null:t.name," Wins!"),o(2),_("Final Score: ",(s=c.getGameWinner())==null?null:s.score," points"),o(),l("ngIf",c.player==null?null:c.player.is_leader),o(),l("ngIf",!(c.player!=null&&c.player.is_leader))}}var ne=(()=>{let r=class r{constructor(){this.actionSubmitted=new R,this.selectedResponseIndex=null,this.selectedWinnerIndex=null,this.isSubmitting=!1,this.playerResponses=[]}ngOnInit(){this.loadPlayerResponses()}ngOnChanges(){this.loadPlayerResponses(),this.game.game_status!=="Waiting for Player Responses"&&(this.selectedResponseIndex=null),this.game.game_status!=="Ready for Judging"&&(this.selectedWinnerIndex=null)}get isJudge(){return this.game.current_judge===this.player?.name}loadPlayerResponses(){this.game.game_data.player_responses&&this.player?.name&&(this.playerResponses=this.game.game_data.player_responses[this.player.name]||[])}selectResponse(s){this.isSubmitting||this.hasSubmittedResponse()||(this.selectedResponseIndex=s)}submitResponse(){return P(this,null,function*(){if(this.selectedResponseIndex===null||this.isSubmitting||this.hasSubmittedResponse())return;let s=this.playerResponses[this.selectedResponseIndex];if(s){this.isSubmitting=!0;try{this.actionSubmitted.emit({type:"submit_response",data:{response:s}})}finally{this.isSubmitting=!1}}})}selectWinner(s){this.isSubmitting||!this.isJudge||(this.selectedWinnerIndex=s)}submitJudgment(){return P(this,null,function*(){if(!(this.selectedWinnerIndex===null||this.isSubmitting||!this.isJudge)){this.isSubmitting=!0;try{this.actionSubmitted.emit({type:"judge",data:{response_index:this.selectedWinnerIndex}})}finally{this.isSubmitting=!1}}})}hasSubmittedResponse(){return!this.game.game_data.submitted_responses||!this.player?.name?!1:this.game.game_data.submitted_responses.some(s=>s.player_name===this.player.name)}getPlayerSubmittedResponse(){return!this.game.game_data.submitted_responses||!this.player?.name?"":this.game.game_data.submitted_responses.find(c=>c.player_name===this.player.name)?.response||""}getSubmittedResponsesCount(){return this.game.game_data.submitted_responses?.length||0}getExpectedResponsesCount(){return this.game.players.length-1}getResponseProgress(){let s=this.getSubmittedResponsesCount(),c=this.getExpectedResponsesCount();return c>0?s/c:0}isGameComplete(){return this.game.players.some(s=>s.score>=this.game.settings.rounds_to_win)}getGameWinner(){return this.game.players.find(s=>s.score>=this.game.settings.rounds_to_win)||null}playAgain(){this.player?.is_leader&&this.actionSubmitted.emit({type:"play_again",data:{}})}endGame(){this.player?.is_leader&&this.actionSubmitted.emit({type:"end_game",data:{}})}};r.\u0275fac=function(c){return new(c||r)},r.\u0275cmp=y({type:r,selectors:[["app-poisoning-pigeons"]],inputs:{game:"game",player:"player"},outputs:{actionSubmitted:"actionSubmitted"},features:[j],decls:9,vars:9,consts:[["class","waiting-card",4,"ngIf"],["class","instructions-card",4,"ngIf"],["class","response-selection-card",4,"ngIf"],["class","judge-waiting-card",4,"ngIf"],["class","submitted-card",4,"ngIf"],["class","judge-selection-card",4,"ngIf"],["class","waiting-judge-card",4,"ngIf"],["class","winner-card",4,"ngIf"],["class","game-complete-card",4,"ngIf"],[1,"waiting-card"],[1,"waiting-content"],["name","people-outline","size","large","color","primary"],[1,"instructions-card"],[1,"prompt-display"],[1,"response-selection-card"],[1,"response-options"],["expand","block","fill","outline","class","response-button",3,"color","click",4,"ngFor","ngForOf"],["expand","block","class","submit-button",3,"disabled","click",4,"ngIf"],["expand","block","fill","outline",1,"response-button",3,"click","color"],["expand","block",1,"submit-button",3,"click","disabled"],["name","crescent","slot","start",4,"ngIf"],[4,"ngIf"],["name","crescent","slot","start"],[1,"judge-waiting-card"],[1,"judge-waiting-content"],["name","gavel-outline","size","large","color","secondary"],[1,"response-progress"],["color","secondary",3,"value"],[1,"submitted-card"],[1,"submitted-content"],["name","checkmark-circle-outline","size","large","color","success"],[1,"judge-selection-card"],[1,"submitted-responses"],["expand","block","color","success","class","submit-button",3,"disabled","click",4,"ngIf"],["expand","block","color","success",1,"submit-button",3,"click","disabled"],[1,"waiting-judge-card"],[1,"waiting-judge-content"],["name","hourglass-outline","size","large","color","warning"],[1,"submitted-responses-preview"],["class","response-preview",4,"ngFor","ngForOf"],[1,"response-preview"],[1,"winner-card"],[1,"winner-content"],["name","trophy-outline","size","large","color","warning"],[1,"winning-response"],[1,"game-complete-card"],[1,"game-complete-content"],["name","medal-outline","size","large","color","success"],["class","leader-options",4,"ngIf"],["class","waiting-leader",4,"ngIf"],[1,"leader-options"],["expand","block","color","primary",3,"click"],["expand","block","fill","outline",3,"click"],[1,"waiting-leader"]],template:function(c,m){c&1&&p(0,oe,13,3,"ion-card",0)(1,ae,8,1,"ion-card",1)(2,ge,10,2,"ion-card",2)(3,pe,14,3,"ion-card",3)(4,de,12,1,"ion-card",4)(5,Ce,10,3,"ion-card",5)(6,ye,14,2,"ion-card",6)(7,be,12,2,"ion-card",7)(8,Oe,12,4,"ion-card",8),c&2&&(l("ngIf",m.game.game_status==="Waiting for Players"),o(),l("ngIf",m.game.game_status==="Ready"||m.game.game_status==="Waiting for Player Responses"),o(),l("ngIf",m.game.game_status==="Waiting for Player Responses"&&!m.isJudge),o(),l("ngIf",m.game.game_status==="Waiting for Player Responses"&&m.isJudge),o(),l("ngIf",m.game.game_status==="Waiting for Player Responses"&&!m.isJudge&&m.hasSubmittedResponse()),o(),l("ngIf",m.game.game_status==="Ready for Judging"&&m.isJudge),o(),l("ngIf",m.game.game_status==="Ready for Judging"&&!m.isJudge),o(),l("ngIf",m.game.game_status==="Winner Chosen"),o(),l("ngIf",m.isGameComplete()))},dependencies:[v,G,b,T,M,O,S,I,W,w,E,K,z],styles:["ion-card[_ngcontent-%COMP%]{margin-bottom:16px}.waiting-content[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.waiting-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-bottom:16px}.waiting-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .waiting-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 12px;font-weight:600}.waiting-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.4rem;color:var(--ion-color-primary);line-height:1.4}.waiting-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.3rem}.waiting-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;color:var(--ion-color-medium)}.waiting-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .prompt-display[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-primary);font-weight:700;font-size:1.1rem}.response-button[_ngcontent-%COMP%]{margin-bottom:12px;height:auto;min-height:60px;white-space:normal;text-align:left}.response-button.button-outline-primary[_ngcontent-%COMP%]{--border-width:2px}.submit-button[_ngcontent-%COMP%]{height:50px;font-weight:700}.judge-waiting-content[_ngcontent-%COMP%], .submitted-content[_ngcontent-%COMP%], .waiting-judge-content[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.judge-waiting-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-secondary)}.submitted-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-success)}.submitted-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-style:italic}.judge-selection-card[_ngcontent-%COMP%]   .response-button.button-outline-success[_ngcontent-%COMP%]{--border-width:2px}.waiting-judge-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-warning)}.submitted-responses-preview[_ngcontent-%COMP%]{margin-top:20px;text-align:left}.submitted-responses-preview[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;font-size:1rem;font-weight:600;text-align:center}.response-preview[_ngcontent-%COMP%]{background:var(--ion-color-light);padding:12px;margin-bottom:8px;border-radius:8px;border-left:4px solid var(--ion-color-primary);font-style:italic}.response-progress[_ngcontent-%COMP%]{margin-top:20px}.response-progress[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:8px;font-weight:500}.response-progress[_ngcontent-%COMP%]   ion-progress-bar[_ngcontent-%COMP%]{height:8px;border-radius:4px}.winner-content[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.winner-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px}.winner-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 16px;font-weight:700}.winner-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .game-complete-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:500}.winner-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-warning)}.winner-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.8rem;color:var(--ion-color-success)}.winning-response[_ngcontent-%COMP%]{background:var(--ion-color-success-tint);color:var(--ion-color-success-contrast);padding:16px;border-radius:12px;margin:16px 0;font-size:1.1rem;font-style:italic;font-weight:500}.game-complete-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-success)}.game-complete-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2rem;color:var(--ion-color-primary)}.game-complete-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem}.leader-options[_ngcontent-%COMP%]{margin-top:24px}.leader-options[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-bottom:12px;height:50px;font-weight:700}.waiting-leader[_ngcontent-%COMP%]{margin-top:20px;color:var(--ion-color-warning);font-style:italic}@media (max-width: 768px){.response-button[_ngcontent-%COMP%]{font-size:.9rem;min-height:50px}.prompt-display[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.2rem}.winner-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.6rem}}@media (prefers-color-scheme: dark){.response-preview[_ngcontent-%COMP%]{background:var(--ion-color-step-100);border-left-color:var(--ion-color-primary-tint)}.winning-response[_ngcontent-%COMP%]{background:var(--ion-color-success-shade)}}"]});let e=r;return e})();function Se(e,r){if(e&1){let t=x();n(0,"div",3)(1,"ion-button",4),u("click",function(){f(t);let c=g();return h(c.goBackToMenu())}),a(2," Return to Main Menu "),i()()}}var ie=(()=>{let r=class r{constructor(){this.actionSubmitted=new R}goBackToMenu(){this.actionSubmitted.emit({type:"end_game",data:{}})}};r.\u0275fac=function(c){return new(c||r)},r.\u0275cmp=y({type:r,selectors:[["app-game-placeholder"]],inputs:{game:"game",player:"player",gameTitle:"gameTitle"},outputs:{actionSubmitted:"actionSubmitted"},decls:16,vars:4,consts:[[1,"placeholder-content"],["name","construct-outline","size","large","color","warning"],["class","leader-controls",4,"ngIf"],[1,"leader-controls"],["expand","block","fill","outline",3,"click"]],template:function(c,m){c&1&&(n(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),a(3),i(),n(4,"ion-card-subtitle"),a(5,"Coming Soon!"),i()(),n(6,"ion-card-content")(7,"div",0),d(8,"ion-icon",1),n(9,"h3"),a(10,"This game is under development"),i(),n(11,"p"),a(12),i(),n(13,"p"),a(14),i(),p(15,Se,3,0,"div",2),i()()()),c&2&&(o(3),C(m.gameTitle),o(9),_("",m.gameTitle," will be available in a future update."),o(2),_("Current status: ",m.game.game_status),o(),l("ngIf",m.player==null?null:m.player.is_leader))},dependencies:[v,b,T,M,O,S,I,W,w,E],styles:[".placeholder-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.placeholder-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-bottom:20px}.placeholder-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 16px;font-size:1.3rem;font-weight:600}.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;color:var(--ion-color-medium)}.placeholder-content[_ngcontent-%COMP%]   .leader-controls[_ngcontent-%COMP%]{margin-top:24px}"]});let e=r;return e})();var Ie=()=>["OK"];function we(e,r){e&1&&(n(0,"div",10),d(1,"ion-spinner",11),n(2,"p"),a(3,"Loading game..."),i()())}function Ee(e,r){e&1&&(n(0,"ion-chip",31),d(1,"ion-icon",32),n(2,"ion-label"),a(3,"Leader"),i()())}function Te(e,r){e&1&&(n(0,"ion-chip",17),d(1,"ion-icon",33),n(2,"ion-label"),a(3,"Judge"),i()())}function ke(e,r){if(e&1&&(n(0,"ion-card",25)(1,"ion-card-content")(2,"div",26)(3,"h3"),a(4),i(),n(5,"div",27),p(6,Ee,4,0,"ion-chip",28)(7,Te,4,0,"ion-chip",29),n(8,"ion-chip",30)(9,"ion-label"),a(10),i()()()()()()),e&2){let t=g(2);o(4),C(t.player.name),o(2),l("ngIf",t.player.is_leader),o(),l("ngIf",t.isPlayerJudge()),o(3),_("",t.player.score," points")}}function Re(e,r){e&1&&(n(0,"ion-chip",38),d(1,"ion-icon",32),n(2,"ion-label"),a(3,"Leader"),i()())}function Ge(e,r){e&1&&(n(0,"ion-chip",39),d(1,"ion-icon",33),n(2,"ion-label"),a(3,"Judge"),i()())}function We(e,r){if(e&1&&(n(0,"ion-item")(1,"div",34),a(2),i(),n(3,"ion-label")(4,"h3"),a(5),i(),n(6,"p"),a(7),i()(),n(8,"div",35),p(9,Re,4,0,"ion-chip",36)(10,Ge,4,0,"ion-chip",37),i()()),e&2){let t=r.$implicit,s=g(2);o(2),_(" ",t.name.charAt(0).toUpperCase()," "),o(3),C(t.name),o(2),_("",t.score," points"),o(2),l("ngIf",t.is_leader),o(),l("ngIf",s.game.current_judge===t.name)}}function ze(e,r){e&1&&(n(0,"p",44),a(1," Need at least 2 players to start "),i())}function Ve(e,r){if(e&1){let t=x();n(0,"ion-card",40)(1,"ion-card-header")(2,"ion-card-title"),a(3,"Game Controls"),i()(),n(4,"ion-card-content")(5,"ion-button",41),u("click",function(){f(t);let c=g(2);return h(c.startGame())}),d(6,"ion-icon",42),a(7," Start Game "),i(),p(8,ze,2,0,"p",43),i()()}if(e&2){let t=g(2);o(5),l("disabled",t.game.players.length<2),o(3),l("ngIf",t.game.players.length<2)}}function Fe(e,r){if(e&1){let t=x();n(0,"app-poisoning-pigeons",45),u("actionSubmitted",function(c){f(t);let m=g(2);return h(m.onActionSubmitted(c))}),i()}if(e&2){let t=g(2);l("game",t.game)("player",t.player)}}function je(e,r){if(e&1){let t=x();n(0,"app-game-placeholder",46),u("actionSubmitted",function(c){f(t);let m=g(2);return h(m.onActionSubmitted(c))}),i()}if(e&2){let t=g(2);l("game",t.game)("player",t.player)("gameTitle",t.getGameTitle())}}function De(e,r){if(e&1&&(n(0,"div",12)(1,"div",13)(2,"div",14)(3,"h2"),a(4),i(),n(5,"div",15)(6,"ion-chip",16)(7,"ion-label"),a(8),i()(),n(9,"ion-chip",1)(10,"ion-label"),a(11),i()(),n(12,"ion-chip",17)(13,"ion-label"),a(14),i()()()()(),p(15,ke,11,4,"ion-card",18),n(16,"ion-card",19)(17,"ion-card-header")(18,"ion-card-title"),a(19),i()(),n(20,"ion-card-content")(21,"ion-list"),p(22,We,11,5,"ion-item",20),i()()(),p(23,Ve,9,2,"ion-card",21),n(24,"div",22),p(25,Fe,1,2,"app-poisoning-pigeons",23)(26,je,1,3,"app-game-placeholder",24),i()()),e&2){let t=g();o(4),C(t.getGameTitle()),o(2),l("color",t.getStatusColor()),o(2),C(t.game.game_status),o(3),_("Room: ",t.game.room_code),o(3),_("Round ",t.game.current_round),o(),l("ngIf",t.player),o(4),k("Players (",t.game.players.length,"/",t.game.settings.max_players,")"),o(3),l("ngForOf",t.game.players),o(),l("ngIf",(t.player==null?null:t.player.is_leader)&&t.game.game_status==="Waiting for Players"),o(2),l("ngIf",t.game.game_type==="poisoning_pigeons"),o(),l("ngIf",t.game.game_type!=="poisoning_pigeons")}}var st=(()=>{let r=class r{constructor(s,c){this.gameService=s,this.router=c,this.game=null,this.player=null,this.errorMessage=null,this.showLeaveConfirmation=!1,this.leaveGameButtons=[{text:"Cancel",role:"cancel"},{text:"Leave",handler:()=>this.confirmLeaveGame()}],this.subscriptions=[]}ngOnInit(){this.subscriptions.push(this.gameService.game$.subscribe(s=>{this.game=s,s||this.router.navigate(["/home"])})),this.subscriptions.push(this.gameService.player$.subscribe(s=>{this.player=s})),this.subscriptions.push(this.gameService.error$.subscribe(s=>{this.errorMessage=s})),this.gameService.getCurrentGame()||this.gameService.reconnectToGame().then(s=>{s||this.router.navigate(["/home"])})}ngOnDestroy(){this.subscriptions.forEach(s=>s.unsubscribe())}getGameTitle(){if(!this.game)return"Game";switch(this.game.game_type){case"poisoning_pigeons":return"Poisoning Pigeons";case"florida_man":return"Florida Man";case"washington_path":return"Washington Path";case"pick_your_nose":return"Pick Your Own Nose";default:return"Unknown Game"}}getStatusColor(){if(!this.game)return"medium";switch(this.game.game_status){case"Waiting for Players":return"warning";case"Ready":return"success";case"Waiting for Player Responses":return"primary";case"Ready for Judging":return"secondary";case"Winner Chosen":return"success";case"Closed":return"danger";default:return"medium"}}isPlayerJudge(){return this.gameService.isPlayerJudge()}startGame(){return P(this,null,function*(){if(!this.player?.is_leader){this.errorMessage="Only the game leader can start the game";return}try{(yield this.gameService.submitAction("start_game",{}))||(this.errorMessage="Failed to start game")}catch(s){console.error("Error starting game:",s),this.errorMessage="Failed to start game"}})}onActionSubmitted(s){return P(this,null,function*(){try{(yield this.gameService.submitAction(s.type,s.data))||(this.errorMessage="Failed to submit action")}catch(c){console.error("Error submitting action:",c),this.errorMessage="Failed to submit action"}})}leaveGame(){this.showLeaveConfirmation=!0}confirmLeaveGame(){return P(this,null,function*(){try{yield this.gameService.leaveGame(),this.router.navigate(["/home"])}catch(s){console.error("Error leaving game:",s),this.errorMessage="Failed to leave game"}})}clearError(){this.errorMessage=null}};r.\u0275fac=function(c){return new(c||r)(V(X),V(J))},r.\u0275cmp=y({type:r,selectors:[["app-game"]],decls:12,vars:11,consts:[[3,"translucent"],["color","primary"],["slot","end"],["fill","clear",3,"click"],["name","exit-outline"],[1,"ion-padding",3,"fullscreen"],["class","loading-container",4,"ngIf"],["class","game-container",4,"ngIf"],["header","Error",3,"didDismiss","isOpen","message","buttons"],["header","Leave Game","message","Are you sure you want to leave this game?",3,"didDismiss","isOpen","buttons"],[1,"loading-container"],["name","crescent"],[1,"game-container"],[1,"game-header"],[1,"game-info"],[1,"game-stats"],[3,"color"],["color","secondary"],["class","player-info",4,"ngIf"],[1,"players-list"],[4,"ngFor","ngForOf"],["class","leader-controls",4,"ngIf"],[1,"game-component"],[3,"game","player","actionSubmitted",4,"ngIf"],[3,"game","player","gameTitle","actionSubmitted",4,"ngIf"],[1,"player-info"],[1,"player-details"],[1,"player-badges"],["color","warning",4,"ngIf"],["color","secondary",4,"ngIf"],["color","success"],["color","warning"],["name","star"],["name","gavel"],["slot","start",1,"player-avatar"],["slot","end",1,"player-badges-list"],["color","warning","size","small",4,"ngIf"],["color","secondary","size","small",4,"ngIf"],["color","warning","size","small"],["color","secondary","size","small"],[1,"leader-controls"],["expand","block","color","success",3,"click","disabled"],["name","play","slot","start"],["class","min-players-warning",4,"ngIf"],[1,"min-players-warning"],[3,"actionSubmitted","game","player"],[3,"actionSubmitted","game","player","gameTitle"]],template:function(c,m){c&1&&(n(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-title"),a(3),i(),n(4,"ion-buttons",2)(5,"ion-button",3),u("click",function(){return m.leaveGame()}),d(6,"ion-icon",4),i()()()(),n(7,"ion-content",5),p(8,we,4,0,"div",6)(9,De,27,12,"div",7),n(10,"ion-alert",8),u("didDismiss",function(){return m.clearError()}),i(),n(11,"ion-alert",9),u("didDismiss",function(){return m.showLeaveConfirmation=!1}),i()()),c&2&&(l("translucent",!0),o(3),_(" ",m.getGameTitle()," "),o(4),l("fullscreen",!0),o(),l("ngIf",!m.game),o(),l("ngIf",m.game),o(),l("isOpen",!!m.errorMessage)("message",m.errorMessage)("buttons",D(10,Ie)),o(),l("isOpen",m.showLeaveConfirmation)("buttons",m.leaveGameButtons))},dependencies:[v,G,b,T,L,M,A,O,S,I,w,B,N,$,E,Y,H,U,z,q,Q,ne,ie],styles:[".loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:50vh}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-bottom:20px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:1.1rem}.game-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.game-header[_ngcontent-%COMP%]{margin-bottom:16px}.game-header[_ngcontent-%COMP%]   .game-info[_ngcontent-%COMP%]{text-align:center}.game-header[_ngcontent-%COMP%]   .game-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 16px;font-size:1.5rem;font-weight:700;color:var(--ion-color-primary)}.game-header[_ngcontent-%COMP%]   .game-info[_ngcontent-%COMP%]   .game-stats[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:8px;flex-wrap:wrap}.player-info[_ngcontent-%COMP%]{margin-bottom:16px}.player-info[_ngcontent-%COMP%]   .player-details[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.player-info[_ngcontent-%COMP%]   .player-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.2rem;font-weight:600}.player-info[_ngcontent-%COMP%]   .player-details[_ngcontent-%COMP%]   .player-badges[_ngcontent-%COMP%]{display:flex;gap:4px;flex-wrap:wrap}.players-list[_ngcontent-%COMP%]{margin-bottom:16px}.players-list[_ngcontent-%COMP%]   .player-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:var(--ion-color-primary);color:#fff;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:1.1rem}.players-list[_ngcontent-%COMP%]   .player-badges-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px;align-items:flex-end}.players-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}.players-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-weight:600}.players-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.9rem}.leader-controls[_ngcontent-%COMP%]{margin-bottom:16px}.leader-controls[_ngcontent-%COMP%]   .min-players-warning[_ngcontent-%COMP%]{margin:12px 0 0;color:var(--ion-color-warning);font-size:.9rem;text-align:center}.game-component[_ngcontent-%COMP%]{margin-bottom:16px}@media (max-width: 768px){.game-container[_ngcontent-%COMP%]{padding:0 8px}.player-details[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:8px}.game-stats[_ngcontent-%COMP%]{justify-content:flex-start!important}}@media (prefers-color-scheme: dark){.game-header[_ngcontent-%COMP%], .player-info[_ngcontent-%COMP%], .players-list[_ngcontent-%COMP%], .leader-controls[_ngcontent-%COMP%]{--background: var(--ion-color-step-50)}.player-avatar[_ngcontent-%COMP%]{background:var(--ion-color-primary-shade)}}"]});let e=r;return e})();export{st as GamePage};
