extends Node

## Simple test script to verify Socket.IO functionality
## This script can be attached to a test scene to verify the WebSocket connection works

func _ready():
	print("Testing Socket.IO implementation...")
	
	# Connect to WebSocket signals to monitor connection status
	WebSocket.connection_status_changed.connect(_on_connection_status_changed)
	WebSocket.server_error.connect(_on_server_error)
	WebSocket.game_updated.connect(_on_game_updated)
	
	# Test connection
	print("Attempting to connect to server...")
	WebSocket.connect_to_server()


func _on_connection_status_changed(is_connected: bool):
	if is_connected:
		print("✓ Successfully connected to server!")
		print("Socket ID: ", WebSocket.get_socket_id())
		
		# Test sending an event
		print("Testing event sending...")
		WebSocket.send_event("test-event", {"message": "Hello from Godot!"})
	else:
		print("✗ Disconnected from server")


func _on_server_error(error_message: String):
	print("✗ Server error: ", error_message)


func _on_game_updated(game_state: Dictionary):
	print("✓ Received game update: ", game_state)


func _input(event):
	if event.is_action_pressed("ui_cancel"):
		print("Disconnecting from server...")
		WebSocket.disconnect_from_server()
		get_tree().quit()
