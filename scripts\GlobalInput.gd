extends Node

## Global input handler for returning to the main menu.
## This will be an autoloaded singleton.

# Emitted when the "back to menu" action is triggered.
signal back_to_menu_requested

func _unhandled_input(event: InputEvent):
	# Check for keyboard presses for Escape and Backspace
	if event is InputEventKey and event.is_pressed() and not event.is_echo():
		if event.keycode == KEY_ESCAPE or event.keycode == KEY_BACKSPACE:
			# Emit the signal and consume the event to prevent further processing
			back_to_menu_requested.emit()
			get_viewport().set_input_as_handled()

	# Check for controller "B" button (or "Circle" on PlayStation)
	# Joypad "Button 1" is typically the "B" / "East" button on most standard controllers.
	if event is InputEventJoypadButton and event.is_pressed():
		if event.button_index == JOY_BUTTON_B:
			# Emit the signal and consume the event
			back_to_menu_requested.emit()
			get_viewport().set_input_as_handled()