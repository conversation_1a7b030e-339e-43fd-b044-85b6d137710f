import{q as c,r as l}from"./chunk-DFAMUM5K.js";import{b as d}from"./chunk-55XXYEDJ.js";import{b as n,f as p,i as u,m as o,n as h,o as f}from"./chunk-7IRWVP2E.js";import"./chunk-S5EYVFES.js";var m="",I="",x=(()=>{let i=class{constructor(t){u(this,t),this.type="password",this.togglePasswordVisibility=()=>{let{inputElRef:s}=this;s&&(s.type=s.type==="text"?"password":"text")}}onTypeChange(t){if(t!=="text"&&t!=="password"){n(`[ion-input-password-toggle] - Only inputs of type "text" or "password" are supported. Input of type "${t}" is not compatible.`,this.el);return}}connectedCallback(){let{el:t}=this,s=this.inputElRef=t.closest("ion-input");if(!s){n("[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.",t);return}this.type=s.type}disconnectedCallback(){this.inputElRef=null}render(){var t,s;let{color:r,type:g}=this,a=p(this),y=(t=this.showIcon)!==null&&t!==void 0?t:c,w=(s=this.hideIcon)!==null&&s!==void 0?s:l,e=g==="text";return o(h,{key:"91bc55664d496fe457518bd112865dd7811d0c17",class:d(r,{[a]:!0})},o("ion-button",{key:"f3e436422110c9cb4d5c0b83500255b24ab4cdef",mode:a,color:r,fill:"clear",shape:"round","aria-checked":e?"true":"false","aria-label":e?"Hide password":"Show password",role:"switch",type:"button",onPointerDown:b=>{b.preventDefault()},onClick:this.togglePasswordVisibility},o("ion-icon",{key:"5c8b121153f148f92aa7cba0447673a4f6f3ad1e",slot:"icon-only","aria-hidden":"true",icon:e?w:y})))}get el(){return f(this)}static get watchers(){return{type:["onTypeChange"]}}};return i.style={ios:m,md:I},i})();export{x as ion_input_password_toggle};
