// Test script to verify start game functionality
async function testStartGame() {
  try {
    // Create a game
    console.log('Creating game...');
    const gameResponse = await fetch('http://localhost:3000/api/games', {
      method: 'POST', 
      headers: {'Content-Type': 'application/json'}, 
      body: JSON.stringify({
        game_type: 'poisoning_pigeons', 
        settings: {
          mature_content: false, 
          max_players: 8, 
          rounds_to_win: 5
        }
      })
    });
    const game = await gameResponse.json();
    console.log('✅ Game created:', game.room_code);
    
    // Add a leader player
    console.log('Adding leader...');
    const leaderResponse = await fetch(`http://localhost:3000/api/games/${game.room_code}/players`, {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({name: 'Leader', connection_id: 'leader123'})
    });
    const leader = await leaderResponse.json();
    console.log('✅ Leader joined:', leader.player?.name, '(is_leader:', leader.player?.is_leader, ')');

    // Add another player
    console.log('Adding second player...');
    const player2Response = await fetch(`http://localhost:3000/api/games/${game.room_code}/players`, {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({name: 'Player2', connection_id: 'player123'})
    });
    const player2 = await player2Response.json();
    console.log('✅ Player2 joined:', player2.player?.name);
    
    // Start the game
    console.log('Starting game...');
    const startResponse = await fetch(`http://localhost:3000/api/players/${game.room_code}/Leader/actions`, {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({action_type: 'start_game'})
    });
    
    if (!startResponse.ok) {
      const errorText = await startResponse.text();
      console.error('❌ Start game failed:', startResponse.status, errorText);
      return;
    }
    
    const startResult = await startResponse.json();
    console.log('✅ Start game result:', startResult);
    
    // Check final game state
    console.log('Checking final game state...');
    const finalGameResponse = await fetch(`http://localhost:3000/api/games/${game.room_code}`);
    const finalGame = await finalGameResponse.json();
    
    console.log('\n🎮 Final Game State:');
    console.log('  Status:', finalGame.game_status);
    console.log('  Round:', finalGame.current_round);
    console.log('  Judge:', finalGame.current_judge);
    console.log('  Prompt:', finalGame.game_data?.current_prompt);
    console.log('  Players:', finalGame.players.length);
    
    if (finalGame.game_status === 'Waiting for Player Responses') {
      console.log('\n🎉 SUCCESS! Game started correctly!');
    } else {
      console.log('\n❌ Game status is not correct. Expected "Waiting for Player Responses"');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testStartGame();
