import{h as u}from"./chunk-OAHYJD5P.js";import{i as h,m as l,n as b,o as p,p as f}from"./chunk-7IRWVP2E.js";import{f as d}from"./chunk-S5EYVFES.js";var m=":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:200px;direction:ltr;z-index:0}:host .picker-before,:host .picker-after{position:absolute;width:100%;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:1;pointer-events:none}:host .picker-before{top:0;height:83px}:host .picker-before{inset-inline-start:0}:host .picker-after{top:116px;height:84px}:host .picker-after{inset-inline-start:0}:host .picker-highlight{border-radius:var(--highlight-border-radius, 8px);left:0;right:0;top:50%;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;position:absolute;width:calc(100% - 16px);height:34px;-webkit-transform:translateY(-50%);transform:translateY(-50%);background:var(--highlight-background);z-index:-1}:host input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host ::slotted(ion-picker-column:first-of-type){text-align:start}:host ::slotted(ion-picker-column:last-of-type){text-align:end}:host ::slotted(ion-picker-column:only-child){text-align:center}:host .picker-before{background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), to(rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8)));background:linear-gradient(to bottom, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 20%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8) 100%)}:host .picker-after{background:-webkit-gradient(linear, left bottom, left top, color-stop(20%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), to(rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8)));background:linear-gradient(to top, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 20%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8) 100%)}:host .picker-highlight{background:var(--highlight-background, var(--ion-color-step-150, var(--ion-background-color-step-150, #eeeeef)))}",v=":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:200px;direction:ltr;z-index:0}:host .picker-before,:host .picker-after{position:absolute;width:100%;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:1;pointer-events:none}:host .picker-before{top:0;height:83px}:host .picker-before{inset-inline-start:0}:host .picker-after{top:116px;height:84px}:host .picker-after{inset-inline-start:0}:host .picker-highlight{border-radius:var(--highlight-border-radius, 8px);left:0;right:0;top:50%;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;position:absolute;width:calc(100% - 16px);height:34px;-webkit-transform:translateY(-50%);transform:translateY(-50%);background:var(--highlight-background);z-index:-1}:host input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host ::slotted(ion-picker-column:first-of-type){text-align:start}:host ::slotted(ion-picker-column:last-of-type){text-align:end}:host ::slotted(ion-picker-column:only-child){text-align:center}:host .picker-before{background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), color-stop(90%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0)));background:linear-gradient(to bottom, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 20%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0) 90%)}:host .picker-after{background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), color-stop(90%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0)));background:linear-gradient(to top, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 30%, rgba(var(--fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0) 90%)}",x=(()=>{let g=class{constructor(i){h(this,i),this.ionInputModeChange=f(this,"ionInputModeChange",7),this.useInputMode=!1,this.isInHighlightBounds=t=>{let{highlightEl:e}=this;if(!e)return!1;let r=e.getBoundingClientRect(),n=t.clientX<r.left||t.clientX>r.right,a=t.clientY<r.top||t.clientY>r.bottom;return!(n||a)},this.onFocusOut=t=>{let{relatedTarget:e}=t;(!e||e.tagName!=="ION-PICKER-COLUMN"&&e!==this.inputEl)&&this.exitInputMode()},this.onFocusIn=t=>{let{target:e}=t;if(e.tagName==="ION-PICKER-COLUMN"&&!this.actionOnClick){let r=e;r.numericInput?this.enterInputMode(r,!1):this.exitInputMode()}},this.onClick=()=>{let{actionOnClick:t}=this;t&&(t(),this.actionOnClick=void 0)},this.onPointerDown=t=>{let{useInputMode:e,inputModeColumn:r,el:n}=this;if(this.isInHighlightBounds(t)){if(e)t.target.tagName==="ION-PICKER-COLUMN"?r&&r===t.target?this.actionOnClick=()=>{this.enterInputMode()}:this.actionOnClick=()=>{this.enterInputMode(t.target)}:this.actionOnClick=()=>{this.exitInputMode()};else{let o=n.querySelectorAll("ion-picker-column.picker-column-numeric-input").length===1?t.target:void 0;this.actionOnClick=()=>{this.enterInputMode(o)}}return}this.actionOnClick=()=>{this.exitInputMode()}},this.enterInputMode=(t,e=!0)=>{let{inputEl:r,el:n}=this;!r||!n.querySelector("ion-picker-column.picker-column-numeric-input")||(this.useInputMode=!0,this.inputModeColumn=t,e?(this.destroyKeypressListener&&(this.destroyKeypressListener(),this.destroyKeypressListener=void 0),r.focus()):(n.addEventListener("keypress",this.onKeyPress),this.destroyKeypressListener=()=>{n.removeEventListener("keypress",this.onKeyPress)}),this.emitInputModeChange())},this.onKeyPress=t=>{let{inputEl:e}=this;if(!e)return;let r=parseInt(t.key,10);Number.isNaN(r)||(e.value+=t.key,this.onInputChange())},this.selectSingleColumn=()=>{let{inputEl:t,inputModeColumn:e,singleColumnSearchTimeout:r}=this;if(!t||!e)return;let n=Array.from(e.querySelectorAll("ion-picker-column-option")).filter(o=>o.disabled!==!0);if(r&&clearTimeout(r),this.singleColumnSearchTimeout=setTimeout(()=>{t.value="",this.singleColumnSearchTimeout=void 0},1e3),t.value.length>=3){let o=t.value.length-2,s=t.value.substring(o);t.value=s,this.selectSingleColumn();return}let a=n.find(({textContent:o})=>o.replace(/^0+(?=[1-9])|0+(?=0$)/,"")===t.value);if(a){e.setValue(a.value);return}if(t.value.length===2){let o=t.value.substring(t.value.length-1);t.value=o,this.selectSingleColumn()}},this.searchColumn=(t,e,r="start")=>{if(!e)return!1;let n=r==="start"?/^0+/:/0$/;e=e.replace(n,"");let a=Array.from(t.querySelectorAll("ion-picker-column-option")).find(o=>o.disabled!==!0&&o.textContent.replace(n,"")===e);return a&&t.setValue(a.value),!!a},this.multiColumnSearch=(t,e,r)=>{if(r.length===0)return;let n=r.split(""),a=n.slice(0,2).join(""),o=this.searchColumn(t,a);if(n.length>2&&o){let s=n.slice(2,4).join("");this.searchColumn(e,s)}else if(!o&&n.length>=1){let s=n[0],c=this.searchColumn(t,s);if(c||(n.shift(),s=n[0],c=this.searchColumn(t,s)),c&&n.length>1){let k=n.slice(1,3).join("");this.searchColumn(e,k)}}},this.selectMultiColumn=()=>{let{inputEl:t,el:e}=this;if(!t)return;let r=Array.from(e.querySelectorAll("ion-picker-column")).filter(s=>s.numericInput),n=r[0],a=r[1],o=t.value;if(o.length>4){let s=t.value.length-4,c=t.value.substring(s);t.value=c,o=c}this.multiColumnSearch(n,a,o)},this.onInputChange=()=>{let{useInputMode:t,inputEl:e,inputModeColumn:r}=this;!t||!e||(r?this.selectSingleColumn():this.selectMultiColumn())},this.emitInputModeChange=()=>{let{useInputMode:t,inputModeColumn:e}=this;this.ionInputModeChange.emit({useInputMode:t,inputModeColumn:e})}}preventTouchStartPropagation(i){i.stopPropagation()}componentWillLoad(){u(this.el).addEventListener("focusin",this.onFocusIn),u(this.el).addEventListener("focusout",this.onFocusOut)}exitInputMode(){return d(this,null,function*(){let{inputEl:i,useInputMode:t}=this;!t||!i||(this.useInputMode=!1,this.inputModeColumn=void 0,i.blur(),i.value="",this.destroyKeypressListener&&(this.destroyKeypressListener(),this.destroyKeypressListener=void 0),this.emitInputModeChange())})}render(){return l(b,{key:"28f81e4ed44a633178561757c5199c2c98f94b74",onPointerDown:i=>this.onPointerDown(i),onClick:()=>this.onClick()},l("input",{key:"abb3d1ad25ef63856af7804111175a4d50008bc0","aria-hidden":"true",tabindex:-1,inputmode:"numeric",type:"number",onKeyDown:i=>{var t;i.key==="Enter"&&((t=this.inputEl)===null||t===void 0||t.blur())},ref:i=>this.inputEl=i,onInput:()=>this.onInputChange(),onBlur:()=>this.exitInputMode()}),l("div",{key:"334a5abdc02e6b127c57177f626d7e4ff5526183",class:"picker-before"}),l("div",{key:"ffd6271931129e88fc7c820e919d684899e420c5",class:"picker-after"}),l("div",{key:"78d1d95fd09e04f154ea59f24a1cece72c47ed7b",class:"picker-highlight",ref:i=>this.highlightEl=i}),l("slot",{key:"0bd5b9f875d3c71f6cbbde2054baeb1b0a2e8cd5"}))}get el(){return p(this)}};return g.style={ios:m,md:v},g})();export{x as ion_picker};
