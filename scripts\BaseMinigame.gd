extends Control
class_name BaseMinigame

# Base class for all minigames
# Handles common functionality like returning to main menu

@onready var game_title: Label = $VBoxContainer/GameTitle
@onready var game_content: Control = $VBoxContainer/GameContent
@onready var back_button: Button = $VBoxContainer/BackButton

var game_name: String = "Minigame"
var _return_to_menu_dialog: ConfirmationDialog

func _ready():
	# Connect back button
	if back_button:
		back_button.pressed.connect(_show_return_to_menu_dialog)
	
	# Connect to the global input handler
	GlobalInput.back_to_menu_requested.connect(_show_return_to_menu_dialog)
	
	# Set game title
	if game_title:
		game_title.text = game_name
	
	# Create and configure the confirmation dialog
	_create_return_to_menu_dialog()
	
	# Call setup function for derived classes
	setup_game()


func _create_return_to_menu_dialog():
	_return_to_menu_dialog = ConfirmationDialog.new()
	add_child(_return_to_menu_dialog)
	
	_return_to_menu_dialog.title = "Return to Main Menu"
	_return_to_menu_dialog.dialog_text = "Are you sure you want to return to the main menu?\nAll current game progress will be lost."
	_return_to_menu_dialog.ok_button_text = "Yes"
	_return_to_menu_dialog.cancel_button_text = "No"
	
	_return_to_menu_dialog.confirmed.connect(_on_return_to_menu_confirmed)
	_return_to_menu_dialog.canceled.connect(_on_return_to_menu_dialog_closed)


func _show_return_to_menu_dialog():
	# Pause the game and show the dialog
	get_tree().paused = true
	_return_to_menu_dialog.popup_centered()


func _on_return_to_menu_confirmed():
	# This will be called when user clicks "Yes"
	get_tree().paused = false # Unpause before changing scenes
	end_game()
	GameSettings.change_scene(GameSettings.MAIN_MENU_SCENE)


func _on_return_to_menu_dialog_closed():
	# This handles both "No" and closing the dialog window
	get_tree().paused = false

# Override this in derived classes
func setup_game():
	pass

# Override this in derived classes to handle game-specific logic
func start_game():
	print("Starting ", game_name)

# Override this in derived classes to handle game cleanup
func end_game():
	print("Ending ", game_name)

func _on_back_pressed():
	_show_return_to_menu_dialog()
