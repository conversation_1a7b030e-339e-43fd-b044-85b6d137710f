import{f as ie,g as ne}from"./chunk-XC7QA3KA.js";import{a as J,g as Q,h as Z,j as B,k as ee,l as M,o as te,t as oe}from"./chunk-WDNHH3UA.js";import{a as K}from"./chunk-VGNGVKU7.js";import{a as G,b as U,c as H}from"./chunk-WKAFHTGB.js";import{c as re}from"./chunk-55XXYEDJ.js";import{a as m}from"./chunk-PZCS62C4.js";import{c as V,f as j,h as C,i as Y}from"./chunk-OAHYJD5P.js";import"./chunk-WI5MSH4N.js";import"./chunk-E2SD7K6D.js";import"./chunk-CKP3SGE2.js";import{b as se,e as ae,f as W,i as pe,m as L,n as ce,o as de,p as D}from"./chunk-7IRWVP2E.js";import{f as E}from"./chunk-S5EYVFES.js";var De=t=>{if(!t)return{arrowWidth:0,arrowHeight:0};let{width:e,height:o}=t.getBoundingClientRect();return{arrowWidth:e,arrowHeight:o}},fe=(t,e,o)=>{let r=e.getBoundingClientRect(),i=r.height,n=r.width;return t==="cover"&&o&&(n=o.getBoundingClientRect().width),{contentWidth:n,contentHeight:i}},Ae=(t,e,o,r)=>{let i=[],a=C(r).querySelector(".popover-content");switch(e){case"hover":i=[{eventName:"mouseenter",callback:s=>{document.elementFromPoint(s.clientX,s.clientY)!==t&&o.dismiss(void 0,void 0,!1)}}];break;case"context-menu":case"click":default:i=[{eventName:"click",callback:s=>{if(s.target.closest("[data-ion-popover-trigger]")===t){s.stopPropagation();return}o.dismiss(void 0,void 0,!1)}}];break}return i.forEach(({eventName:s,callback:p})=>a.addEventListener(s,p)),()=>{i.forEach(({eventName:s,callback:p})=>a.removeEventListener(s,p))}},Ie=(t,e,o)=>{let r=[];switch(e){case"hover":let i;r=[{eventName:"mouseenter",callback:n=>E(null,null,function*(){n.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{Y(()=>{o.presentFromTrigger(n),i=void 0})},100)})},{eventName:"mouseleave",callback:n=>{i&&clearTimeout(i);let a=n.relatedTarget;a&&a.closest("ion-popover")!==o&&o.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:n=>n.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:n=>o.presentFromTrigger(n,!0)}];break;case"context-menu":r=[{eventName:"contextmenu",callback:n=>{n.preventDefault(),o.presentFromTrigger(n)}},{eventName:"click",callback:n=>n.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:n=>o.presentFromTrigger(n,!0)}];break;case"click":default:r=[{eventName:"click",callback:n=>o.presentFromTrigger(n)},{eventName:"ionPopoverActivateTrigger",callback:n=>o.presentFromTrigger(n,!0)}];break}return r.forEach(({eventName:i,callback:n})=>t.addEventListener(i,n)),t.setAttribute("data-ion-popover-trigger","true"),()=>{r.forEach(({eventName:i,callback:n})=>t.removeEventListener(i,n)),t.removeAttribute("data-ion-popover-trigger")}},he=(t,e)=>!e||e.tagName!=="ION-ITEM"?-1:t.findIndex(o=>o===e),Te=(t,e)=>{let o=he(t,e);return t[o+1]},Se=(t,e)=>{let o=he(t,e);return t[o-1]},z=t=>{let o=C(t).querySelector("button");o&&Y(()=>o.focus())},Ee=t=>t.hasAttribute("data-ion-popover-trigger"),Ce=t=>{let e=o=>E(null,null,function*(){var r;let i=document.activeElement,n=[],a=(r=o.target)===null||r===void 0?void 0:r.tagName;if(!(a!=="ION-POPOVER"&&a!=="ION-ITEM")){try{n=Array.from(t.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(o.key){case"ArrowLeft":(yield t.getParentPopover())&&t.dismiss(void 0,void 0,!1);break;case"ArrowDown":o.preventDefault();let p=Te(n,i);p!==void 0&&z(p);break;case"ArrowUp":o.preventDefault();let d=Se(n,i);d!==void 0&&z(d);break;case"Home":o.preventDefault();let u=n[0];u!==void 0&&z(u);break;case"End":o.preventDefault();let l=n[n.length-1];l!==void 0&&z(l);break;case"ArrowRight":case" ":case"Enter":if(i&&Ee(i)){let h=new CustomEvent("ionPopoverActivateTrigger");i.dispatchEvent(h)}break}}});return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)},ve=(t,e,o,r,i,n,a,s,p,d,u)=>{var l;let h={top:0,left:0,width:0,height:0};switch(n){case"event":if(!u)return p;let A=u;h={top:A.clientY,left:A.clientX,width:1,height:1};break;case"trigger":default:let c=u,I=d||((l=c?.detail)===null||l===void 0?void 0:l.ionShadowTarget)||c?.target;if(!I)return p;let x=I.getBoundingClientRect();h={top:x.top,left:x.left,width:x.width,height:x.height};break}let v=$e(a,h,e,o,r,i,t),g=Ne(s,a,h,e,o),b=v.top+g.top,y=v.left+g.left,{arrowTop:f,arrowLeft:k}=Oe(a,r,i,b,y,e,o,t),{originX:w,originY:P}=Le(a,s,t);return{top:b,left:y,referenceCoordinates:h,arrowTop:f,arrowLeft:k,originX:w,originY:P}},Le=(t,e,o)=>{switch(t){case"top":return{originX:le(e),originY:"bottom"};case"bottom":return{originX:le(e),originY:"top"};case"left":return{originX:"right",originY:_(e)};case"right":return{originX:"left",originY:_(e)};case"start":return{originX:o?"left":"right",originY:_(e)};case"end":return{originX:o?"right":"left",originY:_(e)}}},le=t=>{switch(t){case"start":return"left";case"center":return"center";case"end":return"right"}},_=t=>{switch(t){case"start":return"top";case"center":return"center";case"end":return"bottom"}},Oe=(t,e,o,r,i,n,a,s)=>{let p={arrowTop:r+a/2-e/2,arrowLeft:i+n-e/2},d={arrowTop:r+a/2-e/2,arrowLeft:i-e*1.5};switch(t){case"top":return{arrowTop:r+a,arrowLeft:i+n/2-e/2};case"bottom":return{arrowTop:r-o,arrowLeft:i+n/2-e/2};case"left":return p;case"right":return d;case"start":return s?d:p;case"end":return s?p:d;default:return{arrowTop:0,arrowLeft:0}}},$e=(t,e,o,r,i,n,a)=>{let s={top:e.top,left:e.left-o-i},p={top:e.top,left:e.left+e.width+i};switch(t){case"top":return{top:e.top-r-n,left:e.left};case"right":return p;case"bottom":return{top:e.top+e.height+n,left:e.left};case"left":return s;case"start":return a?p:s;case"end":return a?s:p}},Ne=(t,e,o,r,i)=>{switch(t){case"center":return Ye(e,o,r,i);case"end":return qe(e,o,r,i);case"start":default:return{top:0,left:0}}},qe=(t,e,o,r)=>{switch(t){case"start":case"end":case"left":case"right":return{top:-(r-e.height),left:0};case"top":case"bottom":default:return{top:0,left:-(o-e.width)}}},Ye=(t,e,o,r)=>{switch(t){case"start":case"end":case"left":case"right":return{top:-(r/2-e.height/2),left:0};case"top":case"bottom":default:return{top:0,left:-(o/2-e.width/2)}}},me=(t,e,o,r,i,n,a,s,p,d,u,l,h=0,v=0,g=0)=>{let b=h,y=v,f=o,k=e,w,P=d,A=u,c=!1,I=!1,x=l?l.top+l.height:n/2-s/2,T=l?l.height:0,O=!1;return f<r+p?(f=r,c=!0,P="left"):a+r+f+p>i&&(I=!0,f=i-a-r,P="right"),x+T+s>n&&(t==="top"||t==="bottom")&&(x-s>0?(k=Math.max(12,x-s-T-(g-1)),b=k+s,A="bottom",O=!0):w=r),{top:k,left:f,bottom:w,originX:P,originY:A,checkSafeAreaLeft:c,checkSafeAreaRight:I,arrowTop:b,arrowLeft:y,addPopoverBottomClass:O}},ze=(t,e=!1,o,r)=>!(!o&&!r||t!=="top"&&t!=="bottom"&&e),_e=5,Xe=(t,e)=>{var o;let{event:r,size:i,trigger:n,reference:a,side:s,align:p}=e,d=t.ownerDocument,u=d.dir==="rtl",l=d.defaultView.innerWidth,h=d.defaultView.innerHeight,v=C(t),g=v.querySelector(".popover-content"),b=v.querySelector(".popover-arrow"),y=n||((o=r?.detail)===null||o===void 0?void 0:o.ionShadowTarget)||r?.target,{contentWidth:f,contentHeight:k}=fe(i,g,y),{arrowWidth:w,arrowHeight:P}=De(b),A={top:h/2-k/2,left:l/2-f/2,originX:u?"right":"left",originY:"top"},c=ve(u,f,k,w,P,a,s,p,A,n,r),I=i==="cover"?0:_e,x=i==="cover"?0:25,{originX:T,originY:O,top:$,left:S,bottom:N,checkSafeAreaLeft:q,checkSafeAreaRight:ue,arrowTop:ge,arrowLeft:be,addPopoverBottomClass:we}=me(s,c.top,c.left,I,l,h,f,k,x,c.originX,c.originY,c.referenceCoordinates,c.arrowTop,c.arrowLeft,P),xe=m(),F=m(),R=m();return F.addElement(v.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),R.addElement(v.querySelector(".popover-arrow")).addElement(v.querySelector(".popover-content")).fromTo("opacity",.01,1),xe.easing("ease").duration(100).beforeAddWrite(()=>{i==="cover"&&t.style.setProperty("--width",`${f}px`),we&&t.classList.add("popover-bottom"),N!==void 0&&g.style.setProperty("bottom",`${N}px`);let ye=" + var(--ion-safe-area-left, 0)",ke=" - var(--ion-safe-area-right, 0)",X=`${S}px`;if(q&&(X=`${S}px${ye}`),ue&&(X=`${S}px${ke}`),g.style.setProperty("top",`calc(${$}px + var(--offset-y, 0))`),g.style.setProperty("left",`calc(${X} + var(--offset-x, 0))`),g.style.setProperty("transform-origin",`${O} ${T}`),b!==null){let Pe=c.top!==$||c.left!==S;ze(s,Pe,r,n)?(b.style.setProperty("top",`calc(${ge}px + var(--offset-y, 0))`),b.style.setProperty("left",`calc(${be}px + var(--offset-x, 0))`)):b.style.setProperty("display","none")}}).addAnimation([F,R])},Me=t=>{let e=C(t),o=e.querySelector(".popover-content"),r=e.querySelector(".popover-arrow"),i=m(),n=m(),a=m();return n.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),a.addElement(e.querySelector(".popover-arrow")).addElement(e.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{t.style.removeProperty("--width"),t.classList.remove("popover-bottom"),o.style.removeProperty("top"),o.style.removeProperty("left"),o.style.removeProperty("bottom"),o.style.removeProperty("transform-origin"),r&&(r.style.removeProperty("top"),r.style.removeProperty("left"),r.style.removeProperty("display"))}).duration(300).addAnimation([n,a])},We=12,Fe=(t,e)=>{var o;let{event:r,size:i,trigger:n,reference:a,side:s,align:p}=e,d=t.ownerDocument,u=d.dir==="rtl",l=d.defaultView.innerWidth,h=d.defaultView.innerHeight,v=C(t),g=v.querySelector(".popover-content"),b=n||((o=r?.detail)===null||o===void 0?void 0:o.ionShadowTarget)||r?.target,{contentWidth:y,contentHeight:f}=fe(i,g,b),k={top:h/2-f/2,left:l/2-y/2,originX:u?"right":"left",originY:"top"},w=ve(u,y,f,0,0,a,s,p,k,n,r),P=i==="cover"?0:We,{originX:A,originY:c,top:I,left:x,bottom:T}=me(s,w.top,w.left,P,l,h,y,f,0,w.originX,w.originY,w.referenceCoordinates),O=m(),$=m(),S=m(),N=m(),q=m();return $.addElement(v.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),S.addElement(v.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),N.addElement(g).beforeStyles({top:`calc(${I}px + var(--offset-y, 0px))`,left:`calc(${x}px + var(--offset-x, 0px))`,"transform-origin":`${c} ${A}`}).beforeAddWrite(()=>{T!==void 0&&g.style.setProperty("bottom",`${T}px`)}).fromTo("transform","scale(0.8)","scale(1)"),q.addElement(v.querySelector(".popover-viewport")).fromTo("opacity",.01,1),O.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{i==="cover"&&t.style.setProperty("--width",`${y}px`),c==="bottom"&&t.classList.add("popover-bottom")}).addAnimation([$,S,N,q])},Re=t=>{let e=C(t),o=e.querySelector(".popover-content"),r=m(),i=m(),n=m();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),n.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),r.easing("ease").afterAddWrite(()=>{t.style.removeProperty("--width"),t.classList.remove("popover-bottom"),o.style.removeProperty("top"),o.style.removeProperty("left"),o.style.removeProperty("bottom"),o.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,n])},Ve=':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',je=":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}",Ke=class{constructor(t){pe(this,t),this.didPresent=D(this,"ionPopoverDidPresent",7),this.willPresent=D(this,"ionPopoverWillPresent",7),this.willDismiss=D(this,"ionPopoverWillDismiss",7),this.didDismiss=D(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=D(this,"didPresent",7),this.willPresentShorthand=D(this,"willPresent",7),this.willDismissShorthand=D(this,"willDismiss",7),this.didDismissShorthand=D(this,"didDismiss",7),this.ionMount=D(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=H(),this.lockController=K(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,te)},this.onLifecycle=e=>{let o=this.usersElement,r=Ge[e.type];if(o&&r){let i=new CustomEvent(r,{bubbles:!1,cancelable:!1,detail:e.detail});o.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{let{trigger:e,triggerAction:o,el:r,destroyTriggerInteraction:i}=this;if(i&&i(),e===void 0)return;let n=this.triggerEl=e!==void 0?document.getElementById(e):null;if(!n){se(`[ion-popover] - A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el);return}this.destroyTriggerInteraction=Ie(n,o,r)},this.configureKeyboardInteraction=()=>{let{destroyKeyboardInteraction:e,el:o}=this;e&&e(),this.destroyKeyboardInteraction=Ce(o)},this.configureDismissInteraction=()=>{let{destroyDismissInteraction:e,parentPopover:o,triggerAction:r,triggerEl:i,el:n}=this;!o||!i||(e&&e(),this.destroyDismissInteraction=Ae(i,r,n,o))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(t,e){t===!0&&e===!1?this.present():t===!1&&e===!0&&this.dismiss()}connectedCallback(){let{configureTriggerInteraction:t,el:e}=this;Q(e),t()}disconnectedCallback(){let{destroyTriggerInteraction:t}=this;t&&t()}componentWillLoad(){var t,e;let{el:o}=this,r=(e=(t=this.htmlAttributes)===null||t===void 0?void 0:t.id)!==null&&e!==void 0?e:Z(o);this.parentPopover=o.closest(`ion-popover:not(#${r})`),this.alignment===void 0&&(this.alignment=W(this)==="ios"?"center":"start")}componentDidLoad(){let{parentPopover:t,isOpen:e}=this;e===!0&&Y(()=>this.present()),t&&j(t,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(t,e=!1){return E(this,null,function*(){this.focusDescendantOnPresent=e,yield this.present(t),this.focusDescendantOnPresent=!1})}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let e=this.el.parentNode,o=this.inline=e!==null&&!this.hasController,r=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:r}}present(t){return E(this,null,function*(){let e=yield this.lockController.lock();if(this.presented){e();return}let{el:o}=this,{inline:r,delegate:i}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield G(i,o,this.component,["popover-viewport"],this.componentProps,r),this.keyboardEvents||this.configureKeyboardInteraction(),this.configureDismissInteraction(),V(o)?yield ne(this.usersElement):this.keepContentsMounted||(yield ie()),yield B(this,"popoverEnter",Xe,Fe,{event:t||this.event,size:this.size,trigger:this.triggerEl,reference:this.reference,side:this.side,align:this.alignment}),this.focusDescendantOnPresent&&J(o),e()})}dismiss(t,e,o=!0){return E(this,null,function*(){let r=yield this.lockController.lock(),{destroyKeyboardInteraction:i,destroyDismissInteraction:n}=this;o&&this.parentPopover&&this.parentPopover.dismiss(t,e,o);let a=yield ee(this,t,e,"popoverLeave",Me,Re,this.event);if(a){i&&(i(),this.destroyKeyboardInteraction=void 0),n&&(n(),this.destroyDismissInteraction=void 0);let{delegate:s}=this.getDelegate();yield U(s,this.usersElement)}return r(),a})}getParentPopover(){return E(this,null,function*(){return this.parentPopover})}onDidDismiss(){return M(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return M(this.el,"ionPopoverWillDismiss")}render(){let t=W(this),{onLifecycle:e,parentPopover:o,dismissOnSelect:r,side:i,arrow:n,htmlAttributes:a,focusTrap:s}=this,p=ae("desktop"),d=n&&!o;return L(ce,Object.assign({key:"1de4862099cfcb5035e78008e6dc7c1371846f9a","aria-modal":"true","no-router":!0,tabindex:"-1"},a,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},re(this.cssClass)),{[t]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":p,[`popover-side-${i}`]:!0,[oe]:s===!1,"popover-nested":!!o}),onIonPopoverDidPresent:e,onIonPopoverWillPresent:e,onIonPopoverWillDismiss:e,onIonPopoverDidDismiss:e,onIonBackdropTap:this.onBackdropTap}),!o&&L("ion-backdrop",{key:"981aa4e0102cb93312ffbd8243cdf2a0cdc60469",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),L("div",{key:"1a28ed55e9d34ef78cf0eb0178643301fd2dd75d",class:"popover-wrapper ion-overlay-wrapper",onClick:r?()=>this.dismiss():void 0},d&&L("div",{key:"1c206ea5eb3c0b5883a3d45c34cd22dd5ffe4b65",class:"popover-arrow",part:"arrow"}),L("div",{key:"5ba561486a328c0c7ab825995fdbfb7a196429a4",class:"popover-content",part:"content"},L("slot",{key:"00fc244ce9dcc2dfc677e6c34b7c8e7a330b2b03"}))))}get el(){return de(this)}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}},Ge={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};Ke.style={ios:Ve,md:je};export{Ke as ion_popover};
