import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { GameService } from '../services/game.service';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
  imports: [CommonModule, FormsModule, IonicModule],
})
export class HomePage implements OnInit, OnDestroy {
  roomCode: string = '';
  playerName: string = '';
  isJoining: boolean = false;
  connectionStatus: boolean = false;
  errorMessage: string | null = null;
  canReconnect: boolean = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private gameService: GameService,
    private router: Router
  ) {}

  ngOnInit() {
    // Load saved player name
    const savedName = localStorage.getItem('jugshine_player_name');
    if (savedName) {
      this.playerName = savedName;
    }

    // Check if can reconnect
    const savedRoomCode = localStorage.getItem('jugshine_room_code');
    this.canReconnect = !!(savedName && savedRoomCode);

    // Subscribe to connection status
    this.subscriptions.push(
      this.gameService.connectionStatus$.subscribe(status => {
        this.connectionStatus = status;
      })
    );

    // Subscribe to errors
    this.subscriptions.push(
      this.gameService.error$.subscribe(error => {
        this.errorMessage = error;
      })
    );

    // Subscribe to game state changes
    this.subscriptions.push(
      this.gameService.game$.subscribe(game => {
        if (game) {
          // Navigate to game page when successfully joined
          this.router.navigate(['/game']);
        }
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  formatRoomCode(event: any) {
    let value = event.target.value.toUpperCase().replace(/[^A-Z]/g, '');
    if (value.length > 4) {
      value = value.substring(0, 4);
    }
    this.roomCode = value;
    event.target.value = value;
  }

  async joinGame() {
    if (!this.roomCode || !this.playerName || this.isJoining) {
      return;
    }

    // Validate inputs
    if (this.roomCode.length !== 4) {
      this.errorMessage = 'Room code must be exactly 4 letters';
      return;
    }

    if (this.playerName.length < 2 || this.playerName.length > 20) {
      this.errorMessage = 'Name must be between 2 and 20 characters';
      return;
    }

    this.isJoining = true;
    this.errorMessage = null;

    try {
      const success = await this.gameService.joinGame(this.roomCode.toUpperCase(), this.playerName.trim());

      if (success) {
        // Navigation will happen automatically via subscription
        console.log('Successfully joined game');
      }
    } catch (error) {
      console.error('Error joining game:', error);
      this.errorMessage = 'Failed to join game. Please try again.';
    } finally {
      this.isJoining = false;
    }
  }

  async reconnectToGame() {
    this.isJoining = true;
    this.errorMessage = null;

    try {
      const success = await this.gameService.reconnectToGame();

      if (success) {
        console.log('Successfully reconnected to game');
        // Navigation will happen automatically via subscription
      } else {
        // Error message will be set by the game service
        this.canReconnect = false;
        // Clear the error after a few seconds to allow manual joining
        setTimeout(() => {
          this.gameService.clearError();
        }, 5000);
      }
    } catch (error) {
      console.error('Error reconnecting:', error);
      this.errorMessage = 'Failed to reconnect. Please join manually.';
      this.canReconnect = false;
    } finally {
      this.isJoining = false;
    }
  }

  clearError() {
    this.errorMessage = null;
  }
}
