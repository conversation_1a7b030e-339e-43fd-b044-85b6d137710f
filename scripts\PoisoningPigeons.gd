extends BaseMinigame

# This script is now a "dumb" client. It holds no game logic.
# It only displays the game state received from the server and sends host actions.

# --- UI References ---
@onready var room_code_label: Label = $VBoxContainer/GameContent/RoomCodeContainer/RoomCodeLabel
@onready var game_status_label: Label = $VBoxContainer/GameContent/StatusContainer/GameStatusLabel
@onready var round_label: Label = $VBoxContainer/GameContent/StatusContainer/RoundLabel
@onready var prompt_label: Label = $VBoxContainer/GameContent/PromptContainer/PromptLabel
@onready var timer_label: Label = $VBoxContainer/GameContent/TimerContainer/TimerLabel
@onready var responses_container: VBoxContainer = $VBoxContainer/GameContent/ResponsesContainer
@onready var winner_container: VBoxContainer = $VBoxContainer/GameContent/WinnerContainer
@onready var winner_label: Label = $VBoxContainer/GameContent/WinnerContainer/WinnerLabel
@onready var scores_container: VBoxContainer = $VBoxContainer/GameContent/ScoresContainer
@onready var start_game_button: Button = $VBoxContainer/GameContent/StartGameButton # Assuming you add this button
@onready var players_list: VBoxContainer = $VBoxContainer/GameContent/PlayersList # Assuming you add this for player names

# --- Game State ---
var _current_game_state: Dictionary = {}
var _room_code: String = ""

# --- Initialization ---
func setup_game():
	game_name = "Poisoning Pigeons"
	print("Setting up Poisoning Pigeons (Dumb Client)")
	
	# Connect to WebSocket signals
	WebSocket.game_updated.connect(_on_game_updated)
	WebSocket.game_closed.connect(_on_game_closed)
	
	# Initial UI state
	room_code_label.text = "Creating game..."
	
	# The leader's client creates the game on the server
	# In a real scenario, you'd check if this client is the host.
	# For now, we assume this client is always the host.
	_create_game_on_server()

func _create_game_on_server():
	if not WebSocket.is_socket_connected():
		room_code_label.text = "Error: Not connected to server"
		return

	var game_settings = {
		"mature_content": GameSettings.nsfw_content_enabled,
		"max_players": 8,
		"rounds_to_win": 5,
		"round_time_limit": 30
	}
	# This uses an HTTP request as creating a game is a one-time setup action
	var http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_create_game_completed)
	
	var headers = ["Content-Type: application/json"]
	var body = JSON.stringify({"game_type": "poisoning_pigeons", "settings": game_settings})
	
	http_request.request(
		GameSettings.server_url + "/api/games",
		headers,
		HTTPClient.METHOD_POST,
		body
	)

func _on_create_game_completed(_result, response_code, _headers, body):
	if response_code == 201:
		var json = JSON.parse_string(body.get_string_from_utf8())
		if json and json.has("room_code"):
			_room_code = json["room_code"]
			room_code_label.text = "Room Code: " + _room_code
			
			# Now that the game exists, join the WebSocket room
			WebSocket.emit("join-game", _room_code)
			
			# Setup start button for the leader
			if start_game_button:
				start_game_button.visible = true
				start_game_button.pressed.connect(_on_start_game_pressed)
		else:
			room_code_label.text = "Error: Invalid response from server"
	else:
		room_code_label.text = "Error creating game on server"
		print("Error creating game, response code: ", response_code)


# --- Server Event Handlers ---

func _on_game_updated(game_state: Dictionary):
	_current_game_state = game_state
	print("Game state updated: ", game_state.get("game_status", "N/A"))
	
	# Update all UI elements based on the new state
	_update_players_list()
	_update_status_labels()
	_update_prompt()
	_update_judging_view()
	_update_winner_view()


func _on_game_closed(_data: Dictionary):
	GameSettings.change_scene(GameSettings.MAIN_MENU_SCENE)


# --- UI Update Functions ---

func _update_players_list():
	# Clear existing player names
	for child in players_list.get_children():
		child.queue_free()
		
	var players = _current_game_state.get("players", [])
	var judge_name = _current_game_state.get("current_judge", "")
	
	for player in players:
		var player_label = Label.new()
		var player_text = player.get("name", "???")
		if player.get("is_leader", false):
			player_text += " (Leader)"
		if player.get("name", "") == judge_name:
			player_text += " (Judge)"
			
		player_label.text = player_text
		players_list.add_child(player_label)

func _update_status_labels():
	game_status_label.text = "Status: " + _current_game_state.get("game_status", "Unknown")
	round_label.text = "Round: " + str(_current_game_state.get("current_round", 0))

func _update_prompt():
	prompt_label.text = _current_game_state.get("game_data", {}).get("current_prompt", "Waiting for game to start...")

func _update_judging_view():
	# Clear previous responses
	for child in responses_container.get_children():
		child.queue_free()

	if _current_game_state.get("current_phase", "") == "judging":
		var submitted = _current_game_state.get("game_data", {}).get("submitted_responses", [])
		for response_data in submitted:
			var response_label = Label.new()
			response_label.text = response_data.get("response", "---")
			response_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
			responses_container.add_child(response_label)

func _update_winner_view():
	if _current_game_state.get("current_phase", "") == "round_end":
		winner_container.visible = true
		var winner_data = _current_game_state.get("game_data", {}).get("current_winner", {})
		if winner_data:
			var winner_text = "🏆 WINNER 🏆\n"
			winner_text += winner_data.get("player_name", "Unknown") + "\n"
			winner_text += "\"" + winner_data.get("response", "") + "\""
			winner_label.text = winner_text
		else:
			winner_label.text = "No winner was chosen for this round."
		
		_update_scores_display()
	else:
		winner_container.visible = false

func _update_scores_display():
	# Clear existing scores
	for child in scores_container.get_children():
		child.queue_free()
	
	var players = _current_game_state.get("players", [])
	for player in players:
		var score_label = Label.new()
		score_label.text = player.get("name", "???") + ": " + str(player.get("score", 0))
		scores_container.add_child(score_label)


# --- Host Actions ---

func _on_start_game_pressed():
	# The leader (host) clicks the start button. We don't have a player name on the
	# host client, so we send a placeholder. The server knows this is a host action.
	var host_action_payload = {
		"action_type": "start_game",
		"data": {}
	}
	# We need to make an http request for this action.
	var http_request = HTTPRequest.new()
	add_child(http_request)
	# Note: A response isn't strictly necessary to handle here, as the `game-updated`
	# WebSocket event will be the source of truth.
	
	var headers = ["Content-Type: application/json"]
	var body = JSON.stringify(host_action_payload)
	
	# The action route requires a player name, we'll use "host" as a placeholder
	var url = "%s/api/players/%s/host/actions" % [GameSettings.server_url, _room_code]

	http_request.request(url, headers, HTTPClient.METHOD_POST, body)
	
	start_game_button.visible = false # Hide after starting


# --- Cleanup ---

func end_game():
	super.end_game()
	# Disconnect signals to prevent memory leaks
	if WebSocket.is_connected("game_updated", _on_game_updated):
		WebSocket.game_updated.disconnect(_on_game_updated)
	if WebSocket.is_connected("game_closed", _on_game_closed):
		WebSocket.game_closed.disconnect(_on_game_closed)
	
	# Tell server this host is closing the game (optional, depends on desired flow)
	if _room_code != "":
		# WebSocket.emit("host_action", {"action_type": "end_game", "player_name": ... })
		pass
