@echo off
title Jugshine Server & MongoDB Manager
color 0A

echo.
echo ========================================
echo    JUGSHINE SERVER & MONGODB MANAGER
echo ========================================
echo.

:: Set error handling
setlocal enabledelayedexpansion

:: Check current directory
echo [INFO] Current directory: %CD%
echo.

:: Check if we're in the right directory
if not exist "server" (
    echo [ERROR] 'server' directory not found!
    echo Make sure you're running this from the Jugshine root directory.
    echo.
    pause
    exit /b 1
)

echo [INFO] Directory structure looks good!
echo.

:: Check for MongoDB installation in multiple locations
echo [INFO] Checking MongoDB installation...
set MONGOD_PATH=
set MONGO_FOUND=0

:: Check if mongod is in PATH
where mongod >nul 2>&1
if not errorlevel 1 (
    set MONGO_FOUND=1
    set MONGOD_PATH=mongod
    echo [INFO] MongoDB found in PATH
    goto :mongo_found
)

:: Check common installation directories
echo [INFO] MongoDB not in PATH, checking common installation locations...

:: Check Program Files\MongoDB (various versions)
for /d %%d in ("C:\Program Files\MongoDB\Server\*") do (
    if exist "%%d\bin\mongod.exe" (
        set MONGO_FOUND=1
        set MONGOD_PATH="%%d\bin\mongod.exe"
        echo [INFO] MongoDB found at: %%d\bin\mongod.exe
        goto :mongo_found
    )
)

:: Check Program Files (x86)\MongoDB
for /d %%d in ("C:\Program Files (x86)\MongoDB\Server\*") do (
    if exist "%%d\bin\mongod.exe" (
        set MONGO_FOUND=1
        set MONGOD_PATH="%%d\bin\mongod.exe"
        echo [INFO] MongoDB found at: %%d\bin\mongod.exe
        goto :mongo_found
    )
)

:: Check if MongoDB service exists
sc query MongoDB >nul 2>&1
if not errorlevel 1 (
    echo [INFO] MongoDB Windows service found
    echo [INFO] Attempting to start MongoDB service...
    net start MongoDB >nul 2>&1
    if not errorlevel 1 (
        echo [INFO] MongoDB service started successfully
        set MONGO_FOUND=1
        set MONGOD_PATH=service
        goto :mongo_found
    )
)

:: MongoDB not found
if %MONGO_FOUND%==0 (
    echo [ERROR] MongoDB not found!
    echo.
    echo Quick fix options:
    echo   1. Run: install-mongodb.bat (automatic installation)
    echo   2. Run: fix-mongodb-path.bat (if MongoDB is installed but not in PATH)
    echo.
    echo Manual installation:
    echo   3. Download MongoDB Community Server:
    echo      https://www.mongodb.com/try/download/community
    echo   4. Install via Chocolatey: choco install mongodb
    echo   5. Install via Scoop: scoop install mongodb
    echo.
    echo [INFO] Would you like to run the automatic installer now? (Y/N)
    set /p INSTALL_CHOICE=
    if /i "%INSTALL_CHOICE%"=="Y" (
        echo [INFO] Running MongoDB installer...
        call install-mongodb.bat
        echo [INFO] Please run this script again after MongoDB installation.
    )
    echo.
    pause
    exit /b 1
)

:mongo_found

:: Get MongoDB version
for /f "tokens=*" %%i in ('mongod --version 2^>nul ^| findstr "db version"') do set MONGO_VERSION=%%i
echo [INFO] MongoDB found: %MONGO_VERSION%
echo.

:: Check for Node.js
echo [INFO] Checking Node.js...
where node >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found in PATH
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: Get Node.js version
for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
echo [INFO] Node.js version: %NODE_VERSION%
echo.

:: Create MongoDB data directory if it doesn't exist
if not exist "mongodb-data" (
    echo [INFO] Creating MongoDB data directory...
    mkdir mongodb-data
    echo [INFO] Created mongodb-data directory
)
echo.

:: Kill existing MongoDB and Node processes
echo [INFO] Stopping existing MongoDB and Node.js processes...
taskkill /f /im mongod.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
echo [INFO] Existing processes stopped
echo.

:: Wait a moment for processes to fully terminate
timeout /t 2 /nobreak >nul

:: Start MongoDB
echo [INFO] Starting MongoDB...
if "%MONGOD_PATH%"=="service" (
    echo [INFO] Using MongoDB Windows service
    net start MongoDB >nul 2>&1
    echo [INFO] MongoDB service is running
) else (
    echo [INFO] Starting MongoDB with custom data directory
    start "MongoDB Server" cmd /k "%MONGOD_PATH% --dbpath mongodb-data --port 27017 && echo MongoDB started successfully && pause"
    echo [INFO] MongoDB starting in new window...
)

:: Wait for MongoDB to start
echo [INFO] Waiting for MongoDB to start...
timeout /t 5 /nobreak >nul

:: Test MongoDB connection
echo [INFO] Testing MongoDB connection...
node -e "const mongoose = require('./server/node_modules/mongoose'); mongoose.connect('mongodb://localhost:27017/jugshine', {serverSelectionTimeoutMS: 5000}).then(() => {console.log('MongoDB connection successful'); process.exit(0);}).catch(err => {console.error('MongoDB connection failed:', err.message); process.exit(1);})" 2>nul
if errorlevel 1 (
    echo [WARNING] MongoDB connection test failed
    echo [INFO] MongoDB might still be starting up...
    echo [INFO] The server will attempt to connect when it starts
) else (
    echo [INFO] MongoDB connection test successful
)
echo.

:: Install server dependencies if needed
echo [INFO] Checking server dependencies...
if not exist "server\node_modules" (
    echo [INFO] Installing server dependencies...
    cd server
    call npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install server dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [INFO] Server dependencies installed
) else (
    echo [INFO] Server dependencies already installed
)
echo.

:: Seed database
echo [INFO] Seeding database with game content...
cd server
call node scripts/seedContent.js
if errorlevel 1 (
    echo [WARNING] Database seeding failed
    echo [INFO] The server will still work, but game content might be missing
) else (
    echo [INFO] Database seeded successfully
)
cd ..
echo.

:: Start the server
echo [INFO] Starting Jugshine server...
start "Jugshine Server" cmd /k "cd /d %CD%\server && npm run dev"
echo [INFO] Server starting in new window...

:: Wait a bit
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo    SERVICES STARTED!
echo ========================================
echo.
echo [SUCCESS] Jugshine server and MongoDB are starting up!
echo.
echo Services:
echo   MongoDB:    mongodb://localhost:27017/jugshine
echo   Server:     http://localhost:3000
echo   Health:     http://localhost:3000/health
echo.
echo MongoDB Data: %CD%\mongodb-data
echo.
echo Next steps:
echo   1. Wait for both services to start (check the new windows)
echo   2. Open Godot and run the Jugshine project
echo   3. Start a game and note the room code
echo   4. Open the web client at http://localhost:8100
echo   5. Enter room code and play!
echo.
echo [INFO] Press any key to exit this window.
pause >nul
