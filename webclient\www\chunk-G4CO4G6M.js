import{b as l,f as c,i as p,m as a,n as h,o as m,p as f}from"./chunk-7IRWVP2E.js";import{f as o}from"./chunk-S5EYVFES.js";var u=":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--side-min-width:270px;--side-max-width:28%}",v=":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--side-min-width:270px;--side-max-width:28%}",b="split-pane-main",w="split-pane-side",x={xs:"(min-width: 0px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)",never:""},g=class{constructor(e){p(this,e),this.ionSplitPaneVisible=f(this,"ionSplitPaneVisible",7),this.visible=!1,this.disabled=!1,this.when=x.lg}visibleChanged(e){this.ionSplitPaneVisible.emit({visible:e})}isVisible(){return o(this,null,function*(){return Promise.resolve(this.visible)})}connectedCallback(){return o(this,null,function*(){typeof customElements<"u"&&customElements!=null&&(yield customElements.whenDefined("ion-split-pane")),this.styleMainElement(),this.updateState()})}disconnectedCallback(){this.rmL&&(this.rmL(),this.rmL=void 0)}updateState(){if(this.rmL&&(this.rmL(),this.rmL=void 0),this.disabled){this.visible=!1;return}let e=this.when;if(typeof e=="boolean"){this.visible=e;return}let n=x[e]||e;if(n.length===0){this.visible=!1;return}let t=s=>{this.visible=s.matches},i=window.matchMedia(n);i.addListener(t),this.rmL=()=>i.removeListener(t),this.visible=i.matches}styleMainElement(){let e=this.contentId,n=this.el.children,t=this.el.childElementCount,i=!1;for(let s=0;s<t;s++){let d=n[s],r=e!==void 0&&d.id===e;if(r)if(i){l("[ion-split-pane] - Cannot have more than one main node.");return}else y(d,r),i=!0}i||l("[ion-split-pane] - Does not have a specified main node.")}render(){let e=c(this);return a(h,{key:"d5e30df12f1f1f855da4c66f98076b9dce762c59",class:{[e]:!0,[`split-pane-${e}`]:!0,"split-pane-visible":this.visible}},a("slot",{key:"3e30d7cf3bc1cf434e16876a0cb2a36377b8e00f"}))}get el(){return m(this)}static get watchers(){return{visible:["visibleChanged"],disabled:["updateState"],when:["updateState"]}}},y=(e,n)=>{let t,i;n?(t=b,i=w):(t=w,i=b);let s=e.classList;s.add(t),s.remove(i)};g.style={ios:u,md:v};export{g as ion_split_pane};
