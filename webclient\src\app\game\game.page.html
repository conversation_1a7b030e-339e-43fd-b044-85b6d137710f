<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>
      {{ getGameTitle() }}
    </ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="leaveGame()" fill="clear">
        <ion-icon name="exit-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <!-- Loading State -->
  <div *ngIf="!game" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading game...</p>
  </div>

  <!-- Game Content -->
  <div *ngIf="game" class="game-container">
    <!-- Game Header -->
    <div class="game-header">
      <div class="game-info">
        <h2>{{ getGameTitle() }}</h2>
        <div class="game-stats">
          <ion-chip [color]="getStatusColor()">
            <ion-label>{{ game.game_status }}</ion-label>
          </ion-chip>
          <ion-chip color="primary">
            <ion-label>Room: {{ game.room_code }}</ion-label>
          </ion-chip>
          <ion-chip color="secondary">
            <ion-label>Round {{ game.current_round }}</ion-label>
          </ion-chip>
        </div>
      </div>
    </div>

    <!-- Player Info -->
    <ion-card *ngIf="player" class="player-info">
      <ion-card-content>
        <div class="player-details">
          <h3>{{ player.name }}</h3>
          <div class="player-badges">
            <ion-chip *ngIf="player.is_leader" color="warning">
              <ion-icon name="star"></ion-icon>
              <ion-label>Leader</ion-label>
            </ion-chip>
            <ion-chip *ngIf="isPlayerJudge()" color="secondary">
              <ion-icon name="gavel"></ion-icon>
              <ion-label>Judge</ion-label>
            </ion-chip>
            <ion-chip color="success">
              <ion-label>{{ player.score }} points</ion-label>
            </ion-chip>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Players List -->
    <ion-card class="players-list">
      <ion-card-header>
        <ion-card-title>Players ({{ game.players.length }}/{{ game.settings.max_players }})</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item *ngFor="let gamePlayer of game.players">
            <div class="player-avatar" slot="start">
              {{ gamePlayer.name.charAt(0).toUpperCase() }}
            </div>
            <ion-label>
              <h3>{{ gamePlayer.name }}</h3>
              <p>{{ gamePlayer.score }} points</p>
            </ion-label>
            <div class="player-badges-list" slot="end">
              <ion-chip *ngIf="gamePlayer.is_leader" color="warning" size="small">
                <ion-icon name="star"></ion-icon>
                <ion-label>Leader</ion-label>
              </ion-chip>
              <ion-chip *ngIf="game.current_judge === gamePlayer.name" color="secondary" size="small">
                <ion-icon name="gavel"></ion-icon>
                <ion-label>Judge</ion-label>
              </ion-chip>
            </div>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Leader Controls -->
    <ion-card *ngIf="player?.is_leader && game.game_status === 'Waiting for Players'" class="leader-controls">
      <ion-card-header>
        <ion-card-title>Game Controls</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-button 
          expand="block" 
          (click)="startGame()"
          [disabled]="game.players.length < 2"
          color="success">
          <ion-icon name="play" slot="start"></ion-icon>
          Start Game
        </ion-button>
        <p *ngIf="game.players.length < 2" class="min-players-warning">
          Need at least 2 players to start
        </p>
      </ion-card-content>
    </ion-card>

    <!-- Game-Specific Components -->
    <div class="game-component">
      <!-- Poisoning Pigeons -->
      <app-poisoning-pigeons 
        *ngIf="game.game_type === 'poisoning_pigeons'"
        [game]="game"
        [player]="player"
        (actionSubmitted)="onActionSubmitted($event)">
      </app-poisoning-pigeons>

      <!-- Other Games (Placeholder) -->
      <app-game-placeholder
        *ngIf="game.game_type !== 'poisoning_pigeons'"
        [game]="game"
        [player]="player"
        [gameTitle]="getGameTitle()"
        (actionSubmitted)="onActionSubmitted($event)">
      </app-game-placeholder>
    </div>
  </div>

  <!-- Error Alert -->
  <ion-alert
    [isOpen]="!!errorMessage"
    header="Error"
    [message]="errorMessage"
    [buttons]="['OK']"
    (didDismiss)="clearError()">
  </ion-alert>

  <!-- Leave Game Confirmation -->
  <ion-alert
    [isOpen]="showLeaveConfirmation"
    header="Leave Game"
    message="Are you sure you want to leave this game?"
    [buttons]="leaveGameButtons"
    (didDismiss)="showLeaveConfirmation = false">
  </ion-alert>
</ion-content>
