const express = require('express');
const { param, body, validationResult } = require('express-validator');
const Game = require('../models/Game');
const gameEngine = require('../services/gameEngine');

const router = express.Router();

// Middleware for validation
const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

// Middleware to find game and player
const findGameAndPlayer = async (req, res, next) => {
    try {
        req.game = await Game.findByRoomCode(req.params.roomCode);
        if (!req.game) {
            return res.status(404).json({ error: 'Game not found' });
        }
        
        req.player = req.game.getPlayer(req.params.playerName);
        if (!req.player) {
            return res.status(404).json({ error: 'Player not found' });
        }

        next();
    } catch (error) {
        console.error('Error finding game or player:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Route to handle various game actions
router.post('/:roomCode/:playerName', [
    param('roomCode').isLength({ min: 4, max: 4 }).isAlpha().toUpperCase(),
    param('playerName').isLength({ min: 2, max: 20 }).trim(),
    body('action_type').isString().notEmpty(),
    validateRequest,
    findGameAndPlayer
], async (req, res) => {
    const { action_type, data } = req.body;
    const { game, player } = req;
    const io = req.app.get('io');

    try {
        let updatedGame;
        switch (action_type) {
            case 'start_game':
                if (!player.is_leader) return res.status(403).json({ error: 'Only the leader can start the game' });
                updatedGame = await gameEngine.startGame(game, io);
                break;

            case 'submit_response':
                updatedGame = await gameEngine.submitPlayerResponse(game, player.name, data, io);
                break;
            
            case 'judge_winner':
                if (game.current_judge !== player.name) return res.status(403).json({ error: 'Only the judge can select a winner' });
                updatedGame = await gameEngine.judgeWinner(game, data, io);
                break;

            case 'play_again':
                if (!player.is_leader) return res.status(403).json({ error: 'Only the leader can start a new game' });
                updatedGame = await gameEngine.resetForNewGame(game, io);
                break;
            
            case 'end_game':
                if (!player.is_leader) return res.status(403).json({ error: 'Only the leader can end the game' });
                updatedGame = await gameEngine.closeGame(game, io);
                break;

            default:
                return res.status(400).json({ error: 'Invalid action type' });
        }
        
        res.json({ game: updatedGame });

    } catch (error) {
        console.error(`Error processing action '${action_type}':`, error);
        res.status(500).json({ error: `Failed to process action: ${error.message}` });
    }
});

module.exports = router;