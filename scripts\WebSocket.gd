extends SocketIO

## Manages the WebSocket connection to the server.
## This will be an autoloaded singleton that IS a SocketIO object.

# Emitted when the connection status changes
signal connection_status_changed(is_connected: bool)

# Emitted when the game state is updated by the server
signal game_updated(game_state: Dictionary)

# Emitted when the game is closed by the server
signal game_closed(data: Dictionary)

# Emitted when a specific error is received from the server
signal server_error(message: String)


var _is_connected := false

func _ready():
	# Configure this node itself, since it is the SocketIO node.
	self.base_url = "http://localhost:3000"
	
	# Connect to the signals that this node provides
	self.connect("connection_succeed", Callable(self, "_on_connection_succeed"))
	self.connect("disconnected", Callable(self, "_on_disconnected"))
	self.connect("error_occured", Callable(self, "_on_error_occured"))
	self.connect("event_received", Callable(self, "_on_event_received"))


func connect_to_server():
	if _is_connected:
		print("Already connected to server.")
		return
	
	print("Attempting to connect to WebSocket server at ", self.base_url)
	self.connect_socket()


func disconnect_from_server():
	if _is_connected:
		self.disconnect_socket()


func get_socket_id() -> String:
	return self.sid


func is_socket_connected() -> bool:
	return _is_connected


# --- Signal Override Handlers ---

func _on_connection_succeed():
	print("Successfully connected to server! Socket ID: ", self.sid)
	_is_connected = true
	connection_status_changed.emit(true)


func _on_disconnected():
	print("Disconnected from server.")
	_is_connected = false
	connection_status_changed.emit(false)


func _on_error_occured(error_data: Variant):
	var error_string = ""
	if typeof(error_data) == TYPE_DICTIONARY:
		error_string = error_data.get("message", "Unknown dictionary error")
	elif typeof(error_data) == TYPE_STRING:
		error_string = error_data
	else:
		error_string = "An unknown, non-standard error occurred."
	
	print("WebSocket Error! ", error_string)
	server_error.emit(error_string)


func _on_event_received(event_name: String, data: Variant):
	print("Received custom event: '", event_name, "' with data: ", data)
	
	match event_name:
		"game-updated":
			if typeof(data) == TYPE_DICTIONARY:
				game_updated.emit(data)
		"game-closed":
			if typeof(data) == TYPE_DICTIONARY:
				game_closed.emit(data)
		"server-error":
			if typeof(data) == TYPE_DICTIONARY:
				var message = data.get("message", "An unknown server error occurred.")
				server_error.emit(message)
			elif typeof(data) == TYPE_STRING:
				server_error.emit(data)
		_:
			print("Received unknown event type: ", event_name)
