extends SocketIO

## Manages the WebSocket connection to the server.
## This will be an autoloaded singleton that IS a SocketIO object.

# Emitted when the connection status changes
signal connection_status_changed(is_connected: bool)

# Emitted when the game state is updated by the server
signal game_updated(game_state: Dictionary)

# Emitted when the game is closed by the server
signal game_closed(data: Dictionary)

# Emitted when a specific error is received from the server
signal server_error(message: String)


var _is_connected := false

func _ready():
	# Configure this node itself, since it is the SocketIO node.
	self.base_url = "http://localhost:3000"

	# Connect to the signals that this node provides
	self.socket_connected.connect(_on_socket_connected)
	self.socket_disconnected.connect(_on_socket_disconnected)
	self.namespace_connection_error.connect(_on_namespace_connection_error)
	self.event_received.connect(_on_event_received)


func connect_to_server():
	if _is_connected:
		print("Already connected to server.")
		return
	
	print("Attempting to connect to WebSocket server at ", self.base_url)
	self.connect_socket()


func disconnect_from_server():
	if _is_connected:
		self.disconnect_socket()


func get_socket_id() -> String:
	if _namespaces.has(default_namespace):
		return _namespaces[default_namespace].sid
	return ""


func is_socket_connected() -> bool:
	return _is_connected


func send_event(event_name: String, data: Variant = null):
	"""Send an event to the server through Socket.IO"""
	if not _is_connected:
		print("Cannot send event - not connected to server")
		return

	self.emit(event_name, data)


# --- Signal Override Handlers ---

func _on_socket_connected(_ns: String):
	print("Successfully connected to server! Socket ID: ", get_socket_id())
	_is_connected = true
	connection_status_changed.emit(true)


func _on_socket_disconnected():
	print("Disconnected from server.")
	_is_connected = false
	connection_status_changed.emit(false)


func _on_namespace_connection_error(_namespace: String, data: Variant):
	var error_msg = ""
	if typeof(data) == TYPE_DICTIONARY:
		error_msg = data.get("message", "Unknown dictionary error")
	elif typeof(data) == TYPE_STRING:
		error_msg = data
	else:
		error_msg = "An unknown, non-standard error occurred."

	print("WebSocket Error! ", error_msg)
	server_error.emit(error_msg)


func _on_event_received(event_name: String, data: Variant, _ns: String):
	print("Received custom event: '", event_name, "' with data: ", data)

	# Handle the case where data is an array (which is common in Socket.IO)
	var event_data = data
	if typeof(data) == TYPE_ARRAY and data.size() > 0:
		event_data = data[0]

	match event_name:
		"game-updated":
			if typeof(event_data) == TYPE_DICTIONARY:
				game_updated.emit(event_data)
		"game-closed":
			if typeof(event_data) == TYPE_DICTIONARY:
				game_closed.emit(event_data)
		"server-error":
			if typeof(event_data) == TYPE_DICTIONARY:
				var message = event_data.get("message", "An unknown server error occurred.")
				server_error.emit(message)
			elif typeof(event_data) == TYPE_STRING:
				server_error.emit(event_data)
		_:
			print("Received unknown event type: ", event_name)
