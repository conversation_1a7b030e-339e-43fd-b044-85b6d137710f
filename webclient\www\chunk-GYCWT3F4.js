import{b as We}from"./chunk-XOTTEB5B.js";import{e as He,f as Ze}from"./chunk-WDNHH3UA.js";import{g as gi,r as vi}from"./chunk-7IRWVP2E.js";import{$ as L,$a as si,A as d,B as Un,C as ze,D as N,E as ye,F as Ie,H as Xn,I as $n,J as Pe,Ja as te,K as Kn,Ka as Le,L as q,M as ee,N as u,O as l,P as be,Q as Fe,Qa as ti,R as H,Ra as ni,S as Z,Va as ii,Wa as oi,Xa as Ve,Ya as W,_a as ri,a as Q,ab as ai,b as h,ba as p,bb as ci,c as qn,ca as Yn,cb as li,d as Ae,db as di,e as me,ea as Ce,eb as ui,f as Hn,fa as Ne,fb as pi,g as F,ga as Qn,gb as fi,hb as hi,i as J,ia as Jn,ib as ne,j as Me,jb as mi,k as Be,ka as ei,l as E,lb as qe,m as Zn,o as B,p as Oe,q as a,r as ge,s as r,t as Wn,u as c,v as Gn,w as k,x as D,y as ve}from"./chunk-FXSFTU3P.js";import{a as fe,b as he,e as eo,f as P}from"./chunk-S5EYVFES.js";var Ci=(n,o)=>P(null,null,function*(){if(!(typeof window>"u"))return yield gi(),vi(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16,"router-animation"]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16,"html-attributes"],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16,"counter-formatter"],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16,"root-params"],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16,"component-props"],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16,"counter-formatter"],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16,"component-props"],"beforeLeave":[16,"before-leave"],"beforeEnter":[16,"before-enter"]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-input-otp",[[38,"ion-input-otp",{"autocapitalize":[1],"color":[513],"disabled":[516],"fill":[1],"inputmode":[1],"length":[2],"pattern":[1],"readonly":[516],"separators":[1],"shape":[1],"size":[1],"type":[1],"value":[1032],"inputValues":[32],"hasFocus":[32],"setFocus":[64]},null,{"value":["valueChanged"],"separators":["processSeparators"],"length":["processSeparators"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16,"pin-formatter"],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16,"format-options"],"readonly":[4],"isDateEnabled":[16,"is-date-enabled"],"showAdjacentDays":[4,"show-adjacent-days"],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16,"title-selected-dates-formatter"],"multiple":[4],"highlightedDates":[16,"highlighted-dates"],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16,"component-props"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16,"presenting-element"],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"component":[1],"componentProps":[16,"component-props"],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16,"router-animation"],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16,"swipe-handler"],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),o)});(function(){if(typeof window<"u"&&window.Reflect!==void 0&&window.customElements!==void 0){var n=HTMLElement;window.HTMLElement=function(){return Reflect.construct(n,[],this.constructor)},HTMLElement.prototype=n.prototype,HTMLElement.prototype.constructor=HTMLElement,Object.setPrototypeOf(HTMLElement,n)}})();var f=["*"],go=["outletContent"],vo=["outlet"],yo=[[["","slot","top"]],"*",[["ion-tab"]]],Io=["[slot=top]","*","ion-tab"];function bo(n,o){if(n&1){let t=Kn();ye(0,"ion-router-outlet",5,1),q("stackWillChange",function(i){Me(t);let s=ee();return Be(s.onStackWillChange(i))})("stackDidChange",function(i){Me(t);let s=ee();return Be(s.onStackDidChange(i))}),Ie()}}function Co(n,o){n&1&&l(0,2,["*ngIf","tabs.length > 0"])}function Do(n,o){if(n&1&&(ye(0,"div",1),Pe(1,2),Ie()),n&2){let t=ee();ge(),N("ngTemplateOutlet",t.template)}}function xo(n,o){if(n&1&&Pe(0,1),n&2){let t=ee();N("ngTemplateOutlet",t.template)}}var jo=(()=>{class n extends ne{constructor(t,e){super(t,e)}writeValue(t){this.elementRef.nativeElement.checked=this.lastValue=t,mi(this.elementRef)}_handleIonChange(t){this.handleValueChange(t,t.checked)}static \u0275fac=function(e){return new(e||n)(r(E),r(a))};static \u0275dir=k({type:n,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(e,i){e&1&&q("ionChange",function(g){return i._handleIonChange(g.target)})},standalone:!1,features:[L([{provide:te,useExisting:n,multi:!0}]),D]})}return n})(),wo=(()=>{class n extends ne{el;constructor(t,e){super(t,e),this.el=e}handleInputEvent(t){this.handleValueChange(t,t.value)}registerOnChange(t){this.el.nativeElement.tagName==="ION-INPUT"||this.el.nativeElement.tagName==="ION-INPUT-OTP"?super.registerOnChange(e=>{t(e===""?null:parseFloat(e))}):super.registerOnChange(t)}static \u0275fac=function(e){return new(e||n)(r(E),r(a))};static \u0275dir=k({type:n,selectors:[["ion-input","type","number"],["ion-input-otp",3,"type","text"],["ion-range"]],hostBindings:function(e,i){e&1&&q("ionInput",function(g){return i.handleInputEvent(g.target)})},standalone:!1,features:[L([{provide:te,useExisting:n,multi:!0}]),D]})}return n})(),To=(()=>{class n extends ne{constructor(t,e){super(t,e)}_handleChangeEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(e){return new(e||n)(r(E),r(a))};static \u0275dir=k({type:n,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(e,i){e&1&&q("ionChange",function(g){return i._handleChangeEvent(g.target)})},standalone:!1,features:[L([{provide:te,useExisting:n,multi:!0}]),D]})}return n})(),So=(()=>{class n extends ne{constructor(t,e){super(t,e)}_handleInputEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(e){return new(e||n)(r(E),r(a))};static \u0275dir=k({type:n,selectors:[["ion-input",3,"type","number"],["ion-input-otp","type","text"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(e,i){e&1&&q("ionInput",function(g){return i._handleInputEvent(g.target)})},standalone:!1,features:[L([{provide:te,useExisting:n,multi:!0}]),D]})}return n})(),Eo=(n,o)=>{let t=n.prototype;o.forEach(e=>{Object.defineProperty(t,e,{get(){return this.el[e]},set(i){this.z.runOutsideAngular(()=>this.el[e]=i)},configurable:!0})})},ko=(n,o)=>{let t=n.prototype;o.forEach(e=>{t[e]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[e].apply(this.el,i))}})},b=(n,o,t)=>{t.forEach(e=>n[e]=qn(o,e))};function m(n){return function(t){let{defineCustomElementFn:e,inputs:i,methods:s}=n;return e!==void 0&&e(),i&&Eo(t,i),s&&ko(t,s),t}}var Ro=(()=>{let n=class Ge{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ge)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ge,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],n),n})(),_o=(()=>{let n=class Ue{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||Ue)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ue,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],n),n})(),Ao=(()=>{let n=class Xe{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||Xe)(r(p),r(a),r(d))};static \u0275cmp=c({type:Xe,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],n),n})(),Mo=(()=>{let n=class $e{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||$e)(r(p),r(a),r(d))};static \u0275cmp=c({type:$e,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],n),n})(),Bo=(()=>{let n=class Ke{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ke)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ke,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({methods:["setFocus"]})],n),n})(),Oo=(()=>{let n=class Ye{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ye)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ye,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({})],n),n})(),zo=(()=>{let n=class Qe{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionBackdropTap"])}static \u0275fac=function(e){return new(e||Qe)(r(p),r(a),r(d))};static \u0275cmp=c({type:Qe,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["stopPropagation","tappable","visible"]})],n),n})(),Po=(()=>{let n=class Je{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Je)(r(p),r(a),r(d))};static \u0275cmp=c({type:Je,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode"]})],n),n})(),Fo=(()=>{let n=class et{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||et)(r(p),r(a),r(d))};static \u0275cmp=c({type:et,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],n),n})(),No=(()=>{let n=class tt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(e){return new(e||tt)(r(p),r(a),r(d))};static \u0275cmp=c({type:tt,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],n),n})(),Lo=(()=>{let n=class nt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||nt)(r(p),r(a),r(d))};static \u0275cmp=c({type:nt,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],n),n})(),Vo=(()=>{let n=class it{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||it)(r(p),r(a),r(d))};static \u0275cmp=c({type:it,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["collapse"]})],n),n})(),qo=(()=>{let n=class ot{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ot)(r(p),r(a),r(d))};static \u0275cmp=c({type:ot,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],n),n})(),Ho=(()=>{let n=class rt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||rt)(r(p),r(a),r(d))};static \u0275cmp=c({type:rt,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["mode"]})],n),n})(),Zo=(()=>{let n=class st{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||st)(r(p),r(a),r(d))};static \u0275cmp=c({type:st,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode","translucent"]})],n),n})(),Wo=(()=>{let n=class at{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||at)(r(p),r(a),r(d))};static \u0275cmp=c({type:at,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode"]})],n),n})(),Go=(()=>{let n=class ct{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ct)(r(p),r(a),r(d))};static \u0275cmp=c({type:ct,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode"]})],n),n})(),Uo=(()=>{let n=class lt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||lt)(r(p),r(a),r(d))};static \u0275cmp=c({type:lt,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],n),n})(),Xo=(()=>{let n=class dt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||dt)(r(p),r(a),r(d))};static \u0275cmp=c({type:dt,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","disabled","mode","outline"]})],n),n})(),$o=(()=>{let n=class ut{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ut)(r(p),r(a),r(d))};static \u0275cmp=c({type:ut,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],n),n})(),Ko=(()=>{let n=class pt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(e){return new(e||pt)(r(p),r(a),r(d))};static \u0275cmp=c({type:pt,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],n),n})(),Yo=(()=>{let n=class ft{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||ft)(r(p),r(a),r(d))};static \u0275cmp=c({type:ft,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showAdjacentDays:"showAdjacentDays",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showAdjacentDays","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],n),n})(),Qo=(()=>{let n=class ht{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||ht)(r(p),r(a),r(d))};static \u0275cmp=c({type:ht,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","datetime","disabled","mode"]})],n),n})(),Jo=(()=>{let n=class mt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||mt)(r(p),r(a),r(d))};static \u0275cmp=c({type:mt,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],n),n})(),er=(()=>{let n=class gt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||gt)(r(p),r(a),r(d))};static \u0275cmp=c({type:gt,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],n),n})(),tr=(()=>{let n=class vt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||vt)(r(p),r(a),r(d))};static \u0275cmp=c({type:vt,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["activated","side"]})],n),n})(),nr=(()=>{let n=class yt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||yt)(r(p),r(a),r(d))};static \u0275cmp=c({type:yt,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["collapse","mode","translucent"]})],n),n})(),ir=(()=>{let n=class It{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||It)(r(p),r(a),r(d))};static \u0275cmp=c({type:It,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["fixed"]})],n),n})(),or=(()=>{let n=class bt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||bt)(r(p),r(a),r(d))};static \u0275cmp=c({type:bt,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["collapse","mode","translucent"]})],n),n})(),rr=(()=>{let n=class Ct{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ct)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ct,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],n),n})(),sr=(()=>{let n=class Dt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(e){return new(e||Dt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Dt,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["alt","src"]})],n),n})(),ar=(()=>{let n=class xt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionInfinite"])}static \u0275fac=function(e){return new(e||xt)(r(p),r(a),r(d))};static \u0275cmp=c({type:xt,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["disabled","position","threshold"],methods:["complete"]})],n),n})(),cr=(()=>{let n=class jt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||jt)(r(p),r(a),r(d))};static \u0275cmp=c({type:jt,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["loadingSpinner","loadingText"]})],n),n})(),lr=(()=>{let n=class wt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||wt)(r(p),r(a),r(d))};static \u0275cmp=c({type:wt,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],n),n})(),dr=(()=>{let n=class Tt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionInput","ionChange","ionComplete","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||Tt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Tt,selectors:[["ion-input-otp"]],inputs:{autocapitalize:"autocapitalize",color:"color",disabled:"disabled",fill:"fill",inputmode:"inputmode",length:"length",pattern:"pattern",readonly:"readonly",separators:"separators",shape:"shape",size:"size",type:"type",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["autocapitalize","color","disabled","fill","inputmode","length","pattern","readonly","separators","shape","size","type","value"],methods:["setFocus"]})],n),n})(),ur=(()=>{let n=class St{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||St)(r(p),r(a),r(d))};static \u0275cmp=c({type:St,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","hideIcon","mode","showIcon"]})],n),n})(),pr=(()=>{let n=class Et{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Et)(r(p),r(a),r(d))};static \u0275cmp=c({type:Et,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],n),n})(),fr=(()=>{let n=class kt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||kt)(r(p),r(a),r(d))};static \u0275cmp=c({type:kt,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode","sticky"]})],n),n})(),hr=(()=>{let n=class Rt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Rt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Rt,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({})],n),n})(),mr=(()=>{let n=class _t{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||_t)(r(p),r(a),r(d))};static \u0275cmp=c({type:_t,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],n),n})(),gr=(()=>{let n=class At{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionSwipe"])}static \u0275fac=function(e){return new(e||At)(r(p),r(a),r(d))};static \u0275cmp=c({type:At,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["side"]})],n),n})(),vr=(()=>{let n=class Mt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionDrag"])}static \u0275fac=function(e){return new(e||Mt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Mt,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],n),n})(),yr=(()=>{let n=class Bt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Bt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Bt,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode","position"]})],n),n})(),Ir=(()=>{let n=class Ot{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ot)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ot,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],n),n})(),br=(()=>{let n=class zt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||zt)(r(p),r(a),r(d))};static \u0275cmp=c({type:zt,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","lines","mode"]})],n),n})(),Cr=(()=>{let n=class Pt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||Pt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Pt,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],n),n})(),Dr=(()=>{let n=class Ft{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(e){return new(e||Ft)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ft,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],n),n})(),xr=(()=>{let n=class Nt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Nt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Nt,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["autoHide","color","disabled","menu","mode","type"]})],n),n})(),jr=(()=>{let n=class Lt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Lt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Lt,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["autoHide","menu"]})],n),n})(),wr=(()=>{let n=class Vt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Vt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Vt,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["component","componentProps","routerAnimation","routerDirection"]})],n),n})(),Tr=(()=>{let n=class qt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||qt)(r(p),r(a),r(d))};static \u0275cmp=c({type:qt,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode"]})],n),n})(),Sr=(()=>{let n=class Ht{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ht)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ht,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["mode"]})],n),n})(),Er=(()=>{let n=class Zt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||Zt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Zt,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],n),n})(),kr=(()=>{let n=class Wt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Wt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Wt,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","disabled","value"]})],n),n})(),Rr=(()=>{let n=class Gt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||Gt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Gt,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],n),n})(),_r=(()=>{let n=class Ut{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Ut)(r(p),r(a),r(d))};static \u0275cmp=c({type:Ut,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["buffer","color","mode","reversed","type","value"]})],n),n})(),Ar=(()=>{let n=class Xt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||Xt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Xt,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],n),n})(),Mr=(()=>{let n=class $t{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||$t)(r(p),r(a),r(d))};static \u0275cmp=c({type:$t,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],n),n})(),Br=(()=>{let n=class Kt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(e){return new(e||Kt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Kt,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],n),n})(),Or=(()=>{let n=class Yt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(e){return new(e||Yt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Yt,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],n),n})(),zr=(()=>{let n=class Qt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Qt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Qt,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],n),n})(),Pr=(()=>{let n=class Jt{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Jt)(r(p),r(a),r(d))};static \u0275cmp=c({type:Jt,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({})],n),n})(),Fr=(()=>{let n=class en{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionItemReorder"])}static \u0275fac=function(e){return new(e||en)(r(p),r(a),r(d))};static \u0275cmp=c({type:en,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["disabled"],methods:["complete"]})],n),n})(),Nr=(()=>{let n=class tn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||tn)(r(p),r(a),r(d))};static \u0275cmp=c({type:tn,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["type"],methods:["addRipple"]})],n),n})(),Lr=(()=>{let n=class nn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||nn)(r(p),r(a),r(d))};static \u0275cmp=c({type:nn,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({})],n),n})(),Vr=(()=>{let n=class on{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||on)(r(p),r(a),r(d))};static \u0275cmp=c({type:on,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],n),n})(),qr=(()=>{let n=class rn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange"])}static \u0275fac=function(e){return new(e||rn)(r(p),r(a),r(d))};static \u0275cmp=c({type:rn,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],n),n})(),Hr=(()=>{let n=class sn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||sn)(r(p),r(a),r(d))};static \u0275cmp=c({type:sn,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["contentId","disabled","layout","mode","type","value"]})],n),n})(),Zr=(()=>{let n=class an{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||an)(r(p),r(a),r(d))};static \u0275cmp=c({type:an,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({})],n),n})(),Wr=(()=>{let n=class cn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(e){return new(e||cn)(r(p),r(a),r(d))};static \u0275cmp=c({type:cn,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["disabled"]})],n),n})(),Gr=(()=>{let n=class ln{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||ln)(r(p),r(a),r(d))};static \u0275cmp=c({type:ln,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],n),n})(),Ur=(()=>{let n=class dn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||dn)(r(p),r(a),r(d))};static \u0275cmp=c({type:dn,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["header","multiple","options"]})],n),n})(),Xr=(()=>{let n=class un{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||un)(r(p),r(a),r(d))};static \u0275cmp=c({type:un,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["disabled","value"]})],n),n})(),$r=(()=>{let n=class pn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||pn)(r(p),r(a),r(d))};static \u0275cmp=c({type:pn,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated"]})],n),n})(),Kr=(()=>{let n=class fn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||fn)(r(p),r(a),r(d))};static \u0275cmp=c({type:fn,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","duration","name","paused"]})],n),n})(),Yr=(()=>{let n=class hn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(e){return new(e||hn)(r(p),r(a),r(d))};static \u0275cmp=c({type:hn,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["contentId","disabled","when"]})],n),n})(),Di=(()=>{let n=class mn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||mn)(r(p),r(a),r(d))};static \u0275cmp=c({type:mn,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["component","tab"],methods:["setActive"]})],n),n})(),gn=(()=>{let n=class vn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||vn)(r(p),r(a),r(d))};static \u0275cmp=c({type:vn,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode","selectedTab","translucent"]})],n),n})(),Qr=(()=>{let n=class yn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||yn)(r(p),r(a),r(d))};static \u0275cmp=c({type:yn,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],n),n})(),Jr=(()=>{let n=class In{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||In)(r(p),r(a),r(d))};static \u0275cmp=c({type:In,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode"]})],n),n})(),es=(()=>{let n=class bn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(e){return new(e||bn)(r(p),r(a),r(d))};static \u0275cmp=c({type:bn,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],n),n})(),ts=(()=>{let n=class Cn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Cn)(r(p),r(a),r(d))};static \u0275cmp=c({type:Cn,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({})],n),n})(),ns=(()=>{let n=class Dn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||Dn)(r(p),r(a),r(d))};static \u0275cmp=c({type:Dn,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","size"]})],n),n})(),is=(()=>{let n=class xn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(e){return new(e||xn)(r(p),r(a),r(d))};static \u0275cmp=c({type:xn,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],n),n})(),os=(()=>{let n=class jn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement,b(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(e){return new(e||jn)(r(p),r(a),r(d))};static \u0275cmp=c({type:jn,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],n),n})(),rs=(()=>{let n=class wn{z;el;constructor(t,e,i){this.z=i,t.detach(),this.el=e.nativeElement}static \u0275fac=function(e){return new(e||wn)(r(p),r(a),r(d))};static \u0275cmp=c({type:wn,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})};return n=h([m({inputs:["color","mode"]})],n),n})(),De=(()=>{class n extends ai{parentOutlet;outletContent;constructor(t,e,i,s,g,v,y,j){super(t,e,i,s,g,v,y,j),this.parentOutlet=j}static \u0275fac=function(e){return new(e||n)(Oe("name"),Oe("tabs"),r(Yn),r(a),r(ei),r(d),r(Jn),r(n,12))};static \u0275cmp=c({type:n,selectors:[["ion-router-outlet"]],viewQuery:function(e,i){if(e&1&&Fe(go,7,Wn),e&2){let s;H(s=Z())&&(i.outletContent=s.first)}},standalone:!1,features:[D],ngContentSelectors:f,decls:3,vars:0,consts:[["outletContent",""]],template:function(e,i){e&1&&(u(),Xn(0,null,0),l(2),$n())},encapsulation:2})}return n})(),ss=(()=>{class n extends fi{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let t;return function(i){return(t||(t=B(n)))(i||n)}})();static \u0275cmp=c({type:n,selectors:[["ion-tabs"]],contentQueries:function(e,i,s){if(e&1&&(be(s,gn,5),be(s,gn,4),be(s,Di,4)),e&2){let g;H(g=Z())&&(i.tabBar=g.first),H(g=Z())&&(i.tabBars=g),H(g=Z())&&(i.tabs=g)}},viewQuery:function(e,i){if(e&1&&Fe(vo,5,De),e&2){let s;H(s=Z())&&(i.outlet=s.first)}},standalone:!1,features:[D],ngContentSelectors:Io,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(e,i){e&1&&(u(yo),l(0),ye(1,"div",2,0),ve(3,bo,2,0,"ion-router-outlet",3)(4,Co,1,0,"ng-content",4),Ie(),l(5,1)),e&2&&(ge(3),N("ngIf",i.tabs.length===0),ge(),N("ngIf",i.tabs.length>0))},dependencies:[Ce,De],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return n})(),as=(()=>{class n extends li{constructor(t,e,i,s,g,v){super(t,e,i,s,g,v)}static \u0275fac=function(e){return new(e||n)(r(De,8),r(ii),r(oi),r(a),r(d),r(p))};static \u0275cmp=c({type:n,selectors:[["ion-back-button"]],standalone:!1,features:[D],ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})}return n})(),cs=(()=>{class n extends pi{constructor(t,e,i,s,g,v){super(t,e,i,s,g,v)}static \u0275fac=function(e){return new(e||n)(r(a),r(J),r(E),r(W),r(d),r(p))};static \u0275cmp=c({type:n,selectors:[["ion-nav"]],standalone:!1,features:[D],ngContentSelectors:f,decls:1,vars:0,template:function(e,i){e&1&&(u(),l(0))},encapsulation:2,changeDetection:0})}return n})(),ls=(()=>{class n extends di{static \u0275fac=(()=>{let t;return function(i){return(t||(t=B(n)))(i||n)}})();static \u0275dir=k({type:n,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[D]})}return n})(),ds=(()=>{class n extends ui{static \u0275fac=(()=>{let t;return function(i){return(t||(t=B(n)))(i||n)}})();static \u0275dir=k({type:n,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[D]})}return n})(),us=(()=>{class n extends si{static \u0275fac=(()=>{let t;return function(i){return(t||(t=B(n)))(i||n)}})();static \u0275cmp=c({type:n,selectors:[["ion-modal"]],standalone:!1,features:[D],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(e,i){e&1&&ve(0,Do,2,1,"div",0),e&2&&N("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Ce,Ne],encapsulation:2,changeDetection:0})}return n})(),ps=(()=>{class n extends ri{static \u0275fac=(()=>{let t;return function(i){return(t||(t=B(n)))(i||n)}})();static \u0275cmp=c({type:n,selectors:[["ion-popover"]],standalone:!1,features:[D],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(e,i){e&1&&ve(0,xo,1,1,"ng-container",0),e&2&&N("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Ce,Ne],encapsulation:2,changeDetection:0})}return n})(),fs={provide:Le,useExisting:Ae(()=>xi),multi:!0},xi=(()=>{class n extends ti{static \u0275fac=(()=>{let t;return function(i){return(t||(t=B(n)))(i||n)}})();static \u0275dir=k({type:n,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(e,i){e&2&&ze("max",i._enabled?i.max:null)},standalone:!1,features:[L([fs]),D]})}return n})(),hs={provide:Le,useExisting:Ae(()=>ji),multi:!0},ji=(()=>{class n extends ni{static \u0275fac=(()=>{let t;return function(i){return(t||(t=B(n)))(i||n)}})();static \u0275dir=k({type:n,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(e,i){e&2&&ze("min",i._enabled?i.min:null)},standalone:!1,features:[L([hs]),D]})}return n})();var ms=(()=>{class n extends qe{angularDelegate=F(W);injector=F(E);environmentInjector=F(J);constructor(){super(He)}create(t){return super.create(he(fe({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(e){return new(e||n)};static \u0275prov=me({token:n,factory:n.\u0275fac})}return n})();var Tn=class extends qe{angularDelegate=F(W);injector=F(E);environmentInjector=F(J);constructor(){super(Ze)}create(o){return super.create(he(fe({},o),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}};var gs=(n,o,t)=>()=>{let e=o.defaultView;if(e&&typeof window<"u"){We(he(fe({},n),{_zoneGate:s=>t.run(s)}));let i="__zone_symbol__addEventListener"in o.body?"__zone_symbol__addEventListener":"addEventListener";return Ci(e,{exclude:["ion-tabs"],syncQueue:!0,raf:hi,jmp:s=>t.runOutsideAngular(s),ael(s,g,v,y){s[i](g,v,y)},rel(s,g,v,y){s.removeEventListener(g,v,y)}})}},vs=[Ro,_o,Ao,Mo,Bo,Oo,zo,Po,Fo,No,Lo,Vo,qo,Ho,Zo,Wo,Go,Uo,Xo,$o,Ko,Yo,Qo,Jo,er,tr,nr,ir,or,rr,sr,ar,cr,lr,dr,ur,pr,fr,hr,mr,gr,vr,yr,Ir,br,Cr,Dr,xr,jr,wr,Tr,Sr,Er,kr,Rr,_r,Ar,Mr,Br,Or,zr,Pr,Fr,Nr,Lr,Vr,qr,Hr,Zr,Wr,Gr,Ur,Xr,$r,Kr,Yr,Di,gn,Qr,Jr,es,ts,ns,is,os,rs],Ha=[...vs,us,ps,jo,wo,To,So,ss,De,as,cs,ls,ds,ji,xi],Za=(()=>{class n{static forRoot(t={}){return{ngModule:n,providers:[{provide:Ve,useValue:t},{provide:Un,useFactory:gs,multi:!0,deps:[Ve,Zn,d]},W,ci()]}}static \u0275fac=function(e){return new(e||n)};static \u0275mod=Gn({type:n});static \u0275inj=Hn({providers:[ms,Tn],imports:[Qn]})}return n})();var S=Object.create(null);S.open="0";S.close="1";S.ping="2";S.pong="3";S.message="4";S.upgrade="5";S.noop="6";var ie=Object.create(null);Object.keys(S).forEach(n=>{ie[S[n]]=n});var oe={type:"error",data:"parser error"};var Si=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",Ei=typeof ArrayBuffer=="function",ki=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n&&n.buffer instanceof ArrayBuffer,re=({type:n,data:o},t,e)=>Si&&o instanceof Blob?t?e(o):wi(o,e):Ei&&(o instanceof ArrayBuffer||ki(o))?t?e(o):wi(new Blob([o]),e):e(S[n]+(o||"")),wi=(n,o)=>{let t=new FileReader;return t.onload=function(){let e=t.result.split(",")[1];o("b"+(e||""))},t.readAsDataURL(n)};function Ti(n){return n instanceof Uint8Array?n:n instanceof ArrayBuffer?new Uint8Array(n):new Uint8Array(n.buffer,n.byteOffset,n.byteLength)}var Sn;function Ri(n,o){if(Si&&n.data instanceof Blob)return n.data.arrayBuffer().then(Ti).then(o);if(Ei&&(n.data instanceof ArrayBuffer||ki(n.data)))return o(Ti(n.data));re(n,!1,t=>{Sn||(Sn=new TextEncoder),o(Sn.encode(t))})}var _i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",se=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let n=0;n<_i.length;n++)se[_i.charCodeAt(n)]=n;var Ai=n=>{let o=n.length*.75,t=n.length,e,i=0,s,g,v,y;n[n.length-1]==="="&&(o--,n[n.length-2]==="="&&o--);let j=new ArrayBuffer(o),T=new Uint8Array(j);for(e=0;e<t;e+=4)s=se[n.charCodeAt(e)],g=se[n.charCodeAt(e+1)],v=se[n.charCodeAt(e+2)],y=se[n.charCodeAt(e+3)],T[i++]=s<<2|g>>4,T[i++]=(g&15)<<4|v>>2,T[i++]=(v&3)<<6|y&63;return j};var ys=typeof ArrayBuffer=="function",ae=(n,o)=>{if(typeof n!="string")return{type:"message",data:Mi(n,o)};let t=n.charAt(0);return t==="b"?{type:"message",data:Is(n.substring(1),o)}:ie[t]?n.length>1?{type:ie[t],data:n.substring(1)}:{type:ie[t]}:oe},Is=(n,o)=>{if(ys){let t=Ai(n);return Mi(t,o)}else return{base64:!0,data:n}},Mi=(n,o)=>{switch(o){case"blob":return n instanceof Blob?n:new Blob([n]);case"arraybuffer":default:return n instanceof ArrayBuffer?n:n.buffer}};var Bi="",Oi=(n,o)=>{let t=n.length,e=new Array(t),i=0;n.forEach((s,g)=>{re(s,!1,v=>{e[g]=v,++i===t&&o(e.join(Bi))})})},zi=(n,o)=>{let t=n.split(Bi),e=[];for(let i=0;i<t.length;i++){let s=ae(t[i],o);if(e.push(s),s.type==="error")break}return e};function Pi(){return new TransformStream({transform(n,o){Ri(n,t=>{let e=t.length,i;if(e<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,e);else if(e<65536){i=new Uint8Array(3);let s=new DataView(i.buffer);s.setUint8(0,126),s.setUint16(1,e)}else{i=new Uint8Array(9);let s=new DataView(i.buffer);s.setUint8(0,127),s.setBigUint64(1,BigInt(e))}n.data&&typeof n.data!="string"&&(i[0]|=128),o.enqueue(i),o.enqueue(t)})}})}var En;function xe(n){return n.reduce((o,t)=>o+t.length,0)}function je(n,o){if(n[0].length===o)return n.shift();let t=new Uint8Array(o),e=0;for(let i=0;i<o;i++)t[i]=n[0][e++],e===n[0].length&&(n.shift(),e=0);return n.length&&e<n[0].length&&(n[0]=n[0].slice(e)),t}function Fi(n,o){En||(En=new TextDecoder);let t=[],e=0,i=-1,s=!1;return new TransformStream({transform(g,v){for(t.push(g);;){if(e===0){if(xe(t)<1)break;let y=je(t,1);s=(y[0]&128)===128,i=y[0]&127,i<126?e=3:i===126?e=1:e=2}else if(e===1){if(xe(t)<2)break;let y=je(t,2);i=new DataView(y.buffer,y.byteOffset,y.length).getUint16(0),e=3}else if(e===2){if(xe(t)<8)break;let y=je(t,8),j=new DataView(y.buffer,y.byteOffset,y.length),T=j.getUint32(0);if(T>Math.pow(2,21)-1){v.enqueue(oe);break}i=T*Math.pow(2,32)+j.getUint32(4),e=3}else{if(xe(t)<i)break;let y=je(t,i);v.enqueue(ae(s?y:En.decode(y),o)),e=0}if(i===0||i>n){v.enqueue(oe);break}}}})}var kn=4;function C(n){if(n)return bs(n)}function bs(n){for(var o in C.prototype)n[o]=C.prototype[o];return n}C.prototype.on=C.prototype.addEventListener=function(n,o){return this._callbacks=this._callbacks||{},(this._callbacks["$"+n]=this._callbacks["$"+n]||[]).push(o),this};C.prototype.once=function(n,o){function t(){this.off(n,t),o.apply(this,arguments)}return t.fn=o,this.on(n,t),this};C.prototype.off=C.prototype.removeListener=C.prototype.removeAllListeners=C.prototype.removeEventListener=function(n,o){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var t=this._callbacks["$"+n];if(!t)return this;if(arguments.length==1)return delete this._callbacks["$"+n],this;for(var e,i=0;i<t.length;i++)if(e=t[i],e===o||e.fn===o){t.splice(i,1);break}return t.length===0&&delete this._callbacks["$"+n],this};C.prototype.emit=function(n){this._callbacks=this._callbacks||{};for(var o=new Array(arguments.length-1),t=this._callbacks["$"+n],e=1;e<arguments.length;e++)o[e-1]=arguments[e];if(t){t=t.slice(0);for(var e=0,i=t.length;e<i;++e)t[e].apply(this,o)}return this};C.prototype.emitReserved=C.prototype.emit;C.prototype.listeners=function(n){return this._callbacks=this._callbacks||{},this._callbacks["$"+n]||[]};C.prototype.hasListeners=function(n){return!!this.listeners(n).length};var R=typeof Promise=="function"&&typeof Promise.resolve=="function"?o=>Promise.resolve().then(o):(o,t)=>t(o,0),x=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),Ni="arraybuffer";function we(n,...o){return o.reduce((t,e)=>(n.hasOwnProperty(e)&&(t[e]=n[e]),t),{})}var Cs=x.setTimeout,Ds=x.clearTimeout;function _(n,o){o.useNativeTimers?(n.setTimeoutFn=Cs.bind(x),n.clearTimeoutFn=Ds.bind(x)):(n.setTimeoutFn=x.setTimeout.bind(x),n.clearTimeoutFn=x.clearTimeout.bind(x))}var xs=1.33;function Li(n){return typeof n=="string"?js(n):Math.ceil((n.byteLength||n.size)*xs)}function js(n){let o=0,t=0;for(let e=0,i=n.length;e<i;e++)o=n.charCodeAt(e),o<128?t+=1:o<2048?t+=2:o<55296||o>=57344?t+=3:(e++,t+=4);return t}function Te(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function Vi(n){let o="";for(let t in n)n.hasOwnProperty(t)&&(o.length&&(o+="&"),o+=encodeURIComponent(t)+"="+encodeURIComponent(n[t]));return o}function qi(n){let o={},t=n.split("&");for(let e=0,i=t.length;e<i;e++){let s=t[e].split("=");o[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return o}var Se=class extends Error{constructor(o,t,e){super(o),this.description=t,this.context=e,this.type="TransportError"}},A=class extends C{constructor(o){super(),this.writable=!1,_(this,o),this.opts=o,this.query=o.query,this.socket=o.socket,this.supportsBinary=!o.forceBase64}onError(o,t,e){return super.emitReserved("error",new Se(o,t,e)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(o){this.readyState==="open"&&this.write(o)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(o){let t=ae(o,this.socket.binaryType);this.onPacket(t)}onPacket(o){super.emitReserved("packet",o)}onClose(o){this.readyState="closed",super.emitReserved("close",o)}pause(o){}createUri(o,t={}){return o+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let o=this.opts.hostname;return o.indexOf(":")===-1?o:"["+o+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(o){let t=Vi(o);return t.length?"?"+t:""}};var ce=class extends A{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(o){this.readyState="pausing";let t=()=>{this.readyState="paused",o()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",function(){--e||t()})),this.writable||(e++,this.once("drain",function(){--e||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(o){let t=e=>{if(this.readyState==="opening"&&e.type==="open"&&this.onOpen(),e.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)};zi(o,this.socket.binaryType).forEach(t),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){let o=()=>{this.write([{type:"close"}])};this.readyState==="open"?o():this.once("open",o)}write(o){this.writable=!1,Oi(o,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let o=this.opts.secure?"https":"http",t=this.query||{};return this.opts.timestampRequests!==!1&&(t[this.opts.timestampParam]=Te()),!this.supportsBinary&&!t.sid&&(t.b64=1),this.createUri(o,t)}};var Hi=!1;try{Hi=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}var Zi=Hi;function ws(){}var Rn=class extends ce{constructor(o){if(super(o),typeof location<"u"){let t=location.protocol==="https:",e=location.port;e||(e=t?"443":"80"),this.xd=typeof location<"u"&&o.hostname!==location.hostname||e!==o.port}}doWrite(o,t){let e=this.request({method:"POST",data:o});e.on("success",t),e.on("error",(i,s)=>{this.onError("xhr post error",i,s)})}doPoll(){let o=this.request();o.on("data",this.onData.bind(this)),o.on("error",(t,e)=>{this.onError("xhr poll error",t,e)}),this.pollXhr=o}},Ee=(()=>{class n extends C{constructor(t,e,i){super(),this.createRequest=t,_(this,i),this._opts=i,this._method=i.method||"GET",this._uri=e,this._data=i.data!==void 0?i.data:null,this._create()}_create(){var t;let e=we(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;let i=this._xhr=this.createRequest(e);try{i.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){i.setDisableHeaderCheck&&i.setDisableHeaderCheck(!0);for(let s in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(s)&&i.setRequestHeader(s,this._opts.extraHeaders[s])}}catch{}if(this._method==="POST")try{i.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{i.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(i),"withCredentials"in i&&(i.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(i.timeout=this._opts.requestTimeout),i.onreadystatechange=()=>{var s;i.readyState===3&&((s=this._opts.cookieJar)===null||s===void 0||s.parseCookies(i.getResponseHeader("set-cookie"))),i.readyState===4&&(i.status===200||i.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof i.status=="number"?i.status:0)},0))},i.send(this._data)}catch(s){this.setTimeoutFn(()=>{this._onError(s)},0);return}typeof document<"u"&&(this._index=n.requestsCount++,n.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=ws,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete n.requests[this._index],this._xhr=null}}_onLoad(){let t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}return n.requestsCount=0,n.requests={},n})();if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Wi);else if(typeof addEventListener=="function"){let n="onpagehide"in x?"pagehide":"unload";addEventListener(n,Wi,!1)}}function Wi(){for(let n in Ee.requests)Ee.requests.hasOwnProperty(n)&&Ee.requests[n].abort()}var Ts=function(){let n=Gi({xdomain:!1});return n&&n.responseType!==null}(),O=class extends Rn{constructor(o){super(o);let t=o&&o.forceBase64;this.supportsBinary=Ts&&!t}request(o={}){return Object.assign(o,{xd:this.xd},this.opts),new Ee(Gi,this.uri(),o)}};function Gi(n){let o=n.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!o||Zi))return new XMLHttpRequest}catch{}if(!o)try{return new x[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}var Ui=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative",An=class extends A{get name(){return"websocket"}doOpen(){let o=this.uri(),t=this.opts.protocols,e=Ui?{}:we(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(e.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(o,t,e)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=o=>this.onClose({description:"websocket connection closed",context:o}),this.ws.onmessage=o=>this.onData(o.data),this.ws.onerror=o=>this.onError("websocket error",o)}write(o){this.writable=!1;for(let t=0;t<o.length;t++){let e=o[t],i=t===o.length-1;re(e,this.supportsBinary,s=>{try{this.doWrite(e,s)}catch{}i&&R(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let o=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=Te()),this.supportsBinary||(t.b64=1),this.createUri(o,t)}},_n=x.WebSocket||x.MozWebSocket,z=class extends An{createSocket(o,t,e){return Ui?new _n(o,t,e):t?new _n(o,t):new _n(o)}doWrite(o,t){this.ws.send(t)}};var G=class extends A{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(o){return this.emitReserved("error",o)}this._transport.closed.then(()=>{this.onClose()}).catch(o=>{this.onError("webtransport error",o)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(o=>{let t=Fi(Number.MAX_SAFE_INTEGER,this.socket.binaryType),e=o.readable.pipeThrough(t).getReader(),i=Pi();i.readable.pipeTo(o.writable),this._writer=i.writable.getWriter();let s=()=>{e.read().then(({done:v,value:y})=>{v||(this.onPacket(y),s())}).catch(v=>{})};s();let g={type:"open"};this.query.sid&&(g.data=`{"sid":"${this.query.sid}"}`),this._writer.write(g).then(()=>this.onOpen())})})}write(o){this.writable=!1;for(let t=0;t<o.length;t++){let e=o[t],i=t===o.length-1;this._writer.write(e).then(()=>{i&&R(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var o;(o=this._transport)===null||o===void 0||o.close()}};var Mn={websocket:z,webtransport:G,polling:O};var Ss=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Es=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function U(n){if(n.length>8e3)throw"URI too long";let o=n,t=n.indexOf("["),e=n.indexOf("]");t!=-1&&e!=-1&&(n=n.substring(0,t)+n.substring(t,e).replace(/:/g,";")+n.substring(e,n.length));let i=Ss.exec(n||""),s={},g=14;for(;g--;)s[Es[g]]=i[g]||"";return t!=-1&&e!=-1&&(s.source=o,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=ks(s,s.path),s.queryKey=Rs(s,s.query),s}function ks(n,o){let t=/\/{2,9}/g,e=o.replace(t,"/").split("/");return(o.slice(0,1)=="/"||o.length===0)&&e.splice(0,1),o.slice(-1)=="/"&&e.splice(e.length-1,1),e}function Rs(n,o){let t={};return o.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,i,s){i&&(t[i]=s)}),t}var Bn=typeof addEventListener=="function"&&typeof removeEventListener=="function",ke=[];Bn&&addEventListener("offline",()=>{ke.forEach(n=>n())},!1);var Re=(()=>{class n extends C{constructor(t,e){if(super(),this.binaryType=Ni,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(e=t,t=null),t){let i=U(t);e.hostname=i.host,e.secure=i.protocol==="https"||i.protocol==="wss",e.port=i.port,i.query&&(e.query=i.query)}else e.host&&(e.hostname=U(e.host).host);_(this,e),this.secure=e.secure!=null?e.secure:typeof location<"u"&&location.protocol==="https:",e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=e.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(i=>{let s=i.prototype.name;this.transports.push(s),this._transportsByName[s]=i}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=qi(this.opts.query)),Bn&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ke.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){let e=Object.assign({},this.opts.query);e.EIO=kn,e.transport=t,this.id&&(e.sid=this.id);let i=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](i)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let t=this.opts.rememberUpgrade&&n.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";let e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",n.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let e=new Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let i=0;i<this.writeBuffer.length;i++){let s=this.writeBuffer[i].data;if(s&&(e+=Li(s)),i>0&&e>this._maxPayload)return this.writeBuffer.slice(0,i);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,R(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,i){return this._sendPacket("message",t,e,i),this}send(t,e,i){return this._sendPacket("message",t,e,i),this}_sendPacket(t,e,i,s){if(typeof e=="function"&&(s=e,e=void 0),typeof i=="function"&&(s=i,i=null),this.readyState==="closing"||this.readyState==="closed")return;i=i||{},i.compress=i.compress!==!1;let g={type:t,data:e,options:i};this.emitReserved("packetCreate",g),this.writeBuffer.push(g),s&&this.once("flush",s),this.flush()}close(){let t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},i=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?i():t()}):this.upgrading?i():t()),this}_onError(t){if(n.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Bn&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let i=ke.indexOf(this._offlineEventListener);i!==-1&&ke.splice(i,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}return n.protocol=kn,n})(),_e=class extends Re{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let o=0;o<this._upgrades.length;o++)this._probe(this._upgrades[o])}_probe(o){let t=this.createTransport(o),e=!1;Re.priorWebsocketSuccess=!1;let i=()=>{e||(t.send([{type:"ping",data:"probe"}]),t.once("packet",M=>{if(!e)if(M.type==="pong"&&M.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;Re.priorWebsocketSuccess=t.name==="websocket",this.transport.pause(()=>{e||this.readyState!=="closed"&&(T(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{let Y=new Error("probe error");Y.transport=t.name,this.emitReserved("upgradeError",Y)}}))};function s(){e||(e=!0,T(),t.close(),t=null)}let g=M=>{let Y=new Error("probe error: "+M);Y.transport=t.name,s(),this.emitReserved("upgradeError",Y)};function v(){g("transport closed")}function y(){g("socket closed")}function j(M){t&&M.name!==t.name&&s()}let T=()=>{t.removeListener("open",i),t.removeListener("error",g),t.removeListener("close",v),this.off("close",y),this.off("upgrading",j)};t.once("open",i),t.once("error",g),t.once("close",v),this.once("close",y),this.once("upgrading",j),this._upgrades.indexOf("webtransport")!==-1&&o!=="webtransport"?this.setTimeoutFn(()=>{e||t.open()},200):t.open()}onHandshake(o){this._upgrades=this._filterUpgrades(o.upgrades),super.onHandshake(o)}_filterUpgrades(o){let t=[];for(let e=0;e<o.length;e++)~this.transports.indexOf(o[e])&&t.push(o[e]);return t}},X=class extends _e{constructor(o,t={}){let e=typeof o=="object"?o:t;(!e.transports||e.transports&&typeof e.transports[0]=="string")&&(e.transports=(e.transports||["polling","websocket","webtransport"]).map(i=>Mn[i]).filter(i=>!!i)),super(o,e)}};var Qc=X.protocol;function Xi(n,o="",t){let e=n;t=t||typeof location<"u"&&location,n==null&&(n=t.protocol+"//"+t.host),typeof n=="string"&&(n.charAt(0)==="/"&&(n.charAt(1)==="/"?n=t.protocol+n:n=t.host+n),/^(https?|wss?):\/\//.test(n)||(typeof t<"u"?n=t.protocol+"//"+n:n="https://"+n),e=U(n)),e.port||(/^(http|ws)$/.test(e.protocol)?e.port="80":/^(http|ws)s$/.test(e.protocol)&&(e.port="443")),e.path=e.path||"/";let s=e.host.indexOf(":")!==-1?"["+e.host+"]":e.host;return e.id=e.protocol+"://"+s+":"+e.port+o,e.href=e.protocol+"://"+s+(t&&t.port===e.port?"":":"+e.port),e}var Ln={};eo(Ln,{Decoder:()=>Fn,Encoder:()=>Pn,PacketType:()=>I,protocol:()=>Ji});var As=typeof ArrayBuffer=="function",Ms=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n.buffer instanceof ArrayBuffer,$i=Object.prototype.toString,Bs=typeof Blob=="function"||typeof Blob<"u"&&$i.call(Blob)==="[object BlobConstructor]",Os=typeof File=="function"||typeof File<"u"&&$i.call(File)==="[object FileConstructor]";function de(n){return As&&(n instanceof ArrayBuffer||Ms(n))||Bs&&n instanceof Blob||Os&&n instanceof File}function le(n,o){if(!n||typeof n!="object")return!1;if(Array.isArray(n)){for(let t=0,e=n.length;t<e;t++)if(le(n[t]))return!0;return!1}if(de(n))return!0;if(n.toJSON&&typeof n.toJSON=="function"&&arguments.length===1)return le(n.toJSON(),!0);for(let t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&le(n[t]))return!0;return!1}function Ki(n){let o=[],t=n.data,e=n;return e.data=On(t,o),e.attachments=o.length,{packet:e,buffers:o}}function On(n,o){if(!n)return n;if(de(n)){let t={_placeholder:!0,num:o.length};return o.push(n),t}else if(Array.isArray(n)){let t=new Array(n.length);for(let e=0;e<n.length;e++)t[e]=On(n[e],o);return t}else if(typeof n=="object"&&!(n instanceof Date)){let t={};for(let e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=On(n[e],o));return t}return n}function Yi(n,o){return n.data=zn(n.data,o),delete n.attachments,n}function zn(n,o){if(!n)return n;if(n&&n._placeholder===!0){if(typeof n.num=="number"&&n.num>=0&&n.num<o.length)return o[n.num];throw new Error("illegal attachments")}else if(Array.isArray(n))for(let t=0;t<n.length;t++)n[t]=zn(n[t],o);else if(typeof n=="object")for(let t in n)Object.prototype.hasOwnProperty.call(n,t)&&(n[t]=zn(n[t],o));return n}var zs=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Ji=5,I=function(n){return n[n.CONNECT=0]="CONNECT",n[n.DISCONNECT=1]="DISCONNECT",n[n.EVENT=2]="EVENT",n[n.ACK=3]="ACK",n[n.CONNECT_ERROR=4]="CONNECT_ERROR",n[n.BINARY_EVENT=5]="BINARY_EVENT",n[n.BINARY_ACK=6]="BINARY_ACK",n}(I||{}),Pn=class{constructor(o){this.replacer=o}encode(o){return(o.type===I.EVENT||o.type===I.ACK)&&le(o)?this.encodeAsBinary({type:o.type===I.EVENT?I.BINARY_EVENT:I.BINARY_ACK,nsp:o.nsp,data:o.data,id:o.id}):[this.encodeAsString(o)]}encodeAsString(o){let t=""+o.type;return(o.type===I.BINARY_EVENT||o.type===I.BINARY_ACK)&&(t+=o.attachments+"-"),o.nsp&&o.nsp!=="/"&&(t+=o.nsp+","),o.id!=null&&(t+=o.id),o.data!=null&&(t+=JSON.stringify(o.data,this.replacer)),t}encodeAsBinary(o){let t=Ki(o),e=this.encodeAsString(t.packet),i=t.buffers;return i.unshift(e),i}};function Qi(n){return Object.prototype.toString.call(n)==="[object Object]"}var Fn=class n extends C{constructor(o){super(),this.reviver=o}add(o){let t;if(typeof o=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(o);let e=t.type===I.BINARY_EVENT;e||t.type===I.BINARY_ACK?(t.type=e?I.EVENT:I.ACK,this.reconstructor=new Nn(t),t.attachments===0&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(de(o)||o.base64)if(this.reconstructor)t=this.reconstructor.takeBinaryData(o),t&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+o)}decodeString(o){let t=0,e={type:Number(o.charAt(0))};if(I[e.type]===void 0)throw new Error("unknown packet type "+e.type);if(e.type===I.BINARY_EVENT||e.type===I.BINARY_ACK){let s=t+1;for(;o.charAt(++t)!=="-"&&t!=o.length;);let g=o.substring(s,t);if(g!=Number(g)||o.charAt(t)!=="-")throw new Error("Illegal attachments");e.attachments=Number(g)}if(o.charAt(t+1)==="/"){let s=t+1;for(;++t&&!(o.charAt(t)===","||t===o.length););e.nsp=o.substring(s,t)}else e.nsp="/";let i=o.charAt(t+1);if(i!==""&&Number(i)==i){let s=t+1;for(;++t;){let g=o.charAt(t);if(g==null||Number(g)!=g){--t;break}if(t===o.length)break}e.id=Number(o.substring(s,t+1))}if(o.charAt(++t)){let s=this.tryParse(o.substr(t));if(n.isPayloadValid(e.type,s))e.data=s;else throw new Error("invalid payload")}return e}tryParse(o){try{return JSON.parse(o,this.reviver)}catch{return!1}}static isPayloadValid(o,t){switch(o){case I.CONNECT:return Qi(t);case I.DISCONNECT:return t===void 0;case I.CONNECT_ERROR:return typeof t=="string"||Qi(t);case I.EVENT:case I.BINARY_EVENT:return Array.isArray(t)&&(typeof t[0]=="number"||typeof t[0]=="string"&&zs.indexOf(t[0])===-1);case I.ACK:case I.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}},Nn=class{constructor(o){this.packet=o,this.buffers=[],this.reconPack=o}takeBinaryData(o){if(this.buffers.push(o),this.buffers.length===this.reconPack.attachments){let t=Yi(this.reconPack,this.buffers);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}};function w(n,o,t){return n.on(o,t),function(){n.off(o,t)}}var Ps=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1}),$=class extends C{constructor(o,t,e){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=o,this.nsp=t,e&&e.auth&&(this.auth=e.auth),this._opts=Object.assign({},e),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let o=this.io;this.subs=[w(o,"open",this.onopen.bind(this)),w(o,"packet",this.onpacket.bind(this)),w(o,"error",this.onerror.bind(this)),w(o,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...o){return o.unshift("message"),this.emit.apply(this,o),this}emit(o,...t){var e,i,s;if(Ps.hasOwnProperty(o))throw new Error('"'+o.toString()+'" is a reserved event name');if(t.unshift(o),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let g={type:I.EVENT,data:t};if(g.options={},g.options.compress=this.flags.compress!==!1,typeof t[t.length-1]=="function"){let T=this.ids++,M=t.pop();this._registerAckCallback(T,M),g.id=T}let v=(i=(e=this.io.engine)===null||e===void 0?void 0:e.transport)===null||i===void 0?void 0:i.writable,y=this.connected&&!(!((s=this.io.engine)===null||s===void 0)&&s._hasPingExpired());return this.flags.volatile&&!v||(y?(this.notifyOutgoingListeners(g),this.packet(g)):this.sendBuffer.push(g)),this.flags={},this}_registerAckCallback(o,t){var e;let i=(e=this.flags.timeout)!==null&&e!==void 0?e:this._opts.ackTimeout;if(i===void 0){this.acks[o]=t;return}let s=this.io.setTimeoutFn(()=>{delete this.acks[o];for(let v=0;v<this.sendBuffer.length;v++)this.sendBuffer[v].id===o&&this.sendBuffer.splice(v,1);t.call(this,new Error("operation has timed out"))},i),g=(...v)=>{this.io.clearTimeoutFn(s),t.apply(this,v)};g.withError=!0,this.acks[o]=g}emitWithAck(o,...t){return new Promise((e,i)=>{let s=(g,v)=>g?i(g):e(v);s.withError=!0,t.push(s),this.emit(o,...t)})}_addToQueue(o){let t;typeof o[o.length-1]=="function"&&(t=o.pop());let e={id:this._queueSeq++,tryCount:0,pending:!1,args:o,flags:Object.assign({fromQueue:!0},this.flags)};o.push((i,...s)=>e!==this._queue[0]?void 0:(i!==null?e.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(i)):(this._queue.shift(),t&&t(null,...s)),e.pending=!1,this._drainQueue())),this._queue.push(e),this._drainQueue()}_drainQueue(o=!1){if(!this.connected||this._queue.length===0)return;let t=this._queue[0];t.pending&&!o||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(o){o.nsp=this.nsp,this.io._packet(o)}onopen(){typeof this.auth=="function"?this.auth(o=>{this._sendConnectPacket(o)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(o){this.packet({type:I.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},o):o})}onerror(o){this.connected||this.emitReserved("connect_error",o)}onclose(o,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",o,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(o=>{if(!this.sendBuffer.some(e=>String(e.id)===o)){let e=this.acks[o];delete this.acks[o],e.withError&&e.call(this,new Error("socket has been disconnected"))}})}onpacket(o){if(o.nsp===this.nsp)switch(o.type){case I.CONNECT:o.data&&o.data.sid?this.onconnect(o.data.sid,o.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case I.EVENT:case I.BINARY_EVENT:this.onevent(o);break;case I.ACK:case I.BINARY_ACK:this.onack(o);break;case I.DISCONNECT:this.ondisconnect();break;case I.CONNECT_ERROR:this.destroy();let e=new Error(o.data.message);e.data=o.data.data,this.emitReserved("connect_error",e);break}}onevent(o){let t=o.data||[];o.id!=null&&t.push(this.ack(o.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(o){if(this._anyListeners&&this._anyListeners.length){let t=this._anyListeners.slice();for(let e of t)e.apply(this,o)}super.emit.apply(this,o),this._pid&&o.length&&typeof o[o.length-1]=="string"&&(this._lastOffset=o[o.length-1])}ack(o){let t=this,e=!1;return function(...i){e||(e=!0,t.packet({type:I.ACK,id:o,data:i}))}}onack(o){let t=this.acks[o.id];typeof t=="function"&&(delete this.acks[o.id],t.withError&&o.data.unshift(null),t.apply(this,o.data))}onconnect(o,t){this.id=o,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(o=>this.emitEvent(o)),this.receiveBuffer=[],this.sendBuffer.forEach(o=>{this.notifyOutgoingListeners(o),this.packet(o)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(o=>o()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:I.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(o){return this.flags.compress=o,this}get volatile(){return this.flags.volatile=!0,this}timeout(o){return this.flags.timeout=o,this}onAny(o){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(o),this}prependAny(o){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(o),this}offAny(o){if(!this._anyListeners)return this;if(o){let t=this._anyListeners;for(let e=0;e<t.length;e++)if(o===t[e])return t.splice(e,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(o){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(o),this}prependAnyOutgoing(o){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(o),this}offAnyOutgoing(o){if(!this._anyOutgoingListeners)return this;if(o){let t=this._anyOutgoingListeners;for(let e=0;e<t.length;e++)if(o===t[e])return t.splice(e,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(o){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){let t=this._anyOutgoingListeners.slice();for(let e of t)e.apply(this,o.data)}}};function V(n){n=n||{},this.ms=n.min||100,this.max=n.max||1e4,this.factor=n.factor||2,this.jitter=n.jitter>0&&n.jitter<=1?n.jitter:0,this.attempts=0}V.prototype.duration=function(){var n=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var o=Math.random(),t=Math.floor(o*this.jitter*n);n=(Math.floor(o*10)&1)==0?n-t:n+t}return Math.min(n,this.max)|0};V.prototype.reset=function(){this.attempts=0};V.prototype.setMin=function(n){this.ms=n};V.prototype.setMax=function(n){this.max=n};V.prototype.setJitter=function(n){this.jitter=n};var K=class extends C{constructor(o,t){var e;super(),this.nsps={},this.subs=[],o&&typeof o=="object"&&(t=o,o=void 0),t=t||{},t.path=t.path||"/socket.io",this.opts=t,_(this,t),this.reconnection(t.reconnection!==!1),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor((e=t.randomizationFactor)!==null&&e!==void 0?e:.5),this.backoff=new V({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(t.timeout==null?2e4:t.timeout),this._readyState="closed",this.uri=o;let i=t.parser||Ln;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=t.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(o){return arguments.length?(this._reconnection=!!o,o||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(o){return o===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=o,this)}reconnectionDelay(o){var t;return o===void 0?this._reconnectionDelay:(this._reconnectionDelay=o,(t=this.backoff)===null||t===void 0||t.setMin(o),this)}randomizationFactor(o){var t;return o===void 0?this._randomizationFactor:(this._randomizationFactor=o,(t=this.backoff)===null||t===void 0||t.setJitter(o),this)}reconnectionDelayMax(o){var t;return o===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=o,(t=this.backoff)===null||t===void 0||t.setMax(o),this)}timeout(o){return arguments.length?(this._timeout=o,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(o){if(~this._readyState.indexOf("open"))return this;this.engine=new X(this.uri,this.opts);let t=this.engine,e=this;this._readyState="opening",this.skipReconnect=!1;let i=w(t,"open",function(){e.onopen(),o&&o()}),s=v=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",v),o?o(v):this.maybeReconnectOnOpen()},g=w(t,"error",s);if(this._timeout!==!1){let v=this._timeout,y=this.setTimeoutFn(()=>{i(),s(new Error("timeout")),t.close()},v);this.opts.autoUnref&&y.unref(),this.subs.push(()=>{this.clearTimeoutFn(y)})}return this.subs.push(i),this.subs.push(g),this}connect(o){return this.open(o)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let o=this.engine;this.subs.push(w(o,"ping",this.onping.bind(this)),w(o,"data",this.ondata.bind(this)),w(o,"error",this.onerror.bind(this)),w(o,"close",this.onclose.bind(this)),w(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(o){try{this.decoder.add(o)}catch(t){this.onclose("parse error",t)}}ondecoded(o){R(()=>{this.emitReserved("packet",o)},this.setTimeoutFn)}onerror(o){this.emitReserved("error",o)}socket(o,t){let e=this.nsps[o];return e?this._autoConnect&&!e.active&&e.connect():(e=new $(this,o,t),this.nsps[o]=e),e}_destroy(o){let t=Object.keys(this.nsps);for(let e of t)if(this.nsps[e].active)return;this._close()}_packet(o){let t=this.encoder.encode(o);for(let e=0;e<t.length;e++)this.engine.write(t[e],o.options)}cleanup(){this.subs.forEach(o=>o()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(o,t){var e;this.cleanup(),(e=this.engine)===null||e===void 0||e.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",o,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let o=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();this._reconnecting=!0;let e=this.setTimeoutFn(()=>{o.skipReconnect||(this.emitReserved("reconnect_attempt",o.backoff.attempts),!o.skipReconnect&&o.open(i=>{i?(o._reconnecting=!1,o.reconnect(),this.emitReserved("reconnect_error",i)):o.onreconnect()}))},t);this.opts.autoUnref&&e.unref(),this.subs.push(()=>{this.clearTimeoutFn(e)})}}onreconnect(){let o=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",o)}};var ue={};function pe(n,o){typeof n=="object"&&(o=n,n=void 0),o=o||{};let t=Xi(n,o.path||"/socket.io"),e=t.source,i=t.id,s=t.path,g=ue[i]&&s in ue[i].nsps,v=o.forceNew||o["force new connection"]||o.multiplex===!1||g,y;return v?y=new K(e,o):(ue[i]||(ue[i]=new K(e,o)),y=ue[i]),t.query&&!o.query&&(o.query=t.queryKey),y.socket(t.path,o)}Object.assign(pe,{Manager:K,Socket:$,io:pe,connect:pe});var Vn={production:!0,apiUrl:"/api",socketUrl:"",serverPort:3e3,clientPort:8100};var ql=(()=>{let o=class o{constructor(){this.API_URL=Vn.apiUrl,this.socket=null,this.gameSubject=new Q(null),this.playerSubject=new Q(null),this.connectionStatusSubject=new Q(!1),this.errorSubject=new Q(null),this.game$=this.gameSubject.asObservable(),this.player$=this.playerSubject.asObservable(),this.connectionStatus$=this.connectionStatusSubject.asObservable(),this.error$=this.errorSubject.asObservable(),this.initializeSocket()}initializeSocket(){this.socket=pe(Vn.socketUrl),this.socket.on("connect",()=>{console.log("Connected to server"),this.connectionStatusSubject.next(!0)}),this.socket.on("disconnect",()=>{console.log("Disconnected from server"),this.connectionStatusSubject.next(!1)}),this.socket.on("game-updated",e=>{console.log("Game updated:",e),this.gameSubject.next(e)}),this.socket.on("player-joined",e=>{console.log("Player joined:",e.player),this.gameSubject.next(e.game)}),this.socket.on("player-left",e=>{console.log("Player left:",e.player),this.gameSubject.next(e.game)}),this.socket.on("game-closed",e=>{console.log("Game closed:",e.room_code),this.clearGameState()})}joinGame(e,i){return P(this,null,function*(){try{let s=yield fetch(`${this.API_URL}/games/${e}/players`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i,connection_id:this.socket?.id})});if(!s.ok){let v=yield s.json();return this.errorSubject.next(v.error||"Failed to join game"),!1}let g=yield s.json();return this.gameSubject.next(g.game),this.playerSubject.next(g.player),this.socket?.emit("join-game",e),localStorage.setItem("jugshine_player_name",i),localStorage.setItem("jugshine_room_code",e),this.errorSubject.next(null),!0}catch(s){return console.error("Error joining game:",s),this.errorSubject.next("Network error. Please try again."),!1}})}leaveGame(){return P(this,null,function*(){let e=this.gameSubject.value,i=this.playerSubject.value;if(!(!e||!i)){try{yield fetch(`${this.API_URL}/games/${e.room_code}/players/${i.name}`,{method:"DELETE"})}catch(s){console.error("Error leaving game:",s)}this.socket?.emit("leave-game",e.room_code),this.clearGameState()}})}submitAction(e,i){return P(this,null,function*(){let s=this.gameSubject.value,g=this.playerSubject.value;if(!s||!g)return this.errorSubject.next("No active game or player"),!1;try{let v=yield fetch(`${this.API_URL}/players/${s.room_code}/${g.name}/actions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action_type:e,data:i})});if(!v.ok){let j=yield v.json();return this.errorSubject.next(j.error||"Failed to submit action"),!1}let y=yield v.json();return this.gameSubject.next(y.game),this.errorSubject.next(null),!0}catch(v){return console.error("Error submitting action:",v),this.errorSubject.next("Network error. Please try again."),!1}})}reconnectToGame(){return P(this,null,function*(){let e=localStorage.getItem("jugshine_player_name"),i=localStorage.getItem("jugshine_room_code");if(!e||!i)return!1;try{let s=yield fetch(`${this.API_URL}/games/${i}`);if(!s.ok)return s.status===404?(console.log("Previous game no longer exists, clearing saved data"),this.errorSubject.next("Previous game no longer exists")):(console.error("Error checking game status:",s.status,s.statusText),this.errorSubject.next("Unable to reconnect to previous game")),this.clearStoredGameInfo(),!1;let g=yield s.json(),v=g.players.find(y=>y.name===e);return v?(this.gameSubject.next(g),this.playerSubject.next(v),this.socket?.emit("join-game",i),yield fetch(`${this.API_URL}/players/${i}/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({connection_id:this.socket?.id})}),console.log("Successfully reconnected to game:",i),this.errorSubject.next(null),!0):(console.log("Player no longer in game, clearing saved data"),this.errorSubject.next("You are no longer in the previous game"),this.clearStoredGameInfo(),!1)}catch(s){return console.error("Error reconnecting to game:",s),this.errorSubject.next("Network error while reconnecting"),this.clearStoredGameInfo(),!1}})}clearGameState(){this.gameSubject.next(null),this.playerSubject.next(null),this.clearStoredGameInfo()}clearStoredGameInfo(){localStorage.removeItem("jugshine_player_name"),localStorage.removeItem("jugshine_room_code")}clearError(){this.errorSubject.next(null)}getCurrentGame(){return this.gameSubject.value}getCurrentPlayer(){return this.playerSubject.value}isPlayerLeader(){return this.playerSubject.value?.is_leader||!1}isPlayerJudge(){let e=this.gameSubject.value,i=this.playerSubject.value;return e?.current_judge===i?.name}getPlayerResponses(){let e=this.gameSubject.value,i=this.playerSubject.value;return!e||!i||!e.game_data.player_responses?[]:e.game_data.player_responses[i.name]||[]}};o.\u0275fac=function(i){return new(i||o)},o.\u0275prov=me({token:o,factory:o.\u0275fac,providedIn:"root"});let n=o;return n})();export{So as a,Mo as b,Lo as c,Vo as d,qo as e,Ho as f,Zo as g,Wo as h,Go as i,Xo as j,Ko as k,or as l,rr as m,lr as n,pr as o,yr as p,Ir as q,_r as r,Kr as s,ns as t,rs as u,Za as v,ql as w};
