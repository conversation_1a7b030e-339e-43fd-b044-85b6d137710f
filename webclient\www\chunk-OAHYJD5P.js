import{c}from"./chunk-7IRWVP2E.js";var y=(e,t=0)=>new Promise(a=>{l(e,t,a)}),l=(e,t=0,a)=>{let n,i,r={passive:!0},d=500,u=()=>{n&&n()},s=o=>{(o===void 0||e===o.target)&&(u(),a(o))};return e&&(e.addEventListener("webkitTransitionEnd",s,r),e.addEventListener("transitionend",s,r),i=setTimeout(s,t+d),n=()=>{i!==void 0&&(clearTimeout(i),i=void 0),e.removeEventListener("webkitTransitionEnd",s,r),e.removeEventListener("transitionend",s,r)}),u},E=(e,t)=>{e.componentOnReady?e.componentOnReady().then(a=>t(a)):b(()=>t(e))},g=e=>e.componentOnReady!==void 0,f=(e,t=[])=>{let a={};return t.forEach(n=>{e.hasAttribute(n)&&(e.getAttribute(n)!==null&&(a[n]=e.getAttribute(n)),e.removeAttribute(n))}),a},m=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],v=(e,t)=>f(e,m),w=(e,t,a,n)=>e.addEventListener(t,a,n),T=(e,t,a,n)=>e.removeEventListener(t,a,n),A=(e,t=e)=>e.shadowRoot||t,b=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),x=e=>!!e.shadowRoot&&!!e.attachShadow,L=e=>{if(e.focus(),e.classList.contains("ion-focusable")){let t=e.closest("ion-app");t&&t.setFocus([e])}},_=(e,t,a,n,i)=>{{let r=t.querySelector("input.aux-input");r||(r=t.ownerDocument.createElement("input"),r.type="hidden",r.classList.add("aux-input"),t.appendChild(r)),r.disabled=i,r.name=a,r.value=n||""}},R=(e,t,a)=>Math.max(e,Math.min(t,a)),O=(e,t)=>{if(!e){let a="ASSERT: "+t;c(a);debugger;throw new Error(a)}},k=e=>{if(e){let t=e.changedTouches;if(t&&t.length>0){let a=t[0];return{x:a.clientX,y:a.clientY}}if(e.pageX!==void 0)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}},q=e=>{let t=document.dir==="rtl";switch(e){case"start":return t;case"end":return!t;default:throw new Error(`"${e}" is not a valid value for [side]. Use "start" or "end" instead.`)}},S=(e,t)=>{let a=e._original||e;return{_original:e,emit:h(a.emit.bind(a),t)}},h=(e,t=0)=>{let a;return(...n)=>{clearTimeout(a),a=setTimeout(e,t,...n)}},F=(e,t)=>{if(e??(e={}),t??(t={}),e===t)return!0;let a=Object.keys(e);if(a.length!==Object.keys(t).length)return!1;for(let n of a)if(!(n in t)||e[n]!==t[n])return!1;return!0},I=e=>typeof e=="number"&&!isNaN(e)&&isFinite(e);export{y as a,E as b,g as c,f as d,v as e,w as f,T as g,A as h,b as i,x as j,L as k,_ as l,R as m,O as n,k as o,q as p,S as q,h as r,F as s,I as t};
