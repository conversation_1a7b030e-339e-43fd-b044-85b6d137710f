@echo off
title Install MongoDB for Jugshine
color 0E

echo.
echo ========================================
echo    INSTALL MONGODB FOR JUGSHINE
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo [WARNING] This script should be run as Administrator for best results.
    echo [INFO] Right-click and select "Run as administrator"
    echo.
    echo [INFO] Continuing anyway... some features may not work.
    echo.
    pause
)

:: Check if MongoDB is already installed
echo [INFO] Checking for existing MongoDB installation...

:: Check PATH first
where mongod >nul 2>&1
if not errorlevel 1 (
    echo [INFO] MongoDB is already available in PATH
    mongod --version
    echo.
    echo [SUCCESS] MongoDB is already installed and ready to use!
    echo [INFO] You can now run start-server-mongodb.bat
    echo.
    pause
    exit /b 0
)

:: Check common installation directories
for /d %%d in ("C:\Program Files\MongoDB\Server\*") do (
    if exist "%%d\bin\mongod.exe" (
        echo [INFO] MongoDB found at: %%d\bin\mongod.exe
        echo [INFO] MongoDB is installed but not in PATH
        echo.
        echo [INFO] You can either:
        echo   1. Add %%d\bin to your PATH environment variable
        echo   2. Or just run start-server-mongodb.bat (it will find MongoDB automatically)
        echo.
        pause
        exit /b 0
    )
)

:: Check if Chocolatey is available
echo [INFO] MongoDB not found. Checking installation options...
where choco >nul 2>&1
if not errorlevel 1 (
    echo [INFO] Chocolatey found! Installing MongoDB via Chocolatey...
    echo [INFO] This may take a few minutes...
    choco install mongodb -y
    if not errorlevel 1 (
        echo [SUCCESS] MongoDB installed successfully via Chocolatey!
        echo [INFO] You can now run start-server-mongodb.bat
        echo.
        pause
        exit /b 0
    ) else (
        echo [WARNING] Chocolatey installation failed, trying manual method...
    )
)

:: Check if Scoop is available
where scoop >nul 2>&1
if not errorlevel 1 (
    echo [INFO] Scoop found! Installing MongoDB via Scoop...
    scoop install mongodb
    if not errorlevel 1 (
        echo [SUCCESS] MongoDB installed successfully via Scoop!
        echo [INFO] You can now run start-server-mongodb.bat
        echo.
        pause
        exit /b 0
    ) else (
        echo [WARNING] Scoop installation failed, trying manual method...
    )
)

:: Manual installation instructions
echo [INFO] No package managers found. Manual installation required.
echo.
echo ========================================
echo    MANUAL MONGODB INSTALLATION
echo ========================================
echo.
echo Please follow these steps:
echo.
echo 1. Download MongoDB Community Server:
echo    https://www.mongodb.com/try/download/community
echo.
echo 2. Choose:
echo    - Version: Latest (7.0 or newer)
echo    - Platform: Windows
echo    - Package: MSI
echo.
echo 3. Run the installer with these options:
echo    - Install Type: Complete
echo    - Service Configuration: Install as Windows Service
echo    - Service Name: MongoDB
echo.
echo 4. After installation, MongoDB will be available as a Windows service
echo.
echo 5. Then run: start-server-mongodb.bat
echo.
echo Alternative: Install via package manager
echo   - Chocolatey: choco install mongodb
echo   - Scoop: scoop install mongodb
echo.
echo [INFO] Press any key to open the MongoDB download page...
pause >nul

:: Open MongoDB download page
start https://www.mongodb.com/try/download/community

echo.
echo [INFO] Download page opened in your browser.
echo [INFO] After installing MongoDB, run start-server-mongodb.bat
echo.
pause
