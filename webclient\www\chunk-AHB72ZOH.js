import{f as r,g as i}from"./chunk-NLLZB5D2.js";import{b as s}from"./chunk-OAHYJD5P.js";import{j as a,k as c}from"./chunk-7IRWVP2E.js";import{f as n}from"./chunk-S5EYVFES.js";var h=()=>{let e=window;e.addEventListener("statusTap",()=>{a(()=>{let m=e.innerWidth,d=e.innerHeight,o=document.elementFromPoint(m/2,d/2);if(!o)return;let t=r(o);t&&new Promise(f=>s(t,f)).then(()=>{c(()=>n(null,null,function*(){t.style.setProperty("--overflow","hidden"),yield i(t,300),t.style.removeProperty("--overflow")}))})})})};export{h as startStatusTap};
