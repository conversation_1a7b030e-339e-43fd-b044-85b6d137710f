import{A as Ve,Aa as xe,B as ht,Ba as ke,<PERSON>a as je,<PERSON> as re,E as Ge,<PERSON>a as Se,F as qe,Fa as he,G as gt,Ga as _t,H as vt,Ha as Ht,I as It,Ia as se,N as Xe,O as Ke,Q as bt,R as Ct,S as yt,Xa as Je,Ya as Te,Za as Zt,ab as Wt,b as Ze,ba as Dt,bb as Yt,ca as wt,e as dt,g as ee,h as pt,ha as Et,i as ve,ia as xt,ja as kt,ka as jt,kb as et,l as Ie,la as St,lb as tt,m as mt,ma as Tt,na as Mt,oa as te,p as We,pa as q,q as Ye,qa as $,ra as Ot,s as Z,sa as De,t as ut,ta as oe,u as me,ua as ie,va as we,wa as Lt,x as ft,xa as Nt,ya as $t,za as Ee}from"./chunk-FXSFTU3P.js";import{a as ae,c as Vt,d as Me,g as Gt,h as nt,i as qt}from"./chunk-C72CHBU4.js";import"./chunk-EX6SW4ET.js";import"./chunk-BIG7K77F.js";import{a as p,g as zt,h as be,i as Ce}from"./chunk-SM5Y6Q5Y.js";import{a as At,b as W,d as Ue,e as P,f as K,i as k,j as Y}from"./chunk-PFDCGU6V.js";import{a as Qe}from"./chunk-B3E62VDJ.js";import{c as ye}from"./chunk-M2X7KQLB.js";import{a as ot}from"./chunk-2YSZFPCQ.js";import{a as Xt}from"./chunk-57YRIO75.js";import{b as ne,c as Rt,d as Ft,f as z,g as G,i as fe,k as Bt}from"./chunk-XTVTS2NW.js";import{a as N,e as U,f as Pt}from"./chunk-C5RQ2IC2.js";import{a as ue}from"./chunk-42C7ZIID.js";import{a as _e,b as He,f as y}from"./chunk-S5EYVFES.js";var ce=()=>{let o;return{lock:()=>y(null,null,function*(){let t=o,n;return o=new Promise(i=>n=i),t!==void 0&&(yield t),n})}};var wn=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",En=Y(class extends W{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=k(this,"ionNavWillLoad",7),this.ionNavWillChange=k(this,"ionNavWillChange",3),this.ionNavDidChange=k(this,"ionNavDidChange",3),this.lockController=ce(),this.gestureOrAnimationInProgress=!1,this.mode=$(this),this.animated=!0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}connectedCallback(){return y(this,null,function*(){let e=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(yield import("./chunk-HESXRD7D.js")).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>e(),t=>{var n;return(n=this.ani)===null||n===void 0?void 0:n.progressStep(t)},(t,n,i)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(t)},{oneTimeCallback:!0});let r=t?-.001:.001;t?r+=te([0,0],[.32,.72],[0,1],[1,1],n)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),r+=te([0,0],[1,0],[.68,.28],[1,1],n)[0]),this.ani.progressEnd(t?1:0,r,i)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()})}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(e,t,n){return y(this,null,function*(){let i=yield this.lockController.lock(),r=!1;try{r=yield this.transition(e,t,n)}catch(s){Pt("[ion-router-outlet] - Exception in commit:",s)}return i(),r})}setRouteId(e,t,n,i){return y(this,null,function*(){return{changed:yield this.setRoot(e,t,{duration:n==="root"?0:void 0,direction:n==="back"?"back":"forward",animationBuilder:i}),element:this.activeEl}})}getRouteId(){return y(this,null,function*(){let e=this.activeEl;return e?{id:e.tagName,element:e,params:this.activeParams}:void 0})}setRoot(e,t,n){return y(this,null,function*(){if(this.activeComponent===e&&Bt(t,this.activeParams))return!1;let i=this.activeEl,r=yield oe(this.delegate,this.el,e,["ion-page","ion-page-invisible"],t);return this.activeComponent=e,this.activeEl=r,this.activeParams=t,yield this.commit(r,i,n),yield ie(this.delegate,i),!0})}transition(i,r){return y(this,arguments,function*(e,t,n={}){if(t===e)return!1;this.ionNavWillChange.emit();let{el:s,mode:a}=this,c=this.animated&&N.getBoolean("animated",!0),l=n.animationBuilder||this.animation||N.get("navAnimation");return yield zt(Object.assign(Object.assign({mode:a,animated:c,enteringEl:e,leavingEl:t,baseEl:s,deepWait:ne(s),progressCallback:n.progressAnimation?m=>{m!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,m.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),m.progressEnd(0,0,0)):this.ani=m}:void 0},n),{animationBuilder:l})),this.ionNavDidChange.emit(),!0})}render(){return P("slot",{key:"84b50f1155b0d780dff802ee13223287259fd525"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return wn}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16,"swipe-handler"],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);function xn(){if(typeof customElements>"u")return;["ion-router-outlet"].forEach(e=>{switch(e){case"ion-router-outlet":customElements.get(e)||customElements.define(e,En);break}})}var Kt=xn;var kn=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",jn=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",Sn=Y(class extends W{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionBackdropTap=k(this,"ionBackdropTap",7),this.visible=!0,this.tappable=!0,this.stopPropagation=!0}onMouseDown(e){this.emitTap(e)}emitTap(e){this.stopPropagation&&(e.preventDefault(),e.stopPropagation()),this.tappable&&this.ionBackdropTap.emit()}render(){let e=$(this);return P(K,{key:"7abaf2c310aa399607451b14063265e8a5846938","aria-hidden":"true",class:{[e]:!0,"backdrop-hide":!this.visible,"backdrop-no-tappable":!this.tappable}})}static get style(){return{ios:kn,md:jn}}},[33,"ion-backdrop",{visible:[4],tappable:[4],stopPropagation:[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]);function Ae(){if(typeof customElements>"u")return;["ion-backdrop"].forEach(e=>{switch(e){case"ion-backdrop":customElements.get(e)||customElements.define(e,Sn);break}})}var ge=function(o){return o.Dark="DARK",o.Light="LIGHT",o.Default="DEFAULT",o}(ge||{}),at={getEngine(){let o=Xt();if(o?.isPluginAvailable("StatusBar"))return o.Plugins.StatusBar},setStyle(o){let e=this.getEngine();e&&e.setStyle(o)},getStyle:function(){return y(this,null,function*(){let o=this.getEngine();if(!o)return ge.Default;let{style:e}=yield o.getInfo();return e})}},it=(o,e)=>{if(e===1)return 0;let t=1/(1-e),n=-(e*t);return o*t+n},en=()=>{!ue||ue.innerWidth>=768||at.setStyle({style:ge.Dark})},rt=(o=ge.Default)=>{!ue||ue.innerWidth>=768||at.setStyle({style:o})},tn=(o,e)=>y(null,null,function*(){typeof o.canDismiss!="function"||!(yield o.canDismiss(void 0,he))||(e.isRunning()?e.onFinish(()=>{o.dismiss(void 0,"handler")},{oneTimeCallback:!0}):o.dismiss(void 0,"handler"))}),st=o=>.00255275*2.71828**(-14.9619*o)-1.00255*2.71828**(-.0380968*o)+1,Pe={MIN_PRESENTING_SCALE:.915},Tn=(o,e,t,n)=>{let r=o.offsetHeight,s=!1,a=!1,c=null,l=null,m=.2,h=!0,v=0,u=()=>c&&ae(c)?c.scrollY:!0,T=ye({el:o,gestureName:"modalSwipeToClose",gesturePriority:_t,direction:"y",threshold:10,canStart:D=>{let b=D.event.target;return b===null||!b.closest?!0:(c=Me(b),c?(ae(c)?l=z(c).querySelector(".inner-scroll"):l=c,!!!c.querySelector("ion-refresher")&&l.scrollTop===0):b.closest("ion-footer")===null)},onStart:D=>{let{deltaY:b}=D;h=u(),a=o.canDismiss!==void 0&&o.canDismiss!==!0,b>0&&c&&nt(c),e.progressStart(!0,s?1:0)},onMove:D=>{let{deltaY:b}=D;b>0&&c&&nt(c);let S=D.deltaY/r,f=S>=0&&a,R=f?m:.9999,B=f?st(S/R):S,F=fe(1e-4,B,R);e.progressStep(F),F>=.5&&v<.5?rt(t):F<.5&&v>=.5&&en(),v=F},onEnd:D=>{let b=D.velocityY,S=D.deltaY/r,f=S>=0&&a,R=f?m:.9999,B=f?st(S/R):S,F=fe(1e-4,B,R),L=(D.deltaY+b*1e3)/r,M=!f&&L>=.5,A=M?-.001:.001;M?(e.easing("cubic-bezier(0.32, 0.72, 0, 1)"),A+=te([0,0],[.32,.72],[0,1],[1,1],F)[0]):(e.easing("cubic-bezier(1, 0, 0.68, 0.28)"),A+=te([0,0],[1,0],[.68,.28],[1,1],F)[0]);let H=Ut(M?S*r:(1-F)*r,b);s=M,T.enable(!1),c&&qt(c,h),e.onFinish(()=>{M||T.enable(!0)}).progressEnd(M?1:0,A,H),f&&F>R/4?tn(o,e):M&&n()}});return T},Ut=(o,e)=>fe(400,o/Math.abs(e*1.1),500),nn=o=>{let{currentBreakpoint:e,backdropBreakpoint:t,expandToScroll:n}=o,i=t===void 0||t<e,r=i?`calc(var(--backdrop-opacity) * ${e})`:"0",s=p("backdropAnimation").fromTo("opacity",0,r);i&&s.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let a=p("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-e*100}%)`}]),c=n?void 0:p("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-e)*100}%`},{offset:1,opacity:1,maxHeight:`${e*100}%`}]);return{wrapperAnimation:a,backdropAnimation:s,contentAnimation:c}},on=o=>{let{currentBreakpoint:e,backdropBreakpoint:t}=o,n=`calc(var(--backdrop-opacity) * ${it(e,t)})`,i=[{offset:0,opacity:n},{offset:1,opacity:0}],r=[{offset:0,opacity:n},{offset:t,opacity:0},{offset:1,opacity:0}],s=p("backdropAnimation").keyframes(t!==0?r:i);return{wrapperAnimation:p("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-e*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:s}},Mn=()=>{let o=p().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=p().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:o,wrapperAnimation:e,contentAnimation:void 0}},Qt=(o,e)=>{let{presentingEl:t,currentBreakpoint:n,expandToScroll:i}=e,r=z(o),{wrapperAnimation:s,backdropAnimation:a,contentAnimation:c}=n!==void 0?nn(e):Mn();a.addElement(r.querySelector("ion-backdrop")),s.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!i&&c?.addElement(o.querySelector(".ion-page"));let l=p("entering-base").addElement(o).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([s]);if(c&&l.addAnimation(c),t){let m=window.innerWidth<768,h=t.tagName==="ION-MODAL"&&t.presentingElement!==void 0,v=z(t),u=p().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),I=document.body;if(m){let j=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",d=h?"-10px":j,x=Pe.MIN_PRESENTING_SCALE,T=`translateY(${d}) scale(${x})`;u.afterStyles({transform:T}).beforeAddWrite(()=>I.style.setProperty("background-color","black")).addElement(t).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:T,borderRadius:"10px 10px 0 0"}]),l.addAnimation(u)}else if(l.addAnimation(a),!h)s.fromTo("opacity","0","1");else{let d=`translateY(-10px) scale(${h?Pe.MIN_PRESENTING_SCALE:1})`;u.afterStyles({transform:d}).addElement(v.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:d}]);let x=p().afterStyles({transform:d}).addElement(v.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:d}]);l.addAnimation([u,x])}}else l.addAnimation(a);return l},An=()=>{let o=p().fromTo("opacity","var(--backdrop-opacity)",0),e=p().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:o,wrapperAnimation:e}},Jt=(o,e,t=500)=>{let{presentingEl:n,currentBreakpoint:i}=e,r=z(o),{wrapperAnimation:s,backdropAnimation:a}=i!==void 0?on(e):An();a.addElement(r.querySelector("ion-backdrop")),s.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let c=p("leaving-base").addElement(o).easing("cubic-bezier(0.32,0.72,0,1)").duration(t).addAnimation(s);if(n){let l=window.innerWidth<768,m=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,h=z(n),v=p().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(I=>{if(I!==1)return;n.style.setProperty("overflow",""),Array.from(u.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(d=>d.presentingElement!==void 0).length<=1&&u.style.setProperty("background-color","")}),u=document.body;if(l){let I=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",j=m?"-10px":I,d=Pe.MIN_PRESENTING_SCALE,x=`translateY(${j}) scale(${d})`;v.addElement(n).keyframes([{offset:0,filter:"contrast(0.85)",transform:x,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),c.addAnimation(v)}else if(c.addAnimation(a),!m)s.fromTo("opacity","1","0");else{let j=`translateY(-10px) scale(${m?Pe.MIN_PRESENTING_SCALE:1})`;v.addElement(h.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:j},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let d=p().addElement(h.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:j},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);c.addAnimation([v,d])}}else c.addAnimation(a);return c},Pn=()=>{let o=p().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=p().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:o,wrapperAnimation:e,contentAnimation:void 0}},Rn=(o,e)=>{let{currentBreakpoint:t,expandToScroll:n}=e,i=z(o),{wrapperAnimation:r,backdropAnimation:s,contentAnimation:a}=t!==void 0?nn(e):Pn();s.addElement(i.querySelector("ion-backdrop")),r.addElement(i.querySelector(".modal-wrapper")),!n&&a?.addElement(o.querySelector(".ion-page"));let c=p().addElement(o).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([s,r]);return a&&c.addAnimation(a),c},Fn=()=>{let o=p().fromTo("opacity","var(--backdrop-opacity)",0),e=p().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:o,wrapperAnimation:e}},Bn=(o,e)=>{let{currentBreakpoint:t}=e,n=z(o),{wrapperAnimation:i,backdropAnimation:r}=t!==void 0?on(e):Fn();return r.addElement(n.querySelector("ion-backdrop")),i.addElement(n.querySelector(".modal-wrapper")),p().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([r,i])},zn=(o,e,t,n,i,r,s=[],a,c,l,m)=>{let h=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],v=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-i,opacity:0},{offset:1,opacity:0}],u={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:i!==0?v:h,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},I=o.querySelector("ion-content"),j=t.clientHeight,d=n,x=0,T=!1,D=null,b=null,S=null,f=null,R=.95,B=s[s.length-1],F=s[0],L=r.childAnimations.find(w=>w.id==="wrapperAnimation"),M=r.childAnimations.find(w=>w.id==="backdropAnimation"),A=r.childAnimations.find(w=>w.id==="contentAnimation"),H=()=>{o.style.setProperty("pointer-events","auto"),e.style.setProperty("pointer-events","auto"),o.classList.remove(se)},Q=()=>{o.style.setProperty("pointer-events","none"),e.style.setProperty("pointer-events","none"),o.classList.add(se)},X=w=>{if(!b&&(b=Array.from(o.querySelectorAll("ion-footer")),!b.length))return;let g=o.querySelector(".ion-page");if(f=w,w==="stationary")b.forEach(E=>{E.classList.remove("modal-footer-moving"),E.style.removeProperty("position"),E.style.removeProperty("width"),E.style.removeProperty("height"),E.style.removeProperty("top"),E.style.removeProperty("left"),g?.style.removeProperty("padding-bottom"),g?.appendChild(E)});else{let E=0;b.forEach((C,V)=>{let _=C.getBoundingClientRect(),O=document.body.getBoundingClientRect();E+=C.clientHeight;let de=_.top-O.top,pe=_.left-O.left;if(C.style.setProperty("--pinned-width",`${C.clientWidth}px`),C.style.setProperty("--pinned-height",`${C.clientHeight}px`),C.style.setProperty("--pinned-top",`${de}px`),C.style.setProperty("--pinned-left",`${pe}px`),V===0){S=de;let $e=o.querySelector("ion-header");$e&&(S-=$e.clientHeight)}}),b.forEach(C=>{g?.style.setProperty("padding-bottom",`${E}px`),C.classList.add("modal-footer-moving"),C.style.setProperty("position","absolute"),C.style.setProperty("width","var(--pinned-width)"),C.style.setProperty("height","var(--pinned-height)"),C.style.setProperty("top","var(--pinned-top)"),C.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(C)})}};L&&M&&(L.keyframes([...u.WRAPPER_KEYFRAMES]),M.keyframes([...u.BACKDROP_KEYFRAMES]),A?.keyframes([...u.CONTENT_KEYFRAMES]),r.progressStart(!0,1-d),d>i?H():Q()),I&&d!==B&&a&&(I.scrollY=!1);let ze=w=>{let g=Me(w.event.target);if(d=c(),!a&&g)return(ae(g)?z(g).querySelector(".inner-scroll"):g).scrollTop===0;if(d===1&&g){let E=ae(g)?z(g).querySelector(".inner-scroll"):g;return!!!g.querySelector("ion-refresher")&&E.scrollTop===0}return!0},Oe=w=>{if(T=o.canDismiss!==void 0&&o.canDismiss!==!0&&F===0,!a){let g=Me(w.event.target);D=g&&ae(g)?z(g).querySelector(".inner-scroll"):g}a||X("moving"),w.deltaY>0&&I&&(I.scrollY=!1),G(()=>{o.focus()}),r.progressStart(!0,1-d)},Le=w=>{if(!a&&S!==null&&f!==null&&(w.currentY>=S&&f==="moving"?X("stationary"):w.currentY<S&&f==="stationary"&&X("moving")),!a&&w.deltaY<=0&&D)return;w.deltaY>0&&I&&(I.scrollY=!1);let g=1-d,E=s.length>1?1-s[1]:void 0,C=g+w.deltaY/j,V=E!==void 0&&C>=E&&T,_=V?R:.9999,O=V&&E!==void 0?E+st((C-E)/(_-E)):C;x=fe(1e-4,O,_),r.progressStep(x)},Ne=w=>{if(!a&&w.deltaY<=0&&D&&D.scrollTop>0){X("stationary");return}let g=w.velocityY,E=(w.deltaY+g*350)/j,C=d-E,V=s.reduce((_,O)=>Math.abs(O-C)<Math.abs(_-C)?O:_);le({breakpoint:V,breakpointOffset:x,canDismiss:T,animated:!0})},le=w=>{let{breakpoint:g,canDismiss:E,breakpointOffset:C,animated:V}=w,_=E&&g===0,O=_?d:g,de=O!==0;return d=0,L&&M&&(L.keyframes([{offset:0,transform:`translateY(${C*100}%)`},{offset:1,transform:`translateY(${(1-O)*100}%)`}]),M.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${it(1-C,i)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${it(O,i)})`}]),A&&A.keyframes([{offset:0,maxHeight:`${(1-C)*100}%`},{offset:1,maxHeight:`${O*100}%`}]),r.progressStep(0)),J.enable(!1),_?tn(o,r):de||l(),I&&(O===s[s.length-1]||!a)&&(I.scrollY=!0),!a&&O===0&&X("stationary"),new Promise(pe=>{r.onFinish(()=>{de?(a||X("stationary"),L&&M?G(()=>{L.keyframes([...u.WRAPPER_KEYFRAMES]),M.keyframes([...u.BACKDROP_KEYFRAMES]),A?.keyframes([...u.CONTENT_KEYFRAMES]),r.progressStart(!0,1-O),d=O,m(d),d>i?H():Q(),J.enable(!0),pe()}):(J.enable(!0),pe())):pe()},{oneTimeCallback:!0}).progressEnd(1,0,V?500:0)})},J=ye({el:t,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:ze,onStart:Oe,onMove:Le,onEnd:Ne});return{gesture:J,moveSheetToBreakpoint:le}},On=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',Ln=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',rn=Y(class extends W{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=k(this,"ionModalDidPresent",7),this.willPresent=k(this,"ionModalWillPresent",7),this.willDismiss=k(this,"ionModalWillDismiss",7),this.didDismiss=k(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=k(this,"ionBreakpointDidChange",7),this.didPresentShorthand=k(this,"didPresent",7),this.willPresentShorthand=k(this,"willPresent",7),this.willDismissShorthand=k(this,"willDismiss",7),this.didDismissShorthand=k(this,"didDismiss",7),this.ionMount=k(this,"ionMount",7),this.lockController=ce(),this.triggerController=Ht(),this.coreDelegate=we(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:e,handleBehavior:t}=this;t!=="cycle"||e!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:e}=this;e===void 0&&this.dismiss(void 0,Se)},this.onLifecycle=e=>{let t=this.usersElement,n=Nn[e.type];if(t&&n){let i=new CustomEvent(n,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}}}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}triggerChanged(){let{trigger:e,el:t,triggerController:n}=this;e&&n.addClickListener(t,e)}breakpointsChanged(e){e!==void 0&&(this.sortedBreakpoints=e.sort((t,n)=>t-n))}connectedCallback(){let{el:e}=this;Ee(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var e;let{breakpoints:t,initialBreakpoint:n,el:i,htmlAttributes:r}=this,s=this.isSheetModal=t!==void 0&&n!==void 0,a=["aria-label","role"];this.inheritedAttributes=Rt(i,a),r!==void 0&&a.forEach(c=>{r[c]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[c]:r[c]}),delete r[c])}),s&&(this.currentBreakpoint=this.initialBreakpoint),t!==void 0&&n!==void 0&&!t.includes(n)&&U("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((e=this.htmlAttributes)===null||e===void 0)&&e.id||xe(this.el)}componentDidLoad(){this.isOpen===!0&&G(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,n=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate;return{inline:n,delegate:i}}checkCanDismiss(e,t){return y(this,null,function*(){let{canDismiss:n}=this;return typeof n=="function"?n(e,t):n})}present(){return y(this,null,function*(){let e=yield this.lockController.lock();if(this.presented){e();return}let{presentingElement:t,el:n}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield oe(r,n,this.component,["ion-page"],this.componentProps,i),ne(n)?yield Ce(this.usersElement):this.keepContentsMounted||(yield be()),Ue(()=>this.el.classList.add("show-modal"));let s=t!==void 0;s&&$(this)==="ios"&&(this.statusBarStyle=yield at.getStyle(),en()),yield ke(this,"modalEnter",Qt,Rn,{presentingEl:t,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),G(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(ot,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():s&&this.initSwipeToClose(),e()})}initSwipeToClose(){var e;if($(this)!=="ios")return;let{el:t}=this,n=this.leaveAnimation||N.get("modalLeave",Jt),i=this.animation=n(t,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Vt(t)){Gt(t);return}let s=(e=this.statusBarStyle)!==null&&e!==void 0?e:ge.Default;this.gesture=Tn(t,i,s,()=>{this.gestureAnimationDismissing=!0,rt(this.statusBarStyle),this.animation.onFinish(()=>y(this,null,function*(){yield this.dismiss(void 0,he),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:n}=this;if(!e||t===void 0)return;let i=this.enterAnimation||N.get("modalEnter",Qt),r=this.animation=i(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:this.expandToScroll});r.progressStart(!0,1);let{gesture:s,moveSheetToBreakpoint:a}=zn(this.el,this.backdropEl,e,t,n,r,this.sortedBreakpoints,this.expandToScroll,()=>{var c;return(c=this.currentBreakpoint)!==null&&c!==void 0?c:0},()=>this.sheetOnDismiss(),c=>{this.currentBreakpoint!==c&&(this.currentBreakpoint=c,this.ionBreakpointDidChange.emit({breakpoint:c}))});this.gesture=s,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>y(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,he),this.gestureAnimationDismissing=!1}))}dismiss(e,t){return y(this,null,function*(){var n;if(this.gestureAnimationDismissing&&t!==he)return!1;let i=yield this.lockController.lock();if(t!=="handler"&&!(yield this.checkCanDismiss(e,t)))return i(),!1;let{presentingElement:r}=this;r!==void 0&&$(this)==="ios"&&rt(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(ot,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield je(this,e,t,"modalLeave",Jt,Bn,{presentingEl:r,currentBreakpoint:(n=this.currentBreakpoint)!==null&&n!==void 0?n:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:c}=this.getDelegate();yield ie(c,this.usersElement),Ue(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy()}return this.currentBreakpoint=void 0,this.animation=void 0,i(),a})}onDidDismiss(){return re(this.el,"ionModalDidDismiss")}onWillDismiss(){return re(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){return y(this,null,function*(){if(!this.isSheetModal){U("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(e)){U(`[ion-modal] - Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:t,moveSheetToBreakpoint:n,canDismiss:i,breakpoints:r,animated:s}=this;t!==e&&n&&(this.sheetTransition=n({breakpoint:e,breakpointOffset:1-t,canDismiss:i!==void 0&&i!==!0&&r[0]===0,animated:s}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return y(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return y(this,null,function*(){let{breakpoints:e,currentBreakpoint:t}=this;if(!e||t==null)return!1;let n=e.filter(a=>a!==0),r=(n.indexOf(t)+1)%n.length,s=n[r];return yield this.setCurrentBreakpoint(s),!0})}render(){let{handle:e,isSheetModal:t,presentingElement:n,htmlAttributes:i,handleBehavior:r,inheritedAttributes:s,focusTrap:a,expandToScroll:c}=this,l=e!==!1&&t,m=$(this),h=n!==void 0&&m==="ios",v=r==="cycle";return P(K,Object.assign({key:"0bcbdcfcd7d890eb599da3f97f21c317d34f8e0e","no-router":!0,tabindex:"-1"},i,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[m]:!0,"modal-default":!h&&!t,"modal-card":h,"modal-sheet":t,"modal-no-expand-scroll":t&&!c,"overlay-hidden":!0,[se]:a===!1},De(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle}),P("ion-backdrop",{key:"d72159e73daa5af7349aa9e8f695aa435eb43069",ref:u=>this.backdropEl=u,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),m==="ios"&&P("div",{key:"fd2d9b13676ae72473881649a397b6eacde03a03",class:"modal-shadow"}),P("div",Object.assign({key:"908eccb1ad982dcde2dbcff0cbb18b6e60f8ba74",role:"dialog"},s,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:u=>this.wrapperEl=u}),l&&P("button",{key:"332dc0b40363a77c7be62331d9f26def91c790e9",class:"modal-handle",tabIndex:v?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:v?this.onHandleClick:void 0,part:"handle"}),P("slot",{key:"c32698350193c450327e97049daf8b8d1fda0d0e"})))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:On,md:Ln}}},[33,"ion-modal",{hasController:[4,"has-controller"],overlayIndex:[2,"overlay-index"],delegate:[16],keyboardClose:[4,"keyboard-close"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],breakpoints:[16],expandToScroll:[4,"expand-to-scroll"],initialBreakpoint:[2,"initial-breakpoint"],backdropBreakpoint:[2,"backdrop-breakpoint"],handle:[4],handleBehavior:[1,"handle-behavior"],component:[1],componentProps:[16,"component-props"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],animated:[4],presentingElement:[16,"presenting-element"],htmlAttributes:[16,"html-attributes"],isOpen:[4,"is-open"],trigger:[1],keepContentsMounted:[4,"keep-contents-mounted"],focusTrap:[4,"focus-trap"],canDismiss:[4,"can-dismiss"],presented:[32],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64],setCurrentBreakpoint:[64],getCurrentBreakpoint:[64]},void 0,{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]),Nn={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};function sn(){if(typeof customElements>"u")return;["ion-modal","ion-backdrop"].forEach(e=>{switch(e){case"ion-modal":customElements.get(e)||customElements.define(e,rn);break;case"ion-backdrop":customElements.get(e)||Ae();break}})}var an=sn;var $n=o=>{if(!o)return{arrowWidth:0,arrowHeight:0};let{width:e,height:t}=o.getBoundingClientRect();return{arrowWidth:e,arrowHeight:t}},ln=(o,e,t)=>{let n=e.getBoundingClientRect(),i=n.height,r=n.width;return o==="cover"&&t&&(r=t.getBoundingClientRect().width),{contentWidth:r,contentHeight:i}},_n=(o,e,t,n)=>{let i=[],s=z(n).querySelector(".popover-content");switch(e){case"hover":i=[{eventName:"mouseenter",callback:a=>{document.elementFromPoint(a.clientX,a.clientY)!==o&&t.dismiss(void 0,void 0,!1)}}];break;case"context-menu":case"click":default:i=[{eventName:"click",callback:a=>{if(a.target.closest("[data-ion-popover-trigger]")===o){a.stopPropagation();return}t.dismiss(void 0,void 0,!1)}}];break}return i.forEach(({eventName:a,callback:c})=>s.addEventListener(a,c)),()=>{i.forEach(({eventName:a,callback:c})=>s.removeEventListener(a,c))}},Hn=(o,e,t)=>{let n=[];switch(e){case"hover":let i;n=[{eventName:"mouseenter",callback:r=>y(null,null,function*(){r.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{G(()=>{t.presentFromTrigger(r),i=void 0})},100)})},{eventName:"mouseleave",callback:r=>{i&&clearTimeout(i);let s=r.relatedTarget;s&&s.closest("ion-popover")!==t&&t.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"context-menu":n=[{eventName:"contextmenu",callback:r=>{r.preventDefault(),t.presentFromTrigger(r)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"click":default:n=[{eventName:"click",callback:r=>t.presentFromTrigger(r)},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break}return n.forEach(({eventName:i,callback:r})=>o.addEventListener(i,r)),o.setAttribute("data-ion-popover-trigger","true"),()=>{n.forEach(({eventName:i,callback:r})=>o.removeEventListener(i,r)),o.removeAttribute("data-ion-popover-trigger")}},dn=(o,e)=>!e||e.tagName!=="ION-ITEM"?-1:o.findIndex(t=>t===e),Zn=(o,e)=>{let t=dn(o,e);return o[t+1]},Wn=(o,e)=>{let t=dn(o,e);return o[t-1]},Re=o=>{let t=z(o).querySelector("button");t&&G(()=>t.focus())},Yn=o=>o.hasAttribute("data-ion-popover-trigger"),Vn=o=>{let e=t=>y(null,null,function*(){var n;let i=document.activeElement,r=[],s=(n=t.target)===null||n===void 0?void 0:n.tagName;if(!(s!=="ION-POPOVER"&&s!=="ION-ITEM")){try{r=Array.from(o.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(t.key){case"ArrowLeft":(yield o.getParentPopover())&&o.dismiss(void 0,void 0,!1);break;case"ArrowDown":t.preventDefault();let c=Zn(r,i);c!==void 0&&Re(c);break;case"ArrowUp":t.preventDefault();let l=Wn(r,i);l!==void 0&&Re(l);break;case"Home":t.preventDefault();let m=r[0];m!==void 0&&Re(m);break;case"End":t.preventDefault();let h=r[r.length-1];h!==void 0&&Re(h);break;case"ArrowRight":case" ":case"Enter":if(i&&Yn(i)){let v=new CustomEvent("ionPopoverActivateTrigger");i.dispatchEvent(v)}break}}});return o.addEventListener("keydown",e),()=>o.removeEventListener("keydown",e)},pn=(o,e,t,n,i,r,s,a,c,l,m)=>{var h;let v={top:0,left:0,width:0,height:0};switch(r){case"event":if(!m)return c;let S=m;v={top:S.clientY,left:S.clientX,width:1,height:1};break;case"trigger":default:let f=m,R=l||((h=f?.detail)===null||h===void 0?void 0:h.ionShadowTarget)||f?.target;if(!R)return c;let B=R.getBoundingClientRect();v={top:B.top,left:B.left,width:B.width,height:B.height};break}let u=Xn(s,v,e,t,n,i,o),I=Kn(a,s,v,e,t),j=u.top+I.top,d=u.left+I.left,{arrowTop:x,arrowLeft:T}=qn(s,n,i,j,d,e,t,o),{originX:D,originY:b}=Gn(s,a,o);return{top:j,left:d,referenceCoordinates:v,arrowTop:x,arrowLeft:T,originX:D,originY:b}},Gn=(o,e,t)=>{switch(o){case"top":return{originX:cn(e),originY:"bottom"};case"bottom":return{originX:cn(e),originY:"top"};case"left":return{originX:"right",originY:Fe(e)};case"right":return{originX:"left",originY:Fe(e)};case"start":return{originX:t?"left":"right",originY:Fe(e)};case"end":return{originX:t?"right":"left",originY:Fe(e)}}},cn=o=>{switch(o){case"start":return"left";case"center":return"center";case"end":return"right"}},Fe=o=>{switch(o){case"start":return"top";case"center":return"center";case"end":return"bottom"}},qn=(o,e,t,n,i,r,s,a)=>{let c={arrowTop:n+s/2-e/2,arrowLeft:i+r-e/2},l={arrowTop:n+s/2-e/2,arrowLeft:i-e*1.5};switch(o){case"top":return{arrowTop:n+s,arrowLeft:i+r/2-e/2};case"bottom":return{arrowTop:n-t,arrowLeft:i+r/2-e/2};case"left":return c;case"right":return l;case"start":return a?l:c;case"end":return a?c:l;default:return{arrowTop:0,arrowLeft:0}}},Xn=(o,e,t,n,i,r,s)=>{let a={top:e.top,left:e.left-t-i},c={top:e.top,left:e.left+e.width+i};switch(o){case"top":return{top:e.top-n-r,left:e.left};case"right":return c;case"bottom":return{top:e.top+e.height+r,left:e.left};case"left":return a;case"start":return s?c:a;case"end":return s?a:c}},Kn=(o,e,t,n,i)=>{switch(o){case"center":return Qn(e,t,n,i);case"end":return Un(e,t,n,i);case"start":default:return{top:0,left:0}}},Un=(o,e,t,n)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(n-e.height),left:0};case"top":case"bottom":default:return{top:0,left:-(t-e.width)}}},Qn=(o,e,t,n)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(n/2-e.height/2),left:0};case"top":case"bottom":default:return{top:0,left:-(t/2-e.width/2)}}},mn=(o,e,t,n,i,r,s,a,c,l,m,h,v=0,u=0,I=0)=>{let j=v,d=u,x=t,T=e,D,b=l,S=m,f=!1,R=!1,B=h?h.top+h.height:r/2-a/2,F=h?h.height:0,L=!1;return x<n+c?(x=n,f=!0,b="left"):s+n+x+c>i&&(R=!0,x=i-s-n,b="right"),B+F+a>r&&(o==="top"||o==="bottom")&&(B-a>0?(T=Math.max(12,B-a-F-(I-1)),j=T+a,S="bottom",L=!0):D=n),{top:T,left:x,bottom:D,originX:b,originY:S,checkSafeAreaLeft:f,checkSafeAreaRight:R,arrowTop:j,arrowLeft:d,addPopoverBottomClass:L}},Jn=(o,e=!1,t,n)=>!(!t&&!n||o!=="top"&&o!=="bottom"&&e),eo=5,to=(o,e)=>{var t;let{event:n,size:i,trigger:r,reference:s,side:a,align:c}=e,l=o.ownerDocument,m=l.dir==="rtl",h=l.defaultView.innerWidth,v=l.defaultView.innerHeight,u=z(o),I=u.querySelector(".popover-content"),j=u.querySelector(".popover-arrow"),d=r||((t=n?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||n?.target,{contentWidth:x,contentHeight:T}=ln(i,I,d),{arrowWidth:D,arrowHeight:b}=$n(j),S={top:v/2-T/2,left:h/2-x/2,originX:m?"right":"left",originY:"top"},f=pn(m,x,T,D,b,s,a,c,S,r,n),R=i==="cover"?0:eo,B=i==="cover"?0:25,{originX:F,originY:L,top:M,left:A,bottom:H,checkSafeAreaLeft:Q,checkSafeAreaRight:X,arrowTop:ze,arrowLeft:Oe,addPopoverBottomClass:Le}=mn(a,f.top,f.left,R,h,v,x,T,B,f.originX,f.originY,f.referenceCoordinates,f.arrowTop,f.arrowLeft,b),Ne=p(),le=p(),J=p();return le.addElement(u.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),J.addElement(u.querySelector(".popover-arrow")).addElement(u.querySelector(".popover-content")).fromTo("opacity",.01,1),Ne.easing("ease").duration(100).beforeAddWrite(()=>{i==="cover"&&o.style.setProperty("--width",`${x}px`),Le&&o.classList.add("popover-bottom"),H!==void 0&&I.style.setProperty("bottom",`${H}px`);let w=" + var(--ion-safe-area-left, 0)",g=" - var(--ion-safe-area-right, 0)",E=`${A}px`;if(Q&&(E=`${A}px${w}`),X&&(E=`${A}px${g}`),I.style.setProperty("top",`calc(${M}px + var(--offset-y, 0))`),I.style.setProperty("left",`calc(${E} + var(--offset-x, 0))`),I.style.setProperty("transform-origin",`${L} ${F}`),j!==null){let C=f.top!==M||f.left!==A;Jn(a,C,n,r)?(j.style.setProperty("top",`calc(${ze}px + var(--offset-y, 0))`),j.style.setProperty("left",`calc(${Oe}px + var(--offset-x, 0))`)):j.style.setProperty("display","none")}}).addAnimation([le,J])},no=o=>{let e=z(o),t=e.querySelector(".popover-content"),n=e.querySelector(".popover-arrow"),i=p(),r=p(),s=p();return r.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(e.querySelector(".popover-arrow")).addElement(e.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin"),n&&(n.style.removeProperty("top"),n.style.removeProperty("left"),n.style.removeProperty("display"))}).duration(300).addAnimation([r,s])},oo=12,io=(o,e)=>{var t;let{event:n,size:i,trigger:r,reference:s,side:a,align:c}=e,l=o.ownerDocument,m=l.dir==="rtl",h=l.defaultView.innerWidth,v=l.defaultView.innerHeight,u=z(o),I=u.querySelector(".popover-content"),j=r||((t=n?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||n?.target,{contentWidth:d,contentHeight:x}=ln(i,I,j),T={top:v/2-x/2,left:h/2-d/2,originX:m?"right":"left",originY:"top"},D=pn(m,d,x,0,0,s,a,c,T,r,n),b=i==="cover"?0:oo,{originX:S,originY:f,top:R,left:B,bottom:F}=mn(a,D.top,D.left,b,h,v,d,x,0,D.originX,D.originY,D.referenceCoordinates),L=p(),M=p(),A=p(),H=p(),Q=p();return M.addElement(u.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),A.addElement(u.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),H.addElement(I).beforeStyles({top:`calc(${R}px + var(--offset-y, 0px))`,left:`calc(${B}px + var(--offset-x, 0px))`,"transform-origin":`${f} ${S}`}).beforeAddWrite(()=>{F!==void 0&&I.style.setProperty("bottom",`${F}px`)}).fromTo("transform","scale(0.8)","scale(1)"),Q.addElement(u.querySelector(".popover-viewport")).fromTo("opacity",.01,1),L.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{i==="cover"&&o.style.setProperty("--width",`${d}px`),f==="bottom"&&o.classList.add("popover-bottom")}).addAnimation([M,A,H,Q])},ro=o=>{let e=z(o),t=e.querySelector(".popover-content"),n=p(),i=p(),r=p();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),r.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),n.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,r])},so=':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',ao=":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}",un=Y(class extends W{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=k(this,"ionPopoverDidPresent",7),this.willPresent=k(this,"ionPopoverWillPresent",7),this.willDismiss=k(this,"ionPopoverWillDismiss",7),this.didDismiss=k(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=k(this,"didPresent",7),this.willPresentShorthand=k(this,"willPresent",7),this.willDismissShorthand=k(this,"willDismiss",7),this.didDismissShorthand=k(this,"didDismiss",7),this.ionMount=k(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=we(),this.lockController=ce(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,Se)},this.onLifecycle=e=>{let t=this.usersElement,n=co[e.type];if(t&&n){let i=new CustomEvent(n,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{let{trigger:e,triggerAction:t,el:n,destroyTriggerInteraction:i}=this;if(i&&i(),e===void 0)return;let r=this.triggerEl=e!==void 0?document.getElementById(e):null;if(!r){U(`[ion-popover] - A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el);return}this.destroyTriggerInteraction=Hn(r,t,n)},this.configureKeyboardInteraction=()=>{let{destroyKeyboardInteraction:e,el:t}=this;e&&e(),this.destroyKeyboardInteraction=Vn(t)},this.configureDismissInteraction=()=>{let{destroyDismissInteraction:e,parentPopover:t,triggerAction:n,triggerEl:i,el:r}=this;!t||!i||(e&&e(),this.destroyDismissInteraction=_n(i,n,r,t))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}connectedCallback(){let{configureTriggerInteraction:e,el:t}=this;Ee(t),e()}disconnectedCallback(){let{destroyTriggerInteraction:e}=this;e&&e()}componentWillLoad(){var e,t;let{el:n}=this,i=(t=(e=this.htmlAttributes)===null||e===void 0?void 0:e.id)!==null&&t!==void 0?t:xe(n);this.parentPopover=n.closest(`ion-popover:not(#${i})`),this.alignment===void 0&&(this.alignment=$(this)==="ios"?"center":"start")}componentDidLoad(){let{parentPopover:e,isOpen:t}=this;t===!0&&G(()=>this.present()),e&&Ft(e,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(e,t=!1){return y(this,null,function*(){this.focusDescendantOnPresent=t,yield this.present(e),this.focusDescendantOnPresent=!1})}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,n=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate;return{inline:n,delegate:i}}present(e){return y(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{el:n}=this,{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield oe(r,n,this.component,["popover-viewport"],this.componentProps,i),this.keyboardEvents||this.configureKeyboardInteraction(),this.configureDismissInteraction(),ne(n)?yield Ce(this.usersElement):this.keepContentsMounted||(yield be()),yield ke(this,"popoverEnter",to,io,{event:e||this.event,size:this.size,trigger:this.triggerEl,reference:this.reference,side:this.side,align:this.alignment}),this.focusDescendantOnPresent&&Lt(n),t()})}dismiss(e,t,n=!0){return y(this,null,function*(){let i=yield this.lockController.lock(),{destroyKeyboardInteraction:r,destroyDismissInteraction:s}=this;n&&this.parentPopover&&this.parentPopover.dismiss(e,t,n);let a=yield je(this,e,t,"popoverLeave",no,ro,this.event);if(a){r&&(r(),this.destroyKeyboardInteraction=void 0),s&&(s(),this.destroyDismissInteraction=void 0);let{delegate:c}=this.getDelegate();yield ie(c,this.usersElement)}return i(),a})}getParentPopover(){return y(this,null,function*(){return this.parentPopover})}onDidDismiss(){return re(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return re(this.el,"ionPopoverWillDismiss")}render(){let e=$(this),{onLifecycle:t,parentPopover:n,dismissOnSelect:i,side:r,arrow:s,htmlAttributes:a,focusTrap:c}=this,l=q("desktop"),m=s&&!n;return P(K,Object.assign({key:"1de4862099cfcb5035e78008e6dc7c1371846f9a","aria-modal":"true","no-router":!0,tabindex:"-1"},a,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},De(this.cssClass)),{[e]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":l,[`popover-side-${r}`]:!0,[se]:c===!1,"popover-nested":!!n}),onIonPopoverDidPresent:t,onIonPopoverWillPresent:t,onIonPopoverWillDismiss:t,onIonPopoverDidDismiss:t,onIonBackdropTap:this.onBackdropTap}),!n&&P("ion-backdrop",{key:"981aa4e0102cb93312ffbd8243cdf2a0cdc60469",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),P("div",{key:"1a28ed55e9d34ef78cf0eb0178643301fd2dd75d",class:"popover-wrapper ion-overlay-wrapper",onClick:i?()=>this.dismiss():void 0},m&&P("div",{key:"1c206ea5eb3c0b5883a3d45c34cd22dd5ffe4b65",class:"popover-arrow",part:"arrow"}),P("div",{key:"5ba561486a328c0c7ab825995fdbfb7a196429a4",class:"popover-content",part:"content"},P("slot",{key:"00fc244ce9dcc2dfc677e6c34b7c8e7a330b2b03"}))))}get el(){return this}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}static get style(){return{ios:so,md:ao}}},[33,"ion-popover",{hasController:[4,"has-controller"],delegate:[16],overlayIndex:[2,"overlay-index"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],component:[1],componentProps:[16,"component-props"],keyboardClose:[4,"keyboard-close"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],event:[8],showBackdrop:[4,"show-backdrop"],translucent:[4],animated:[4],htmlAttributes:[16,"html-attributes"],triggerAction:[1,"trigger-action"],trigger:[1],size:[1],dismissOnSelect:[4,"dismiss-on-select"],reference:[1],side:[1],alignment:[1025],arrow:[4],isOpen:[4,"is-open"],keyboardEvents:[4,"keyboard-events"],focusTrap:[4,"focus-trap"],keepContentsMounted:[4,"keep-contents-mounted"],presented:[32],presentFromTrigger:[64],present:[64],dismiss:[64],getParentPopover:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}]),co={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};function fn(){if(typeof customElements>"u")return;["ion-popover","ion-backdrop"].forEach(e=>{switch(e){case"ion-popover":customElements.get(e)||customElements.define(e,un);break;case"ion-backdrop":customElements.get(e)||Ae();break}})}var hn=fn;var lo="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",po=Y(class extends W{constructor(){super(),this.__registerHost()}componentDidLoad(){At.isBrowser&&uo(()=>y(this,null,function*(){let e=q(window,"hybrid");if(N.getBoolean("_testing")||import("./chunk-QLDSCERF.js").then(i=>i.startTapClick(N)),N.getBoolean("statusTap",e)&&import("./chunk-DYIXN46E.js").then(i=>i.startStatusTap()),N.getBoolean("inputShims",mo())){let i=q(window,"ios")?"ios":"android";import("./chunk-XUDE6CUH.js").then(r=>r.startInputShims(N,i))}let t=yield import("./chunk-FMQDFSTF.js"),n=e||Qe();N.getBoolean("hardwareBackButton",n)?t.startHardwareBackButton():(Qe()&&U("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),t.blockHardwareBackButton()),typeof window<"u"&&import("./chunk-KRZ5TYHG.js").then(i=>i.startKeyboardAssist(window)),import("./chunk-3S45ZQRP.js").then(i=>this.focusVisible=i.startFocusVisible())}))}setFocus(e){return y(this,null,function*(){this.focusVisible&&this.focusVisible.setFocus(e)})}render(){let e=$(this);return P(K,{key:"03aa892f986330078d112b1e8b010df98fa7e39e",class:{[e]:!0,"ion-page":!0,"force-statusbar-padding":N.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return lo}},[0,"ion-app",{setFocus:[64]}]),mo=()=>!!(q(window,"ios")&&q(window,"mobile")||q(window,"android")&&q(window,"mobileweb")),uo=o=>{"requestIdleCallback"in window?window.requestIdleCallback(o):setTimeout(o,32)};function fo(){if(typeof customElements>"u")return;["ion-app"].forEach(e=>{switch(e){case"ion-app":customElements.get(e)||customElements.define(e,po);break}})}var gn=fo;var vo=["outletContent"],vn=["*"];var In=(()=>{let o=class Be extends Wt{parentOutlet;outletContent;constructor(t,n,i,r,s,a,c,l){super(t,n,i,r,s,a,c,l),this.parentOutlet=l}static \u0275fac=function(n){return new(n||Be)(We("name"),We("tabs"),Z(wt),Z(Ye),Z(jt),Z(Ve),Z(xt),Z(Be,12))};static \u0275cmp=me({type:Be,selectors:[["ion-router-outlet"]],viewQuery:function(n,i){if(n&1&&bt(vo,7,ut),n&2){let r;Ct(r=yt())&&(i.outletContent=r.first)}},features:[ft],ngContentSelectors:vn,decls:3,vars:0,consts:[["outletContent",""]],template:function(n,i){n&1&&(Xe(),vt(0,null,0),Ke(2),It())},encapsulation:2})};return o=Ze([Zt({defineCustomElementFn:Kt})],o),o})();var Io=(o,e)=>{let t=o.prototype;e.forEach(n=>{Object.defineProperty(t,n,{get(){return this.el[n]},set(i){this.z.runOutsideAngular(()=>this.el[n]=i)},configurable:!0})})},bo=(o,e)=>{let t=o.prototype;e.forEach(n=>{t[n]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[n].apply(this.el,i))}})};function Co(o){return function(t){let{defineCustomElementFn:n,inputs:i,methods:r}=o;return n!==void 0&&n(),i&&Io(t,i),r&&bo(t,r),t}}var bn=(()=>{let o=class ct{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||ct)(Z(Dt),Z(Ye),Z(Ve))};static \u0275cmp=me({type:ct,selectors:[["ion-app"]],ngContentSelectors:vn,decls:1,vars:0,template:function(n,i){n&1&&(Xe(),Ke(0))},encapsulation:2,changeDetection:0})};return o=Ze([Co({defineCustomElementFn:gn,methods:["setFocus"]})],o),o})();var yo=(()=>{class o extends tt{angularDelegate=ee(Te);injector=ee(Ie);environmentInjector=ee(ve);constructor(){super(Nt),an()}create(t){return super.create(He(_e({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(n){return new(n||o)};static \u0275prov=dt({token:o,factory:o.\u0275fac})}return o})(),lt=class extends tt{angularDelegate=ee(Te);injector=ee(Ie);environmentInjector=ee(ve);constructor(){super($t),hn()}create(e){return super.create(He(_e({},e),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},Cn=(o={})=>pt([{provide:Je,useValue:o},{provide:ht,useFactory:Do,multi:!0,deps:[Je,mt]},Yt(),Te,yo,lt]),Do=(o,e)=>()=>{e.documentElement.classList.add("ion-ce"),Ot(o)};var yn=[{path:"home",loadComponent:()=>import("./chunk-ID23W3YJ.js").then(o=>o.HomePage)},{path:"game",loadComponent:()=>import("./chunk-FSLYLEM7.js").then(o=>o.GamePage)},{path:"",redirectTo:"home",pathMatch:"full"}];var Dn=(()=>{let e=class e{constructor(){}};e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=me({type:e,selectors:[["app-root"]],decls:2,vars:0,template:function(i,r){i&1&&(Ge(0,"ion-app"),gt(1,"ion-router-outlet"),qe())},dependencies:[bn,In],encapsulation:2});let o=e;return o})();Et(Dn,{providers:[{provide:kt,useClass:et},Cn(),Tt(yn,Mt(St))]});
