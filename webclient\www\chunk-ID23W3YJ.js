import{a as D,b as z,c as W,e as A,f as L,g as q,h as B,i as U,j as K,k as Y,l as Z,m as Q,n as X,o as $,p as ee,q as ne,s as te,t as ie,u as oe,v as re,w as ae}from"./chunk-GYCWT3F4.js";import"./chunk-NWICGN3P.js";import"./chunk-QUJFQN2Y.js";import"./chunk-33ZKSDCD.js";import"./chunk-VX6CYJTR.js";import"./chunk-XC7QA3KA.js";import"./chunk-XOTTEB5B.js";import"./chunk-WDNHH3UA.js";import"./chunk-WKAFHTGB.js";import"./chunk-55XXYEDJ.js";import"./chunk-PZCS62C4.js";import"./chunk-OAHYJD5P.js";import"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-E2SD7K6D.js";import"./chunk-CKP3SGE2.js";import"./chunk-7IRWVP2E.js";import{D as c,E as e,F as t,G as h,K as b,L as _,La as j,M as x,Ma as V,Na as H,Oa as J,Pa as R,Sa as G,T as C,Ta as k,U as i,Ua as F,V as w,Y as S,Z as v,_ as P,aa as E,ea as O,ga as N,j as p,k as u,ka as T,r as s,s as M,u as I,y as f}from"./chunk-FXSFTU3P.js";import"./chunk-EX6SW4ET.js";import"./chunk-BIG7K77F.js";import"./chunk-SM5Y6Q5Y.js";import"./chunk-PFDCGU6V.js";import"./chunk-B3E62VDJ.js";import"./chunk-M2X7KQLB.js";import"./chunk-XTVTS2NW.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import{f as y}from"./chunk-S5EYVFES.js";var ce=()=>["OK"];function le(a,l){a&1&&(e(0,"div",25),i(1," Room code must be 4 letters "),t())}function me(a,l){a&1&&(e(0,"div",25),i(1," Name is required (2-20 characters) "),t())}function de(a,l){a&1&&h(0,"ion-spinner",26)}function ge(a,l){a&1&&(e(0,"span"),i(1,"Join Game"),t())}function pe(a,l){a&1&&(e(0,"span"),i(1,"Joining..."),t())}function ue(a,l){if(a&1){let se=b();e(0,"ion-button",27),_("click",function(){p(se);let r=x();return u(r.reconnectToGame())}),h(1,"ion-icon",28),i(2," Reconnect to Previous Game "),t()}}var Pe=(()=>{let l=class l{constructor(o,r){this.gameService=o,this.router=r,this.roomCode="",this.playerName="",this.isJoining=!1,this.connectionStatus=!1,this.errorMessage=null,this.canReconnect=!1,this.subscriptions=[]}ngOnInit(){let o=localStorage.getItem("jugshine_player_name");o&&(this.playerName=o);let r=localStorage.getItem("jugshine_room_code");this.canReconnect=!!(o&&r),this.subscriptions.push(this.gameService.connectionStatus$.subscribe(n=>{this.connectionStatus=n})),this.subscriptions.push(this.gameService.error$.subscribe(n=>{this.errorMessage=n})),this.subscriptions.push(this.gameService.game$.subscribe(n=>{n&&this.router.navigate(["/game"])}))}ngOnDestroy(){this.subscriptions.forEach(o=>o.unsubscribe())}formatRoomCode(o){let r=o.target.value.toUpperCase().replace(/[^A-Z]/g,"");r.length>4&&(r=r.substring(0,4)),this.roomCode=r,o.target.value=r}joinGame(){return y(this,null,function*(){if(!(!this.roomCode||!this.playerName||this.isJoining)){if(this.roomCode.length!==4){this.errorMessage="Room code must be exactly 4 letters";return}if(this.playerName.length<2||this.playerName.length>20){this.errorMessage="Name must be between 2 and 20 characters";return}this.isJoining=!0,this.errorMessage=null;try{(yield this.gameService.joinGame(this.roomCode.toUpperCase(),this.playerName.trim()))&&console.log("Successfully joined game")}catch(o){console.error("Error joining game:",o),this.errorMessage="Failed to join game. Please try again."}finally{this.isJoining=!1}}})}reconnectToGame(){return y(this,null,function*(){this.isJoining=!0,this.errorMessage=null;try{(yield this.gameService.reconnectToGame())?console.log("Successfully reconnected to game"):(this.canReconnect=!1,setTimeout(()=>{this.gameService.clearError()},5e3))}catch(o){console.error("Error reconnecting:",o),this.errorMessage="Failed to reconnect. Please join manually.",this.canReconnect=!1}finally{this.isJoining=!1}})}clearError(){this.errorMessage=null}};l.\u0275fac=function(r){return new(r||l)(M(ae),M(T))},l.\u0275cmp=I({type:l,selectors:[["app-home"]],decls:69,vars:18,consts:[["joinForm","ngForm"],["roomCodeInput","ngModel"],["playerNameInput","ngModel"],[3,"translucent"],["color","primary"],[1,"ion-padding",3,"fullscreen"],[1,"welcome-container"],[1,"logo-section"],[1,"connection-chip",3,"color"],[3,"name"],["header","Error",3,"didDismiss","isOpen","message","buttons"],[1,"join-card"],[3,"ngSubmit"],["position","stacked"],["name","roomCode","placeholder","ABCD","maxlength","4","required","",1,"room-code-input",3,"ngModelChange","ionInput","ngModel"],["class","validation-error",4,"ngIf"],["name","playerName","placeholder","Enter your name","maxlength","20","required","",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"join-button",3,"disabled"],["name","crescent",4,"ngIf"],[4,"ngIf"],["fill","outline","expand","block","class","reconnect-button",3,"click",4,"ngIf"],[1,"info-card"],["name","people","slot","start","color","primary"],["name","person","slot","start","color","secondary"],["name","game-controller","slot","start","color","tertiary"],[1,"validation-error"],["name","crescent"],["fill","outline","expand","block",1,"reconnect-button",3,"click"],["name","refresh","slot","start"]],template:function(r,n){if(r&1){let d=b();e(0,"ion-header",3)(1,"ion-toolbar",4)(2,"ion-title"),i(3," Jugshine "),t()()(),e(4,"ion-content",5)(5,"div",6)(6,"div",7)(7,"h1"),i(8,"\u{1F3AE} JUGSHINE"),t(),e(9,"p"),i(10,"Join the party game fun!"),t()(),e(11,"ion-chip",8),h(12,"ion-icon",9),e(13,"ion-label"),i(14),t()(),e(15,"ion-alert",10),_("didDismiss",function(){return p(d),u(n.clearError())}),t(),e(16,"ion-card",11)(17,"ion-card-header")(18,"ion-card-title"),i(19,"Join Game"),t(),e(20,"ion-card-subtitle"),i(21,"Enter your room code and name"),t()(),e(22,"ion-card-content")(23,"form",12,0),_("ngSubmit",function(){return p(d),u(n.joinGame())}),e(25,"ion-item")(26,"ion-label",13),i(27,"Room Code"),t(),e(28,"ion-input",14,1),P("ngModelChange",function(m){return p(d),v(n.roomCode,m)||(n.roomCode=m),u(m)}),_("ionInput",function(m){return p(d),u(n.formatRoomCode(m))}),t()(),f(30,le,2,0,"div",15),e(31,"ion-item")(32,"ion-label",13),i(33,"Your Name"),t(),e(34,"ion-input",16,2),P("ngModelChange",function(m){return p(d),v(n.playerName,m)||(n.playerName=m),u(m)}),t()(),f(36,me,2,0,"div",15),e(37,"ion-button",17),f(38,de,1,0,"ion-spinner",18)(39,ge,2,0,"span",19)(40,pe,2,0,"span",19),t()()()(),f(41,ue,3,0,"ion-button",20),e(42,"ion-card",21)(43,"ion-card-header")(44,"ion-card-title"),i(45,"How to Play"),t()(),e(46,"ion-card-content")(47,"ion-list")(48,"ion-item"),h(49,"ion-icon",22),e(50,"ion-label")(51,"h3"),i(52,"Get the room code from the game host"),t(),e(53,"p"),i(54,"The host will share a 4-letter code"),t()()(),e(55,"ion-item"),h(56,"ion-icon",23),e(57,"ion-label")(58,"h3"),i(59,"Enter your name"),t(),e(60,"p"),i(61,"Choose a fun name for the game"),t()()(),e(62,"ion-item"),h(63,"ion-icon",24),e(64,"ion-label")(65,"h3"),i(66,"Play and have fun!"),t(),e(67,"p"),i(68,"Follow the game instructions on your phone"),t()()()()()()()()}if(r&2){let d=C(24),g=C(29),m=C(35);c("translucent",!0),s(4),c("fullscreen",!0),s(7),c("color",n.connectionStatus?"success":"danger"),s(),c("name",n.connectionStatus?"wifi":"wifi-off"),s(2),w(n.connectionStatus?"Connected":"Disconnected"),s(),c("isOpen",!!n.errorMessage)("message",n.errorMessage)("buttons",E(17,ce)),s(13),S("ngModel",n.roomCode),s(2),c("ngIf",g.invalid&&g.touched),s(4),S("ngModel",n.playerName),s(2),c("ngIf",m.invalid&&m.touched),s(),c("disabled",!d.form.valid||n.isJoining),s(),c("ngIf",n.isJoining),s(),c("ngIf",!n.isJoining),s(),c("ngIf",n.isJoining),s(),c("ngIf",n.canReconnect)}},dependencies:[N,O,F,R,j,V,G,k,J,H,re,z,W,A,L,q,B,U,K,Y,Z,Q,X,$,ee,ne,te,ie,oe,D],styles:[".welcome-container[_ngcontent-%COMP%]{max-width:500px;margin:0 auto;padding:20px}.logo-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:30px}.logo-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;color:var(--ion-color-primary);margin-bottom:10px}.logo-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;color:var(--ion-color-medium);margin:0}.connection-chip[_ngcontent-%COMP%]{display:block;margin:0 auto 20px;width:fit-content}.join-card[_ngcontent-%COMP%]{margin-bottom:20px}.join-card[_ngcontent-%COMP%]   .room-code-input[_ngcontent-%COMP%]{text-transform:uppercase;font-weight:700;font-size:1.2rem;text-align:center}.join-card[_ngcontent-%COMP%]   .join-button[_ngcontent-%COMP%]{margin-top:20px;height:50px;font-weight:700}.validation-error[_ngcontent-%COMP%]{color:var(--ion-color-danger);font-size:.8rem;margin-top:5px;margin-left:16px}.reconnect-button[_ngcontent-%COMP%]{margin-bottom:20px}.info-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}.info-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:16px}.info-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-weight:600}.info-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.9rem}@media (max-width: 768px){.welcome-container[_ngcontent-%COMP%]{padding:10px}.logo-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}}@media (prefers-color-scheme: dark){.join-card[_ngcontent-%COMP%], .info-card[_ngcontent-%COMP%]{--background: var(--ion-color-step-50)}}"]});let a=l;return a})();export{Pe as HomePage};
