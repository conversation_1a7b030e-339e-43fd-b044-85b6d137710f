@echo off
title Restart Jugshine Server
color 0C

echo.
echo ========================================
echo    RESTART JUGSHINE SERVER
echo ========================================
echo.

:: Kill existing Node.js processes (server)
echo [INFO] Stopping existing server processes...
taskkill /f /im node.exe >nul 2>&1
echo [INFO] Server processes stopped

:: Wait a moment
timeout /t 2 /nobreak >nul

:: Check if MongoDB is still running
echo [INFO] Checking MongoDB status...
tasklist /fi "imagename eq mongod.exe" 2>nul | find /i "mongod.exe" >nul
if errorlevel 1 (
    echo [WARNING] MongoDB is not running!
    echo [INFO] Starting MongoDB...
    if not exist "mongodb-data" mkdir mongodb-data
    start "MongoDB Server" cmd /k "mongod --dbpath mongodb-data --port 27017"
    timeout /t 3 /nobreak >nul
) else (
    echo [INFO] MongoDB is already running
)

:: Start the server
echo [INFO] Starting Jugshine server...
start "Jugshine Server" cmd /k "cd /d %CD%\server && npm run dev"

echo.
echo [SUCCESS] Server restarted!
echo.
echo Services:
echo   MongoDB:    mongodb://localhost:27017/jugshine
echo   Server:     http://localhost:3000
echo   Health:     http://localhost:3000/health
echo.
echo [INFO] Press any key to exit.
pause >nul
