# MongoDB Setup for Jugshine

This guide helps you set up MongoDB for the Jugshine game server.

## Quick Start

### Option 1: Automatic Setup (Recommended)
```bash
start-server-mongodb.bat
```
This script will:
- Automatically detect existing MongoDB installations
- Start MongoDB and the Jugshine server
- Handle all configuration

### Option 2: Install MongoDB First
If you get "MongoDB not found" error:
```bash
install-mongodb.bat
```
Then run:
```bash
start-server-mongodb.bat
```

## Troubleshooting

### "MongoDB not found in PATH"
This means MongoDB is installed but not accessible. Try:
```bash
fix-mongodb-path.bat
```
(Must run as Administrator)

### MongoDB Installation Locations
The scripts check these locations automatically:
- `C:\Program Files\MongoDB\Server\*\bin\mongod.exe`
- `C:\Program Files (x86)\MongoDB\Server\*\bin\mongod.exe`
- Windows Service: `MongoDB`
- System PATH

### Manual Installation
1. Download MongoDB Community Server: https://www.mongodb.com/try/download/community
2. Choose Windows MSI package
3. Install as Windows Service
4. Run `start-server-mongodb.bat`

### Package Manager Installation
**Chocolatey:**
```bash
choco install mongodb
```

**Scoop:**
```bash
scoop install mongodb
```

## Available Scripts

| Script | Purpose |
|--------|---------|
| `start-server-mongodb.bat` | Start MongoDB + Jugshine server |
| `install-mongodb.bat` | Install MongoDB automatically |
| `fix-mongodb-path.bat` | Add MongoDB to PATH |
| `restart-server.bat` | Restart server only |
| `test-mongodb.bat` | Test MongoDB connection |

## Data Storage

- **MongoDB Data:** `./mongodb-data/` directory
- **Database Name:** `jugshine`
- **Connection:** `mongodb://localhost:27017/jugshine`

## Verification

Check if everything is working:
1. Open: http://localhost:3000/health
2. Should show: `"database": {"status": "connected"}`

## Common Issues

**Issue:** "MongoDB connection failed"
**Solution:** Ensure MongoDB is running:
```bash
test-mongodb.bat
```

**Issue:** "Port 27017 already in use"
**Solution:** Another MongoDB instance is running. Stop it or use the existing one.

**Issue:** "Access denied" when starting MongoDB
**Solution:** Run as Administrator or check folder permissions.

## Production Notes

- The server now requires MongoDB (no in-memory fallback)
- All game data persists between restarts
- MongoDB service can be configured to start automatically with Windows
