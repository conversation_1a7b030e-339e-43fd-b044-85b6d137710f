list=[{
"base": &"Control",
"class": &"BaseMinigame",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/BaseMinigame.gd"
}, {
"base": &"Node",
"class": &"EngineIO",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/godot-socketio/engineio.gd"
}, {
"base": &"HTTPRequest",
"class": &"Request",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/godot-socketio/request.gd"
}, {
"base": &"EngineIO",
"class": &"SocketIO",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/godot-socketio/socketio.gd"
}]
