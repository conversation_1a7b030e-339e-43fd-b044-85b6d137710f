[res://scripts/ServerConfig.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 12,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 14,
"scroll_position": 14.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/PoisoningPigeons.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 196,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/WebSocket.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 2,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 42,
"scroll_position": 33.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/LoadingScreen.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 16,
"scroll_position": 6.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://addons/godot-socketio/socketio.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 69,
"scroll_position": 60.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://addons/godot-socketio/request.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 41,
"scroll_position": 24.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
